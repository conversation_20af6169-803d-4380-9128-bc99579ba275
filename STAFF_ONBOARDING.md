# Ocean Soul Sparkles Admin Portal - Staff Onboarding Guide

## 🎯 Welcome to the Ocean Soul Sparkles Admin Portal

This guide will help you get started with the new admin portal system for managing bookings, customers, and business operations.

## 🔐 Access & Authentication

### Initial Access
1. **Admin Portal URL**: https://admin.oceansoulsparkles.com.au
2. **Your account will be created by a DEV or Admin user**
3. **You will receive login credentials via secure email**
4. **Multi-Factor Authentication (MFA) is required for all users**

### First Login Process
1. Navigate to https://admin.oceansoulsparkles.com.au
2. Enter your email and temporary password
3. Set up MFA using your smartphone authenticator app
4. Create a new secure password
5. Complete your profile information

### Role-Based Access
- **DEV**: Full system access (development team only)
- **Admin**: Complete business management access
- **Artist**: Service delivery and customer management
- **Braider**: Hair braiding services and scheduling
- **User**: Basic customer service functions

## 📱 Dashboard Overview

### Main Navigation
- **Dashboard**: Overview of daily operations
- **Bookings**: Manage all customer bookings
- **Customers**: Customer database and communications
- **Services**: Service management and pricing
- **Calendar**: Schedule and availability management
- **Reports**: Business analytics and insights

### Quick Actions
- Create new booking
- Search customers
- View today's schedule
- Process payments
- Send notifications

## 📅 Booking Management

### Creating a New Booking
1. Click "New Booking" from dashboard
2. Search or create customer profile
3. Select service(s) and duration
4. Choose date and time
5. Assign artist/braider
6. Process payment or set payment terms
7. Send confirmation to customer

### Managing Existing Bookings
- **View**: Click on any booking for details
- **Edit**: Modify time, services, or assignments
- **Cancel**: Process cancellations with refund options
- **Reschedule**: Move to different date/time
- **Add Notes**: Document special requirements

### Artist/Braider Assignment
- Automatic assignment based on availability
- Manual override for specific requests
- Skill-based matching for specialized services
- Workload balancing across team

## 👥 Customer Management

### Customer Profiles
- Complete contact information
- Service history and preferences
- Communication logs
- Payment history
- Special notes and requirements

### Communication Tools
- Send SMS notifications
- Email confirmations and reminders
- In-app messaging system
- Automated follow-up sequences

## 💰 Payment Processing

### Accepted Payment Methods
- Square Card Reader (in-person)
- Manual card entry
- Cash payments with change calculation
- Apple Pay and digital wallets
- Gift certificates

### Processing Payments
1. Select payment method
2. Enter amount (auto-calculated from services)
3. Process transaction
4. Print or email receipt
5. Update booking status

### Refunds and Adjustments
- Partial and full refunds
- Service adjustments
- Discount applications
- Gift certificate issuance

## 📊 Reporting and Analytics

### Available Reports
- Daily/weekly/monthly revenue
- Service popularity analysis
- Artist/braider performance
- Customer retention metrics
- Booking trends and patterns

### Accessing Reports
1. Navigate to Reports section
2. Select report type and date range
3. Apply filters as needed
4. Export to PDF or Excel
5. Schedule automated reports

## 🔧 Settings and Preferences

### Personal Settings
- Update your profile information
- Change password and MFA settings
- Set notification preferences
- Customize dashboard layout

### Business Settings (Admin only)
- Service pricing and availability
- Staff schedules and permissions
- Payment processing configuration
- Automated messaging templates

## 🆘 Support and Troubleshooting

### Common Issues
- **Login Problems**: Check email/password, ensure MFA is working
- **Payment Failures**: Verify card details, check internet connection
- **Booking Conflicts**: Use calendar view to resolve scheduling issues
- **Customer Data**: Always verify information before saving

### Getting Help
- **In-app Help**: Click the "?" icon for contextual help
- **Admin Support**: Contact your Admin or DEV team member
- **Emergency Issues**: Use the emergency contact procedures
- **Training Resources**: Access video tutorials in the Help section

## 📋 Daily Procedures

### Opening Checklist
1. Log into admin portal
2. Review today's bookings
3. Check for customer messages
4. Verify payment processing is working
5. Review any overnight notifications

### During Service Delivery
1. Check in customers as they arrive
2. Update booking status in real-time
3. Process payments immediately after service
4. Take photos for portfolio (with permission)
5. Send follow-up messages

### Closing Checklist
1. Complete all booking records
2. Process any pending payments
3. Backup important data
4. Review tomorrow's schedule
5. Log out securely

## 🔒 Security Best Practices

### Password Security
- Use strong, unique passwords
- Enable MFA on all accounts
- Never share login credentials
- Log out when finished

### Data Protection
- Only access customer data when necessary
- Never share customer information
- Use secure networks only
- Report any security concerns immediately

### Device Security
- Keep devices locked when not in use
- Use only approved devices for admin access
- Install security updates promptly
- Report lost or stolen devices immediately

## 📞 Emergency Procedures

### System Outages
1. Check internet connection
2. Try refreshing the browser
3. Contact IT support immediately
4. Use backup paper systems if needed
5. Document all manual transactions

### Payment Processing Issues
1. Verify card reader connection
2. Try alternative payment methods
3. Document cash transactions carefully
4. Contact payment support if needed
5. Never store card information manually

### Customer Emergencies
1. Follow standard first aid procedures
2. Contact emergency services if needed
3. Notify management immediately
4. Document incident thoroughly
5. Follow up with customer care

## 📚 Training Resources

### Video Tutorials
- Basic navigation and dashboard use
- Booking creation and management
- Payment processing procedures
- Customer communication tools
- Reporting and analytics

### Practice Environment
- Use the training mode to practice
- Test scenarios without affecting real data
- Familiarize yourself with all features
- Complete certification modules

### Ongoing Education
- Monthly team training sessions
- New feature announcements
- Best practice sharing
- Performance feedback and coaching

---

## 📞 Contact Information

**Technical Support**: <EMAIL>
**Business Operations**: <EMAIL>
**Emergency Contact**: [To be provided during onboarding]

**Admin Portal**: https://admin.oceansoulsparkles.com.au
**Public Website**: https://www.oceansoulsparkles.com.au

---

*This guide is updated regularly. Please check for the latest version monthly.*
