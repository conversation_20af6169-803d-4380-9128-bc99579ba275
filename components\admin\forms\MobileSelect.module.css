/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Select Styles
 * Touch-friendly select styling optimized for mobile devices
 */

.container {
  position: relative;
  width: 100%;
  margin-bottom: 1rem;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: color 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
}

.required {
  color: #ef4444;
  margin-left: 0.25rem;
}

.selectWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ffffff;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  padding: 1rem;
  min-height: 44px;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.selectWrapper:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.selectedValue {
  flex: 1;
  font-size: 1rem;
  color: #111827;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.selectWrapper:not(.hasValue) .selectedValue {
  color: #9ca3af;
}

.actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.5rem;
}

.clearButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  -webkit-tap-highlight-color: transparent;
}

.clearButton:hover {
  background: #f3f4f6;
  color: #374151;
}

.clearButton:active {
  background: #e5e7eb;
}

.chevron {
  color: #6b7280;
  font-size: 0.875rem;
  transition: transform 0.2s ease;
  pointer-events: none;
}

.open .chevron {
  transform: rotate(180deg);
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 50;
  background: #ffffff;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  margin-top: 0.25rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  animation: slideDown 0.2s ease;
}

.searchWrapper {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.searchInput {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  outline: none;
  transition: border-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;
}

.searchInput:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.optionsList {
  max-height: inherit;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.groupLabel {
  padding: 0.75rem 1rem 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  font-size: 1rem;
  color: #111827;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
  min-height: 48px;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.option:last-child {
  border-bottom: none;
}

.option:hover,
.option.focused {
  background: #f3f4f6;
}

.option:active {
  background: #e5e7eb;
}

.option.selected {
  background: #eff6ff;
  color: #3b82f6;
  font-weight: 500;
}

.option.optionDisabled {
  color: #9ca3af;
  cursor: not-allowed;
  background: #f9fafb;
}

.option.optionDisabled:hover {
  background: #f9fafb;
}

.checkmark {
  color: #3b82f6;
  font-weight: bold;
  font-size: 1.125rem;
}

.noOptions {
  padding: 2rem 1rem;
  text-align: center;
  color: #9ca3af;
  font-style: italic;
}

/* Error state */
.error .selectWrapper {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error .label {
  color: #ef4444;
}

/* Success state */
.success .selectWrapper {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.success .label {
  color: #10b981;
}

/* Disabled state */
.disabled .selectWrapper {
  background: #f9fafb;
  border-color: #e5e7eb;
  cursor: not-allowed;
}

.disabled .selectedValue {
  color: #9ca3af;
}

.disabled .label {
  color: #9ca3af;
  cursor: not-allowed;
}

.disabled .chevron {
  color: #d1d5db;
}

/* Error message */
.errorMessage {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #ef4444;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  animation: slideDown 0.2s ease;
}

.errorMessage::before {
  content: '⚠️';
  font-size: 0.75rem;
}

/* Help text */
.helpText {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .container {
    margin-bottom: 1.25rem;
  }
  
  .selectWrapper {
    padding: 1.25rem 1rem;
    min-height: 48px;
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .selectedValue {
    font-size: 16px;
  }
  
  .label {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }
  
  .dropdown {
    margin-top: 0.5rem;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  .option {
    padding: 1.25rem 1rem;
    min-height: 52px;
    font-size: 16px;
  }
  
  .searchInput {
    padding: 1rem;
    font-size: 16px;
    border-radius: 12px;
  }
  
  .errorMessage,
  .helpText {
    font-size: 1rem;
    margin-top: 0.75rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .selectWrapper {
    padding: 1.5rem 1rem;
    min-height: 52px;
  }
  
  .option {
    padding: 1.5rem 1rem;
    min-height: 56px;
  }
  
  .searchInput {
    padding: 1.25rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .selectWrapper {
    border-width: 3px;
  }
  
  .selectWrapper:focus {
    border-color: #000000;
  }
  
  .error .selectWrapper {
    border-color: #cc0000;
  }
  
  .success .selectWrapper {
    border-color: #006600;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .selectWrapper,
  .label,
  .chevron,
  .clearButton,
  .option {
    transition: none;
  }
  
  .dropdown,
  .errorMessage {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .label {
    color: #e5e7eb;
  }
  
  .selectWrapper {
    background: #1f2937;
    border-color: #4b5563;
  }
  
  .selectedValue {
    color: #f9fafb;
  }
  
  .dropdown {
    background: #1f2937;
    border-color: #4b5563;
  }
  
  .option {
    color: #f9fafb;
    border-color: #374151;
  }
  
  .option:hover,
  .option.focused {
    background: #374151;
  }
  
  .option.selected {
    background: #1e40af;
    color: #93c5fd;
  }
  
  .groupLabel {
    background: #111827;
    color: #9ca3af;
    border-color: #374151;
  }
  
  .searchInput {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .helpText {
    color: #9ca3af;
  }
  
  .disabled .selectWrapper {
    background: #111827;
    border-color: #374151;
  }
}
