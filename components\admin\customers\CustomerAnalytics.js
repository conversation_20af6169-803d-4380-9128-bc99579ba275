import { useMemo } from 'react';
import styles from '../../../styles/admin/CustomerAnalytics.module.css';

export default function CustomerAnalytics({ customers }) {
  const analytics = useMemo(() => {
    if (!customers || customers.length === 0) {
      return {
        totalCustomers: 0,
        newThisMonth: 0,
        activeCustomers: 0,
        averageBookings: 0,
        topCustomers: [],
        growthData: []
      };
    }

    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    // Basic metrics
    const totalCustomers = customers.length;
    const newThisMonth = customers.filter(c => 
      new Date(c.created_at) >= thisMonth
    ).length;
    const activeCustomers = customers.filter(c => 
      (c.total_bookings || 0) > 0
    ).length;
    const totalBookings = customers.reduce((sum, c) => sum + (c.total_bookings || 0), 0);
    const averageBookings = totalCustomers > 0 ? (totalBookings / totalCustomers).toFixed(1) : 0;

    // Top customers by bookings
    const topCustomers = customers
      .filter(c => (c.total_bookings || 0) > 0)
      .sort((a, b) => (b.total_bookings || 0) - (a.total_bookings || 0))
      .slice(0, 5)
      .map(c => ({
        name: `${c.first_name || ''} ${c.last_name || ''}`.trim(),
        bookings: c.total_bookings || 0,
        email: c.email
      }));

    // Growth data for last 6 months
    const growthData = [];
    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
      
      const monthCustomers = customers.filter(c => {
        const createdDate = new Date(c.created_at);
        return createdDate >= monthDate && createdDate < nextMonth;
      }).length;

      growthData.push({
        month: monthDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        customers: monthCustomers
      });
    }

    return {
      totalCustomers,
      newThisMonth,
      activeCustomers,
      averageBookings,
      topCustomers,
      growthData
    };
  }, [customers]);

  return (
    <div className={styles.analyticsContainer}>
      <div className={styles.analyticsHeader}>
        <h2>Customer Analytics</h2>
        <p>Overview of your customer base and growth trends</p>
      </div>

      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.totalCustomers}</div>
          <div className={styles.metricLabel}>Total Customers</div>
          <div className={styles.metricIcon}>👥</div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.newThisMonth}</div>
          <div className={styles.metricLabel}>New This Month</div>
          <div className={styles.metricIcon}>✨</div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.activeCustomers}</div>
          <div className={styles.metricLabel}>Active Customers</div>
          <div className={styles.metricIcon}>🎯</div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.averageBookings}</div>
          <div className={styles.metricLabel}>Avg. Bookings per Customer</div>
          <div className={styles.metricIcon}>📊</div>
        </div>
      </div>

      <div className={styles.chartsGrid}>
        <div className={styles.chartCard}>
          <h3>Customer Growth (Last 6 Months)</h3>
          <div className={styles.barChart}>
            {analytics.growthData.map((data, index) => {
              const maxValue = Math.max(...analytics.growthData.map(d => d.customers));
              const height = maxValue > 0 ? (data.customers / maxValue) * 100 : 0;
              
              return (
                <div key={index} className={styles.barContainer}>
                  <div 
                    className={styles.bar}
                    style={{ height: `${height}%` }}
                    title={`${data.customers} customers`}
                  ></div>
                  <div className={styles.barLabel}>{data.month}</div>
                  <div className={styles.barValue}>{data.customers}</div>
                </div>
              );
            })}
          </div>
        </div>

        <div className={styles.chartCard}>
          <h3>Top Customers by Bookings</h3>
          {analytics.topCustomers.length > 0 ? (
            <div className={styles.topCustomersList}>
              {analytics.topCustomers.map((customer, index) => (
                <div key={index} className={styles.topCustomerItem}>
                  <div className={styles.customerRank}>#{index + 1}</div>
                  <div className={styles.customerInfo}>
                    <div className={styles.customerName}>{customer.name}</div>
                    <div className={styles.customerEmail}>{customer.email}</div>
                  </div>
                  <div className={styles.customerBookings}>
                    <span className={styles.bookingCount}>{customer.bookings}</span>
                    <span className={styles.bookingLabel}>bookings</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.emptyState}>
              <p>No booking data available yet</p>
            </div>
          )}
        </div>
      </div>

      <div className={styles.insightsGrid}>
        <div className={styles.insightCard}>
          <h4>Customer Engagement</h4>
          <div className={styles.insightContent}>
            <div className={styles.engagementStat}>
              <span className={styles.percentage}>
                {analytics.totalCustomers > 0 
                  ? Math.round((analytics.activeCustomers / analytics.totalCustomers) * 100)
                  : 0
                }%
              </span>
              <span className={styles.engagementLabel}>of customers have made bookings</span>
            </div>
          </div>
        </div>

        <div className={styles.insightCard}>
          <h4>Growth Trend</h4>
          <div className={styles.insightContent}>
            <div className={styles.trendStat}>
              {analytics.growthData.length >= 2 && (
                <>
                  <span className={styles.trendValue}>
                    {analytics.growthData[analytics.growthData.length - 1].customers - 
                     analytics.growthData[analytics.growthData.length - 2].customers > 0 ? '+' : ''}
                    {analytics.growthData[analytics.growthData.length - 1].customers - 
                     analytics.growthData[analytics.growthData.length - 2].customers}
                  </span>
                  <span className={styles.trendLabel}>customers vs last month</span>
                </>
              )}
            </div>
          </div>
        </div>

        <div className={styles.insightCard}>
          <h4>Customer Retention</h4>
          <div className={styles.insightContent}>
            <div className={styles.retentionStat}>
              <span className={styles.retentionValue}>
                {analytics.topCustomers.length > 0 
                  ? analytics.topCustomers[0].bookings 
                  : 0
                }
              </span>
              <span className={styles.retentionLabel}>max bookings by single customer</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
