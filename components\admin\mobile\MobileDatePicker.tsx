/**
 * Ocean Soul Sparkles Admin - Mobile Date/Time Picker Component
 * Touch-friendly date and time selection optimized for mobile devices
 */

import React, { useState, useRef, useEffect } from 'react';
import { HapticFeedback } from '../../../lib/gestures/swipe-handler';
import styles from '../../../styles/admin/mobile/MobileDatePicker.module.css';

interface MobileDatePickerProps {
  value?: Date;
  onChange: (date: Date) => void;
  mode?: 'date' | 'time' | 'datetime';
  minDate?: Date;
  maxDate?: Date;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  label?: string;
  required?: boolean;
}

export default function MobileDatePicker({
  value,
  onChange,
  mode = 'date',
  minDate,
  maxDate,
  placeholder,
  disabled = false,
  className = '',
  label,
  required = false
}: MobileDatePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(value || new Date());
  const [currentView, setCurrentView] = useState<'date' | 'time'>('date');
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (value) {
      setSelectedDate(value);
    }
  }, [value]);

  const handleInputClick = () => {
    if (disabled) return;
    
    HapticFeedback.light();
    
    // Check if device supports native date/time input
    if (supportsNativeInput()) {
      // Use native input on mobile devices
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.click();
      }
    } else {
      // Use custom picker for desktop or unsupported devices
      setIsOpen(true);
      setCurrentView(mode === 'time' ? 'time' : 'date');
    }
  };

  const handleNativeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = new Date(e.target.value);
    if (!isNaN(newDate.getTime())) {
      setSelectedDate(newDate);
      onChange(newDate);
      HapticFeedback.light();
    }
  };

  const handleCustomDateChange = (date: Date) => {
    setSelectedDate(date);
    HapticFeedback.light();
    
    if (mode === 'date') {
      onChange(date);
      setIsOpen(false);
    } else if (mode === 'datetime') {
      setCurrentView('time');
    }
  };

  const handleCustomTimeChange = (time: { hours: number; minutes: number }) => {
    const newDate = new Date(selectedDate);
    newDate.setHours(time.hours, time.minutes, 0, 0);
    setSelectedDate(newDate);
    onChange(newDate);
    setIsOpen(false);
    HapticFeedback.medium();
  };

  const supportsNativeInput = (): boolean => {
    // Check if we're on a mobile device and the browser supports native inputs
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const input = document.createElement('input');
    input.type = mode === 'time' ? 'time' : mode === 'datetime' ? 'datetime-local' : 'date';
    return isMobile && input.type !== 'text';
  };

  const formatDisplayValue = (): string => {
    if (!value) return placeholder || '';
    
    const options: Intl.DateTimeFormatOptions = {};
    
    if (mode === 'date') {
      options.year = 'numeric';
      options.month = 'short';
      options.day = 'numeric';
    } else if (mode === 'time') {
      options.hour = '2-digit';
      options.minute = '2-digit';
    } else {
      options.year = 'numeric';
      options.month = 'short';
      options.day = 'numeric';
      options.hour = '2-digit';
      options.minute = '2-digit';
    }
    
    return value.toLocaleDateString('en-AU', options);
  };

  const getInputType = (): string => {
    switch (mode) {
      case 'time':
        return 'time';
      case 'datetime':
        return 'datetime-local';
      default:
        return 'date';
    }
  };

  const getInputValue = (): string => {
    if (!value) return '';
    
    if (mode === 'time') {
      return value.toTimeString().slice(0, 5);
    } else if (mode === 'datetime') {
      return value.toISOString().slice(0, 16);
    } else {
      return value.toISOString().slice(0, 10);
    }
  };

  return (
    <div className={`${styles.datePickerContainer} ${className}`}>
      {label && (
        <label className={styles.label}>
          {label}
          {required && <span className={styles.required}>*</span>}
        </label>
      )}
      
      {/* Native Input (hidden, used for mobile) */}
      <input
        ref={inputRef}
        type={getInputType()}
        value={getInputValue()}
        onChange={handleNativeChange}
        min={minDate?.toISOString().slice(0, mode === 'time' ? 5 : mode === 'datetime' ? 16 : 10)}
        max={maxDate?.toISOString().slice(0, mode === 'time' ? 5 : mode === 'datetime' ? 16 : 10)}
        disabled={disabled}
        className={styles.nativeInput}
        aria-label={label || placeholder}
      />
      
      {/* Custom Display Input */}
      <div
        className={`${styles.displayInput} ${disabled ? styles.disabled : ''} ${
          isOpen ? styles.focused : ''
        }`}
        onClick={handleInputClick}
      >
        <span className={styles.displayValue}>
          {formatDisplayValue() || placeholder}
        </span>
        <span className={styles.icon}>
          {mode === 'time' ? '🕐' : '📅'}
        </span>
      </div>

      {/* Custom Picker Modal */}
      {isOpen && !supportsNativeInput() && (
        <CustomDateTimePicker
          selectedDate={selectedDate}
          currentView={currentView}
          mode={mode}
          minDate={minDate}
          maxDate={maxDate}
          onDateChange={handleCustomDateChange}
          onTimeChange={handleCustomTimeChange}
          onClose={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}

/**
 * Custom Date/Time Picker Component for desktop fallback
 */
interface CustomDateTimePickerProps {
  selectedDate: Date;
  currentView: 'date' | 'time';
  mode: 'date' | 'time' | 'datetime';
  minDate?: Date;
  maxDate?: Date;
  onDateChange: (date: Date) => void;
  onTimeChange: (time: { hours: number; minutes: number }) => void;
  onClose: () => void;
}

function CustomDateTimePicker({
  selectedDate,
  currentView,
  mode,
  minDate,
  maxDate,
  onDateChange,
  onTimeChange,
  onClose
}: CustomDateTimePickerProps) {
  const [viewDate, setViewDate] = useState(new Date(selectedDate));
  const [selectedTime, setSelectedTime] = useState({
    hours: selectedDate.getHours(),
    minutes: selectedDate.getMinutes()
  });

  const handleDateSelect = (day: number) => {
    const newDate = new Date(viewDate.getFullYear(), viewDate.getMonth(), day);
    onDateChange(newDate);
  };

  const handleTimeConfirm = () => {
    onTimeChange(selectedTime);
  };

  const navigateMonth = (direction: 1 | -1) => {
    const newDate = new Date(viewDate);
    newDate.setMonth(newDate.getMonth() + direction);
    setViewDate(newDate);
    HapticFeedback.light();
  };

  const getDaysInMonth = () => {
    const year = viewDate.getFullYear();
    const month = viewDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }
    
    return days;
  };

  const isDateDisabled = (day: number): boolean => {
    const date = new Date(viewDate.getFullYear(), viewDate.getMonth(), day);
    
    if (minDate && date < minDate) return true;
    if (maxDate && date > maxDate) return true;
    
    return false;
  };

  return (
    <div className={styles.customPickerOverlay} onClick={onClose}>
      <div className={styles.customPicker} onClick={(e) => e.stopPropagation()}>
        {currentView === 'date' && (
          <div className={styles.datePicker}>
            {/* Month Navigation */}
            <div className={styles.monthHeader}>
              <button
                className={styles.navButton}
                onClick={() => navigateMonth(-1)}
              >
                ‹
              </button>
              <h3 className={styles.monthTitle}>
                {viewDate.toLocaleDateString('en-AU', { month: 'long', year: 'numeric' })}
              </h3>
              <button
                className={styles.navButton}
                onClick={() => navigateMonth(1)}
              >
                ›
              </button>
            </div>

            {/* Days of Week */}
            <div className={styles.daysHeader}>
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className={styles.dayHeader}>{day}</div>
              ))}
            </div>

            {/* Calendar Grid */}
            <div className={styles.daysGrid}>
              {getDaysInMonth().map((day, index) => (
                <button
                  key={index}
                  className={`${styles.dayButton} ${
                    day === selectedDate.getDate() &&
                    viewDate.getMonth() === selectedDate.getMonth() &&
                    viewDate.getFullYear() === selectedDate.getFullYear()
                      ? styles.selected
                      : ''
                  } ${day && isDateDisabled(day) ? styles.disabled : ''}`}
                  onClick={() => day && !isDateDisabled(day) && handleDateSelect(day)}
                  disabled={!day || isDateDisabled(day)}
                >
                  {day}
                </button>
              ))}
            </div>
          </div>
        )}

        {currentView === 'time' && (
          <div className={styles.timePicker}>
            <div className={styles.timeInputs}>
              <div className={styles.timeInput}>
                <label>Hours</label>
                <input
                  type="number"
                  min="0"
                  max="23"
                  value={selectedTime.hours}
                  onChange={(e) => setSelectedTime(prev => ({
                    ...prev,
                    hours: parseInt(e.target.value) || 0
                  }))}
                />
              </div>
              <div className={styles.timeSeparator}>:</div>
              <div className={styles.timeInput}>
                <label>Minutes</label>
                <input
                  type="number"
                  min="0"
                  max="59"
                  value={selectedTime.minutes}
                  onChange={(e) => setSelectedTime(prev => ({
                    ...prev,
                    minutes: parseInt(e.target.value) || 0
                  }))}
                />
              </div>
            </div>
            
            <button
              className={styles.confirmButton}
              onClick={handleTimeConfirm}
            >
              Confirm
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
