-- Create receipt templates table for Ocean Soul Sparkles
-- This script creates the receipt_templates table with all necessary fields

CREATE TABLE IF NOT EXISTS receipt_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template_type VARCHAR(50) NOT NULL DEFAULT 'standard',
  is_default BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  
  -- Business Information
  business_name VARCHAR(200) DEFAULT 'Ocean Soul Sparkles',
  business_address TEXT,
  business_phone VARCHAR(50),
  business_email VARCHAR(100),
  business_website VARCHAR(100),
  business_abn VARCHAR(50),
  
  -- Layout Settings
  show_logo BOOLEAN DEFAULT true,
  logo_position VARCHAR(20) DEFAULT 'center',
  header_color VARCHAR(7) DEFAULT '#667eea',
  text_color VARCHAR(7) DEFAULT '#333333',
  font_family VARCHAR(50) DEFAULT 'Arial',
  font_size INTEGER DEFAULT 12,
  
  -- Content Settings
  show_customer_details BOOLEAN DEFAULT true,
  show_service_details BOOLEAN DEFAULT true,
  show_artist_details BOOLEAN DEFAULT true,
  show_payment_details BOOLEAN DEFAULT true,
  show_booking_notes BOOLEAN DEFAULT false,
  show_terms_conditions BOOLEAN DEFAULT true,
  
  -- Footer Settings
  footer_message TEXT DEFAULT 'Thank you for choosing Ocean Soul Sparkles!',
  show_social_media BOOLEAN DEFAULT false,
  social_media_links JSONB,
  custom_fields JSONB DEFAULT '[]',
  
  -- Metadata
  created_by UUID,
  updated_by UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default receipt templates
INSERT INTO receipt_templates (
  name, description, template_type, is_default, is_active,
  business_name, business_address, business_phone, business_email, business_website,
  show_logo, logo_position, header_color, text_color, font_family, font_size,
  show_customer_details, show_service_details, show_artist_details, show_payment_details,
  show_booking_notes, show_terms_conditions, footer_message, show_social_media
) VALUES 
(
  'Standard Receipt', 
  'Default receipt template with all standard information',
  'standard', 
  true, 
  true,
  'Ocean Soul Sparkles',
  'Australia',
  '+61 XXX XXX XXX',
  '<EMAIL>',
  'oceansoulsparkles.com.au',
  true,
  'center',
  '#667eea',
  '#333333',
  'Arial',
  12,
  true,
  true,
  true,
  true,
  false,
  true,
  'Thank you for choosing Ocean Soul Sparkles! We hope you love your new look!',
  false
),
(
  'Compact Receipt', 
  'Minimal receipt template for quick transactions',
  'compact', 
  false, 
  true,
  'Ocean Soul Sparkles',
  'Australia',
  '+61 XXX XXX XXX',
  '<EMAIL>',
  'oceansoulsparkles.com.au',
  false,
  'left',
  '#667eea',
  '#333333',
  'Arial',
  10,
  true,
  true,
  false,
  true,
  false,
  false,
  'Thank you!',
  false
),
(
  'Detailed Receipt', 
  'Comprehensive receipt template with all available information',
  'detailed', 
  false, 
  true,
  'Ocean Soul Sparkles',
  'Australia',
  '+61 XXX XXX XXX',
  '<EMAIL>',
  'oceansoulsparkles.com.au',
  true,
  'center',
  '#667eea',
  '#333333',
  'Arial',
  12,
  true,
  true,
  true,
  true,
  true,
  true,
  'Thank you for choosing Ocean Soul Sparkles! Follow us on social media for inspiration and updates.',
  true
)
ON CONFLICT (name) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_receipt_templates_active ON receipt_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_receipt_templates_default ON receipt_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_receipt_templates_type ON receipt_templates(template_type);

-- Success message
SELECT 'Receipt templates table created successfully!' as message;
