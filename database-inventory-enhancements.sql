-- Ocean Soul Sparkles - Inventory Management Enhancements
-- Database Schema Updates for Supplier Management and Purchase Orders

-- 1. SUPPLIERS TABLE
CREATE TABLE IF NOT EXISTS suppliers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  contact_person VARCHAR(100),
  email VARCHAR(255),
  phone VARCHAR(20),
  address TEXT,
  website VARCHAR(255),
  payment_terms VARCHAR(100) DEFAULT 'Net 30',
  lead_time_days INTEGER DEFAULT 7,
  minimum_order_amount DECIMAL(10,2) DEFAULT 0.00,
  is_active BOOLEAN DEFAULT true,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. PURCHASE ORDERS TABLE
CREATE TABLE IF NOT EXISTS purchase_orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  po_number VARCHAR(50) UNIQUE NOT NULL,
  supplier_id UUID REFERENCES suppliers(id) NOT NULL,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'confirmed', 'received', 'cancelled')),
  order_date DATE DEFAULT CURRENT_DATE,
  expected_delivery_date DATE,
  actual_delivery_date DATE,
  subtotal DECIMAL(10,2) DEFAULT 0.00,
  tax_amount DECIMAL(10,2) DEFAULT 0.00,
  total_amount DECIMAL(10,2) DEFAULT 0.00,
  notes TEXT,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. PURCHASE ORDER ITEMS TABLE
CREATE TABLE IF NOT EXISTS purchase_order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  purchase_order_id UUID REFERENCES purchase_orders(id) ON DELETE CASCADE,
  inventory_id UUID REFERENCES inventory(id),
  product_name VARCHAR(200) NOT NULL, -- Store name in case inventory item is deleted
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  unit_cost DECIMAL(10,2) NOT NULL CHECK (unit_cost >= 0),
  total_cost DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED,
  received_quantity INTEGER DEFAULT 0 CHECK (received_quantity >= 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. INVENTORY ALERTS TABLE
CREATE TABLE IF NOT EXISTS inventory_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  inventory_id UUID REFERENCES inventory(id) ON DELETE CASCADE,
  alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('low_stock', 'out_of_stock', 'overstock')),
  threshold_value INTEGER NOT NULL,
  current_value INTEGER NOT NULL,
  is_active BOOLEAN DEFAULT true,
  is_resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolved_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Add supplier_id to existing inventory table (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'inventory' AND column_name = 'supplier_id'
  ) THEN
    ALTER TABLE inventory ADD COLUMN supplier_id UUID REFERENCES suppliers(id);
  END IF;
END $$;

-- 6. Add reorder_point and reorder_quantity to inventory table (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'inventory' AND column_name = 'reorder_point'
  ) THEN
    ALTER TABLE inventory ADD COLUMN reorder_point INTEGER DEFAULT 0;
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'inventory' AND column_name = 'reorder_quantity'
  ) THEN
    ALTER TABLE inventory ADD COLUMN reorder_quantity INTEGER DEFAULT 0;
  END IF;
END $$;

-- 7. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name);
CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_status ON purchase_orders(status);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_date ON purchase_orders(order_date);
CREATE INDEX IF NOT EXISTS idx_purchase_order_items_po ON purchase_order_items(purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_inventory ON inventory_alerts(inventory_id);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_active ON inventory_alerts(is_active);
CREATE INDEX IF NOT EXISTS idx_inventory_supplier ON inventory(supplier_id);

-- 8. Enable Row Level Security
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_alerts ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies for admin access
CREATE POLICY "Admin users can manage suppliers" ON suppliers
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE admin_users.id = auth.uid() 
      AND admin_users.role IN ('DEV', 'Admin')
    )
  );

CREATE POLICY "Admin users can manage purchase orders" ON purchase_orders
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE admin_users.id = auth.uid() 
      AND admin_users.role IN ('DEV', 'Admin')
    )
  );

CREATE POLICY "Admin users can manage purchase order items" ON purchase_order_items
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE admin_users.id = auth.uid() 
      AND admin_users.role IN ('DEV', 'Admin')
    )
  );

CREATE POLICY "Admin users can view inventory alerts" ON inventory_alerts
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE admin_users.id = auth.uid() 
      AND admin_users.role IN ('DEV', 'Admin', 'Artist', 'Braider')
    )
  );

-- 10. Insert sample suppliers data
INSERT INTO suppliers (name, contact_person, email, phone, address, payment_terms, lead_time_days, minimum_order_amount, notes) VALUES
('Hair Plus Wholesale', 'Sarah Johnson', '<EMAIL>', '+61-2-9876-5432', '123 Beauty Supply St, Sydney NSW 2000', 'Net 30', 5, 200.00, 'Primary supplier for braiding hair and extensions'),
('Beauty Supply Co', 'Michael Chen', '<EMAIL>', '+61-3-8765-4321', '456 Wholesale Ave, Melbourne VIC 3000', 'Net 15', 3, 150.00, 'Quick delivery for urgent orders'),
('Professional Hair Products', 'Emma Wilson', '<EMAIL>', '+61-7-7654-3210', '789 Trade Blvd, Brisbane QLD 4000', 'Net 45', 7, 300.00, 'Premium quality products, higher minimum order'),
('Aussie Beauty Distributors', 'David Thompson', '<EMAIL>', '+61-8-6543-2109', '321 Distribution Way, Perth WA 6000', 'Net 30', 10, 250.00, 'Wide range of beauty and hair care products')
ON CONFLICT DO NOTHING;

-- 11. Create function to generate PO numbers
CREATE OR REPLACE FUNCTION generate_po_number()
RETURNS TEXT AS $$
DECLARE
  next_number INTEGER;
  po_number TEXT;
BEGIN
  -- Get the next number in sequence
  SELECT COALESCE(MAX(CAST(SUBSTRING(po_number FROM 'PO-(\d+)') AS INTEGER)), 0) + 1
  INTO next_number
  FROM purchase_orders
  WHERE po_number ~ '^PO-\d+$';
  
  -- Format as PO-YYYYMMDD-XXX
  po_number := 'PO-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || LPAD(next_number::TEXT, 3, '0');
  
  RETURN po_number;
END;
$$ LANGUAGE plpgsql;

-- 12. Create function to check low stock and create alerts
CREATE OR REPLACE FUNCTION check_low_stock_alerts()
RETURNS INTEGER AS $$
DECLARE
  alert_count INTEGER := 0;
  inventory_item RECORD;
BEGIN
  -- Check for low stock items
  FOR inventory_item IN 
    SELECT id, name, quantity_on_hand, min_stock_level, reorder_point
    FROM inventory 
    WHERE is_active = true 
    AND (quantity_on_hand <= min_stock_level OR quantity_on_hand <= reorder_point)
  LOOP
    -- Check if alert already exists and is active
    IF NOT EXISTS (
      SELECT 1 FROM inventory_alerts 
      WHERE inventory_id = inventory_item.id 
      AND alert_type = 'low_stock' 
      AND is_active = true 
      AND is_resolved = false
    ) THEN
      -- Create new alert
      INSERT INTO inventory_alerts (
        inventory_id, 
        alert_type, 
        threshold_value, 
        current_value
      ) VALUES (
        inventory_item.id,
        'low_stock',
        GREATEST(inventory_item.min_stock_level, inventory_item.reorder_point),
        inventory_item.quantity_on_hand
      );
      
      alert_count := alert_count + 1;
    END IF;
  END LOOP;
  
  RETURN alert_count;
END;
$$ LANGUAGE plpgsql;

-- 13. Create function to update inventory from purchase orders
CREATE OR REPLACE FUNCTION update_inventory_from_po(po_id UUID, item_id UUID, received_qty INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  inventory_item_id UUID;
  success BOOLEAN := false;
BEGIN
  -- Get the inventory item ID from the purchase order item
  SELECT inventory_id INTO inventory_item_id
  FROM purchase_order_items
  WHERE id = item_id AND purchase_order_id = po_id;
  
  IF inventory_item_id IS NOT NULL THEN
    -- Update inventory quantity
    UPDATE inventory 
    SET quantity_on_hand = quantity_on_hand + received_qty,
        updated_at = NOW()
    WHERE id = inventory_item_id;
    
    -- Update received quantity in purchase order item
    UPDATE purchase_order_items
    SET received_quantity = received_quantity + received_qty
    WHERE id = item_id;
    
    success := true;
  END IF;
  
  RETURN success;
END;
$$ LANGUAGE plpgsql;
