import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { toast } from 'react-toastify';
import styles from '../../styles/admin/Suppliers.module.css';

interface Supplier {
  id: string;
  name: string;
  contact_person: string | null;
  email: string | null;
  phone: string | null;
  address: string | null;
  website: string | null;
  payment_terms: string;
  lead_time_days: number;
  minimum_order_amount: number;
  is_active: boolean;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

interface SupplierManagementProps {
  initialSuppliers?: Supplier[];
}

export default function SupplierManagement({ initialSuppliers = [] }: SupplierManagementProps) {
  const [suppliers, setSuppliers] = useState<Supplier[]>(initialSuppliers);
  const [filteredSuppliers, setFilteredSuppliers] = useState<Supplier[]>(initialSuppliers);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Filter and sort suppliers
  useEffect(() => {
    let filtered = [...suppliers];

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(supplier =>
        supplier.name.toLowerCase().includes(search) ||
        supplier.contact_person?.toLowerCase().includes(search) ||
        supplier.email?.toLowerCase().includes(search)
      );
    }

    // Apply active filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(supplier => 
        activeFilter === 'active' ? supplier.is_active : !supplier.is_active
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Supplier];
      let bValue: any = b[sortBy as keyof Supplier];

      // Handle null values
      if (aValue === null) aValue = '';
      if (bValue === null) bValue = '';

      // Convert to string for comparison
      aValue = String(aValue).toLowerCase();
      bValue = String(bValue).toLowerCase();

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredSuppliers(filtered);
  }, [suppliers, searchTerm, activeFilter, sortBy, sortOrder]);

  const fetchSuppliers = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/suppliers', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch suppliers');
      }

      const data = await response.json();
      setSuppliers(data.suppliers || []);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      toast.error('Failed to load suppliers');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSupplier = async (supplierId: string, supplierName: string) => {
    if (!confirm(`Are you sure you want to delete supplier "${supplierName}"? This will mark them as inactive.`)) {
      return;
    }

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/suppliers/${supplierId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete supplier');
      }

      toast.success('Supplier deleted successfully');
      fetchSuppliers(); // Refresh the list
    } catch (error) {
      console.error('Error deleting supplier:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete supplier');
    }
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU');
  };

  return (
    <div className={styles.supplierManagement}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h1 className={styles.title}>Supplier Management</h1>
          <p className={styles.subtitle}>Manage your suppliers and vendor relationships</p>
        </div>
        <div className={styles.headerActions}>
          <Link href="/admin/suppliers/new" className={styles.addBtn}>
            + Add Supplier
          </Link>
        </div>
      </div>

      {/* Controls */}
      <div className={styles.controls}>
        <div className={styles.searchSection}>
          <input
            type="text"
            placeholder="Search suppliers by name, contact, or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.filters}>
          <select
            value={activeFilter}
            onChange={(e) => setActiveFilter(e.target.value)}
            className={styles.filterSelect}
          >
            <option value="all">All Suppliers</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </select>

          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order as 'asc' | 'desc');
            }}
            className={styles.sortSelect}
          >
            <option value="name-asc">Name (A-Z)</option>
            <option value="name-desc">Name (Z-A)</option>
            <option value="created_at-desc">Newest First</option>
            <option value="created_at-asc">Oldest First</option>
            <option value="lead_time_days-asc">Lead Time (Low-High)</option>
            <option value="lead_time_days-desc">Lead Time (High-Low)</option>
          </select>
        </div>
      </div>

      {/* Suppliers List */}
      <div className={styles.suppliersContainer}>
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading suppliers...</p>
          </div>
        ) : filteredSuppliers.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>📦</div>
            <h3>No suppliers found</h3>
            <p>
              {searchTerm || activeFilter !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Get started by adding your first supplier'
              }
            </p>
            {!searchTerm && activeFilter === 'all' && (
              <Link href="/admin/suppliers/new" className={styles.addFirstBtn}>
                Add First Supplier
              </Link>
            )}
          </div>
        ) : (
          <div className={styles.suppliersGrid}>
            {filteredSuppliers.map((supplier) => (
              <div key={supplier.id} className={styles.supplierCard}>
                <div className={styles.cardHeader}>
                  <div className={styles.supplierInfo}>
                    <h3 className={styles.supplierName}>{supplier.name}</h3>
                    <span 
                      className={`${styles.statusBadge} ${supplier.is_active ? styles.active : styles.inactive}`}
                    >
                      {supplier.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                <div className={styles.cardBody}>
                  {supplier.contact_person && (
                    <div className={styles.contactInfo}>
                      <span className={styles.label}>Contact:</span>
                      <span className={styles.value}>{supplier.contact_person}</span>
                    </div>
                  )}
                  
                  {supplier.email && (
                    <div className={styles.contactInfo}>
                      <span className={styles.label}>Email:</span>
                      <span className={styles.value}>{supplier.email}</span>
                    </div>
                  )}
                  
                  {supplier.phone && (
                    <div className={styles.contactInfo}>
                      <span className={styles.label}>Phone:</span>
                      <span className={styles.value}>{supplier.phone}</span>
                    </div>
                  )}

                  <div className={styles.businessInfo}>
                    <div className={styles.infoItem}>
                      <span className={styles.label}>Payment Terms:</span>
                      <span className={styles.value}>{supplier.payment_terms}</span>
                    </div>
                    
                    <div className={styles.infoItem}>
                      <span className={styles.label}>Lead Time:</span>
                      <span className={styles.value}>{supplier.lead_time_days} days</span>
                    </div>
                    
                    <div className={styles.infoItem}>
                      <span className={styles.label}>Min Order:</span>
                      <span className={styles.value}>{formatCurrency(supplier.minimum_order_amount)}</span>
                    </div>
                  </div>

                  {supplier.notes && (
                    <div className={styles.notes}>
                      <span className={styles.label}>Notes:</span>
                      <p className={styles.notesText}>{supplier.notes}</p>
                    </div>
                  )}
                </div>

                <div className={styles.cardFooter}>
                  <div className={styles.cardActions}>
                    <Link 
                      href={`/admin/suppliers/${supplier.id}`} 
                      className={styles.viewBtn}
                    >
                      View Details
                    </Link>
                    <Link 
                      href={`/admin/suppliers/${supplier.id}/edit`} 
                      className={styles.editBtn}
                    >
                      Edit
                    </Link>
                    <button
                      onClick={() => handleDeleteSupplier(supplier.id, supplier.name)}
                      className={styles.deleteBtn}
                      disabled={!supplier.is_active}
                    >
                      Delete
                    </button>
                  </div>
                  
                  <div className={styles.cardMeta}>
                    <span className={styles.createdDate}>
                      Added {formatDate(supplier.created_at)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
