import Link from 'next/link';
import styles from '../../styles/admin/QuickActions.module.css';

interface QuickActionsProps {
  userRole: 'DEV' | 'Admin' | 'Artist' | 'Braider';
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  href: string;
  color: string;
  roles: string[];
}

const QUICK_ACTIONS: QuickAction[] = [
  {
    id: 'new-booking',
    title: 'New Booking',
    description: 'Create a new customer booking',
    icon: '📅',
    href: '/admin/bookings/new',
    color: '#3788d8',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'new-customer',
    title: 'Add Customer',
    description: 'Register a new customer',
    icon: '👤',
    href: '/admin/customers/new',
    color: '#28a745',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'new-service',
    title: 'Add Service',
    description: 'Create a new service offering',
    icon: '✨',
    href: '/admin/services/new',
    color: '#4ECDC4',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'new-product',
    title: 'Add Product',
    description: 'Add a new product to inventory',
    icon: '🛍️',
    href: '/admin/products/new',
    color: '#fd7e14',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'view-calendar',
    title: 'Calendar',
    description: 'View booking calendar',
    icon: '📆',
    href: '/admin/calendar',
    color: '#6f42c1',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'reports',
    title: 'Reports',
    description: 'View business reports',
    icon: '📊',
    href: '/admin/reports',
    color: '#e83e8c',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'inventory',
    title: 'Inventory',
    description: 'Manage product inventory',
    icon: '📦',
    href: '/admin/inventory',
    color: '#20c997',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'settings',
    title: 'Settings',
    description: 'Configure system settings',
    icon: '⚙️',
    href: '/admin/settings',
    color: '#6c757d',
    roles: ['DEV', 'Admin']
  }
];

export default function QuickActions({ userRole }: QuickActionsProps) {
  const hasAccess = (roles: string[]) => {
    return roles.includes(userRole);
  };

  const filteredActions = QUICK_ACTIONS.filter(action => hasAccess(action.roles));

  return (
    <div className={styles.quickActionsContainer}>
      <div className={styles.header}>
        <h2 className={styles.sectionTitle}>Quick Actions</h2>
        <p className={styles.sectionSubtitle}>Common tasks and shortcuts</p>
      </div>

      <div className={styles.actionsGrid}>
        {filteredActions.map((action) => (
          <Link 
            key={action.id}
            href={action.href}
            className={styles.actionCard}
            style={{ '--action-color': action.color } as React.CSSProperties}
          >
            <div className={styles.actionIcon}>
              {action.icon}
            </div>
            <div className={styles.actionContent}>
              <h3 className={styles.actionTitle}>{action.title}</h3>
              <p className={styles.actionDescription}>{action.description}</p>
            </div>
            <div className={styles.actionArrow}>→</div>
          </Link>
        ))}
      </div>

      {/* Role-specific shortcuts */}
      {(userRole === 'Artist' || userRole === 'Braider') && (
        <div className={styles.roleSection}>
          <h3 className={styles.roleSectionTitle}>Your Tools</h3>
          <div className={styles.roleActions}>
            <Link href="/admin/my-bookings" className={styles.roleAction}>
              <span className={styles.roleActionIcon}>📋</span>
              <span>My Bookings</span>
            </Link>
            <Link href="/admin/my-schedule" className={styles.roleAction}>
              <span className={styles.roleActionIcon}>🗓️</span>
              <span>My Schedule</span>
            </Link>
            <Link href="/admin/my-earnings" className={styles.roleAction}>
              <span className={styles.roleActionIcon}>💰</span>
              <span>My Earnings</span>
            </Link>
            <Link href="/admin/my-profile" className={styles.roleAction}>
              <span className={styles.roleActionIcon}>👨‍🎨</span>
              <span>My Profile</span>
            </Link>
          </div>
        </div>
      )}

      {/* Admin shortcuts */}
      {(userRole === 'DEV' || userRole === 'Admin') && (
        <div className={styles.adminSection}>
          <h3 className={styles.adminSectionTitle}>Admin Tools</h3>
          <div className={styles.adminActions}>
            <button className={styles.adminAction}>
              <span className={styles.adminActionIcon}>🔄</span>
              <span>Sync Data</span>
            </button>
            <button className={styles.adminAction}>
              <span className={styles.adminActionIcon}>📤</span>
              <span>Export Data</span>
            </button>
            <button className={styles.adminAction}>
              <span className={styles.adminActionIcon}>🔧</span>
              <span>System Check</span>
            </button>
            <button className={styles.adminAction}>
              <span className={styles.adminActionIcon}>📊</span>
              <span>Analytics</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
