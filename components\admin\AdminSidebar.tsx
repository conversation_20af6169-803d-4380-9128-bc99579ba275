import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '../../styles/admin/AdminSidebar.module.css';

interface AdminSidebarProps {
  user: {
    id: string;
    email: string;
    role: 'DEV' | 'Admin' | 'Artist' | 'Braider';
    firstName: string;
    lastName: string;
  };
  collapsed: boolean;
  onToggle: () => void;
  isMobile: boolean;
}

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  href: string;
  roles: string[];
  children?: MenuItem[];
}

const MENU_ITEMS: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: '📊',
    href: '/admin/dashboard',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'bookings',
    label: 'Bookings',
    icon: '📅',
    href: '/admin/bookings',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'customers',
    label: 'Customers',
    icon: '👥',
    href: '/admin/customers',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'services',
    label: 'Services',
    icon: '✨',
    href: '/admin/services',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'products',
    label: 'Products',
    icon: '🛍️',
    href: '/admin/products',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'suppliers',
    label: 'Suppliers',
    icon: '📦',
    href: '/admin/suppliers',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'pos',
    label: 'POS Terminal',
    icon: '💳',
    href: '/admin/pos',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'purchase-orders',
    label: 'Purchase Orders',
    icon: '📋',
    href: '/admin/purchase-orders',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'staff',
    label: 'Staff Management',
    icon: '👨‍💼',
    href: '/admin/staff',
    roles: ['DEV', 'Admin'],
    children: [
      {
        id: 'staff-overview',
        label: 'Staff Overview',
        icon: '👥',
        href: '/admin/staff',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'staff-onboarding',
        label: 'Onboarding',
        icon: '📋',
        href: '/admin/staff/onboarding',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'staff-training',
        label: 'Training',
        icon: '🎓',
        href: '/admin/staff/training',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'staff-performance',
        label: 'Performance',
        icon: '📊',
        href: '/admin/staff/performance',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'staff-schedule',
        label: 'Schedule Management',
        icon: '🗓️',
        href: '/admin/staff/schedule',
        roles: ['DEV', 'Admin']
      }
    ]
  },
  {
    id: 'artists',
    label: 'Artists',
    icon: '🎨',
    href: '/admin/artists',
    roles: ['DEV', 'Admin'],
    children: [
      {
        id: 'artists-overview',
        label: 'Artists Overview',
        icon: '👨‍🎨',
        href: '/admin/artists',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'artists-portfolio',
        label: 'Portfolio Management',
        icon: '🖼️',
        href: '/admin/artists/portfolio',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'artists-schedule',
        label: 'Artist Scheduling',
        icon: '📅',
        href: '/admin/artists/schedule',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'artists-commissions',
        label: 'Commission Tracking',
        icon: '💰',
        href: '/admin/artists/commissions',
        roles: ['DEV', 'Admin']
      }
    ]
  },
  {
    id: 'tips',
    label: 'Tip Management',
    icon: '💰',
    href: '/admin/tips',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'receipts',
    label: 'Receipts',
    icon: '🧾',
    href: '/admin/receipts',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: '📈',
    href: '/admin/reports',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'communications',
    label: 'Communications',
    icon: '📧',
    href: '/admin/communications',
    roles: ['DEV', 'Admin'],
    children: [
      {
        id: 'email-templates',
        label: 'Email Templates',
        icon: '📝',
        href: '/admin/email-templates',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'sms-templates',
        label: 'SMS Templates',
        icon: '📱',
        href: '/admin/sms-templates',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'communications-log',
        label: 'Communications Log',
        icon: '📋',
        href: '/admin/communications',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'feedback',
        label: 'Customer Feedback',
        icon: '⭐',
        href: '/admin/feedback',
        roles: ['DEV', 'Admin']
      }
    ]
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: '🔔',
    href: '/admin/notifications',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: '⚙️',
    href: '/admin/settings',
    roles: ['DEV', 'Admin']
  }
];

export default function AdminSidebar({ user, collapsed, onToggle, isMobile }: AdminSidebarProps) {
  const router = useRouter();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const hasAccess = (roles: string[]) => {
    return roles.includes(user.role);
  };

  const isActive = (href: string) => {
    return router.pathname === href || router.pathname.startsWith(href + '/');
  };

  const filteredMenuItems = MENU_ITEMS.filter(item => hasAccess(item.roles));

  return (
    <aside className={`${styles.sidebar} ${collapsed ? styles.collapsed : ''} ${isMobile ? styles.mobile : ''}`}>
      {/* Sidebar Header */}
      <div className={styles.sidebarHeader}>
        <div className={styles.logo}>
          {!collapsed && (
            <>
              <div className={styles.logoIcon}>🌊</div>
              <div className={styles.logoText}>
                <div className={styles.logoTitle}>Ocean Soul</div>
                <div className={styles.logoSubtitle}>Admin</div>
              </div>
            </>
          )}
          {collapsed && (
            <div className={styles.logoIconOnly}>🌊</div>
          )}
        </div>
        
        {!isMobile && (
          <button 
            className={styles.toggleButton}
            onClick={onToggle}
            title={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {collapsed ? '→' : '←'}
          </button>
        )}
      </div>

      {/* User Info */}
      <div className={styles.userInfo}>
        <div className={styles.userAvatar}>
          {user.firstName.charAt(0)}{user.lastName.charAt(0)}
        </div>
        {!collapsed && (
          <div className={styles.userDetails}>
            <div className={styles.userName}>
              {user.firstName} {user.lastName}
            </div>
            <div className={styles.userRole}>
              {user.role}
            </div>
          </div>
        )}
      </div>

      {/* Navigation Menu */}
      <nav className={styles.navigation}>
        <ul className={styles.menuList}>
          {filteredMenuItems.map((item) => (
            <li key={item.id} className={styles.menuItem}>
              <Link 
                href={item.href}
                className={`${styles.menuLink} ${isActive(item.href) ? styles.active : ''}`}
                title={collapsed ? item.label : undefined}
              >
                <span className={styles.menuIcon}>{item.icon}</span>
                {!collapsed && (
                  <span className={styles.menuLabel}>{item.label}</span>
                )}
                {!collapsed && item.children && (
                  <button
                    className={styles.expandButton}
                    onClick={(e) => {
                      e.preventDefault();
                      toggleExpanded(item.id);
                    }}
                  >
                    {expandedItems.includes(item.id) ? '▼' : '▶'}
                  </button>
                )}
              </Link>
              
              {!collapsed && item.children && expandedItems.includes(item.id) && (
                <ul className={styles.submenu}>
                  {item.children.filter(child => hasAccess(child.roles)).map((child) => (
                    <li key={child.id} className={styles.submenuItem}>
                      <Link 
                        href={child.href}
                        className={`${styles.submenuLink} ${isActive(child.href) ? styles.active : ''}`}
                      >
                        <span className={styles.submenuIcon}>{child.icon}</span>
                        <span className={styles.submenuLabel}>{child.label}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </nav>

      {/* Sidebar Footer */}
      <div className={styles.sidebarFooter}>
        {!collapsed && (
          <div className={styles.footerContent}>
            <div className={styles.versionInfo}>
              <div className={styles.version}>v1.0.0</div>
              <div className={styles.environment}>
                {process.env.NODE_ENV === 'development' ? 'DEV' : 'PROD'}
              </div>
            </div>
          </div>
        )}
        
        <div className={styles.securityIndicator}>
          <div className={styles.securityIcon}>🔒</div>
          {!collapsed && (
            <div className={styles.securityText}>Secure Portal</div>
          )}
        </div>
      </div>
    </aside>
  );
}
