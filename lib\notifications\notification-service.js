/**
 * Unified Notification Service for Ocean Soul Sparkles Admin Dashboard
 * 
 * Central service that manages all notifications (email, SMS, push) with
 * feature flag checking, fallback behavior, and settings-based control.
 */

import emailService from '../email/email-service'
import smsService from '../sms/sms-service'

/**
 * Notification service class with comprehensive feature flag support
 */
class NotificationService {
  constructor() {
    this.settings = null
    this.settingsLastFetched = null
    this.settingsCacheTime = 5 * 60 * 1000 // 5 minutes
  }

  /**
   * Get current notification settings with caching
   */
  async getSettings() {
    const now = Date.now()
    
    // Return cached settings if still valid
    if (this.settings && this.settingsLastFetched && 
        (now - this.settingsLastFetched) < this.settingsCacheTime) {
      return this.settings
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/admin/settings`)
      const { settings } = await response.json()
      
      this.settings = settings?.notifications || {}
      this.settingsLastFetched = now
      
      return this.settings
    } catch (error) {
      console.error('Error fetching notification settings:', error)
      // Return default settings if fetch fails
      return {
        emailNotifications: true,
        smsNotifications: false,
        emailFallbackWhenSMSFails: true,
        smsFallbackWhenEmailFails: false
      }
    }
  }

  /**
   * Clear settings cache (call when settings are updated)
   */
  clearSettingsCache() {
    this.settings = null
    this.settingsLastFetched = null
  }

  /**
   * Check if a specific notification type is enabled
   */
  async isNotificationEnabled(type, channel) {
    const settings = await this.getSettings()
    
    // Check global channel toggle
    const globalKey = `${channel}Notifications`
    if (!settings[globalKey]) {
      return false
    }
    
    // Check specific notification type
    const typeKey = `${channel}${type.charAt(0).toUpperCase() + type.slice(1)}`
    return settings[typeKey] !== false
  }

  /**
   * Send booking confirmation notification
   */
  async sendBookingConfirmation(booking) {
    const results = {
      email: { attempted: false, success: false },
      sms: { attempted: false, success: false }
    }

    // Try email first
    if (await this.isNotificationEnabled('bookingConfirmation', 'email')) {
      results.email.attempted = true
      const emailResult = await emailService.sendBookingConfirmation(booking)
      results.email.success = emailResult.success
      results.email.messageId = emailResult.messageId
      results.email.error = emailResult.error
    }

    // Try SMS
    if (await this.isNotificationEnabled('bookingConfirmation', 'sms')) {
      results.sms.attempted = true
      const smsResult = await smsService.sendBookingConfirmation(booking)
      results.sms.success = smsResult.success
      results.sms.messageId = smsResult.messageId
      results.sms.error = smsResult.error
      results.sms.skipped = smsResult.skipped
    }

    // Handle fallbacks
    await this.handleFallbacks('bookingConfirmation', booking, results)

    return results
  }

  /**
   * Send booking reminder notification
   */
  async sendBookingReminder(booking) {
    const results = {
      email: { attempted: false, success: false },
      sms: { attempted: false, success: false }
    }

    // Try email first
    if (await this.isNotificationEnabled('bookingReminder', 'email')) {
      results.email.attempted = true
      const emailResult = await emailService.sendBookingReminder(booking)
      results.email.success = emailResult.success
      results.email.messageId = emailResult.messageId
      results.email.error = emailResult.error
    }

    // Try SMS
    if (await this.isNotificationEnabled('bookingReminder', 'sms')) {
      results.sms.attempted = true
      const smsResult = await smsService.sendBookingReminder(booking)
      results.sms.success = smsResult.success
      results.sms.messageId = smsResult.messageId
      results.sms.error = smsResult.error
      results.sms.skipped = smsResult.skipped
    }

    // Handle fallbacks
    await this.handleFallbacks('bookingReminder', booking, results)

    return results
  }

  /**
   * Send booking cancellation notification
   */
  async sendBookingCancellation(booking) {
    const results = {
      email: { attempted: false, success: false },
      sms: { attempted: false, success: false }
    }

    // Try email first
    if (await this.isNotificationEnabled('bookingCancellation', 'email')) {
      results.email.attempted = true
      const emailResult = await emailService.sendBookingCancellation(booking)
      results.email.success = emailResult.success
      results.email.messageId = emailResult.messageId
      results.email.error = emailResult.error
    }

    // Try SMS
    if (await this.isNotificationEnabled('bookingCancellation', 'sms')) {
      results.sms.attempted = true
      const smsResult = await smsService.sendBookingCancellation(booking)
      results.sms.success = smsResult.success
      results.sms.messageId = smsResult.messageId
      results.sms.error = smsResult.error
      results.sms.skipped = smsResult.skipped
    }

    // Handle fallbacks
    await this.handleFallbacks('bookingCancellation', booking, results)

    return results
  }

  /**
   * Send payment receipt notification
   */
  async sendPaymentReceipt(payment) {
    const results = {
      email: { attempted: false, success: false },
      sms: { attempted: false, success: false }
    }

    // Try email first
    if (await this.isNotificationEnabled('paymentReceipt', 'email')) {
      results.email.attempted = true
      const emailResult = await emailService.sendPaymentReceipt(payment)
      results.email.success = emailResult.success
      results.email.messageId = emailResult.messageId
      results.email.error = emailResult.error
    }

    // Try SMS
    if (await this.isNotificationEnabled('paymentReceipt', 'sms')) {
      results.sms.attempted = true
      const smsResult = await smsService.sendSMS({
        to: payment.customerPhone,
        message: `Payment received! $${payment.amount} for ${payment.serviceName}. Receipt #${payment.receiptNumber}. Thank you for choosing Ocean Soul Sparkles!`,
        type: 'payment_receipt'
      })
      results.sms.success = smsResult.success
      results.sms.messageId = smsResult.messageId
      results.sms.error = smsResult.error
    }

    // Handle fallbacks
    await this.handleFallbacks('paymentReceipt', payment, results)

    return results
  }

  /**
   * Send staff notification
   */
  async sendStaffNotification(notification) {
    const results = {
      email: { attempted: false, success: false },
      sms: { attempted: false, success: false }
    }

    // Try email first
    if (await this.isNotificationEnabled('staffNotification', 'email')) {
      results.email.attempted = true
      const emailResult = await emailService.sendStaffNotification(notification)
      results.email.success = emailResult.success
      results.email.messageId = emailResult.messageId
      results.email.error = emailResult.error
    }

    // Try SMS
    if (await this.isNotificationEnabled('staffNotification', 'sms')) {
      results.sms.attempted = true
      const smsResult = await smsService.sendStaffNotification(notification)
      results.sms.success = smsResult.success
      results.sms.messageId = smsResult.messageId
      results.sms.error = smsResult.error
      results.sms.skipped = smsResult.skipped
    }

    return results
  }

  /**
   * Handle fallback behavior when primary notification method fails
   */
  async handleFallbacks(type, data, results) {
    const settings = await this.getSettings()

    // Email fallback when SMS fails
    if (settings.emailFallbackWhenSMSFails && 
        results.sms.attempted && !results.sms.success && 
        !results.email.attempted) {
      
      console.log(`SMS failed for ${type}, attempting email fallback`)
      
      try {
        let emailResult
        switch (type) {
          case 'bookingConfirmation':
            emailResult = await emailService.sendBookingConfirmation(data)
            break
          case 'bookingReminder':
            emailResult = await emailService.sendBookingReminder(data)
            break
          case 'bookingCancellation':
            emailResult = await emailService.sendBookingCancellation(data)
            break
          case 'paymentReceipt':
            emailResult = await emailService.sendPaymentReceipt(data)
            break
          default:
            console.log(`No email fallback available for ${type}`)
            return
        }
        
        results.email.attempted = true
        results.email.success = emailResult.success
        results.email.messageId = emailResult.messageId
        results.email.error = emailResult.error
        results.email.fallback = true
      } catch (error) {
        console.error(`Email fallback failed for ${type}:`, error)
        results.email.attempted = true
        results.email.success = false
        results.email.error = error.message
        results.email.fallback = true
      }
    }

    // SMS fallback when email fails
    if (settings.smsFallbackWhenEmailFails && 
        results.email.attempted && !results.email.success && 
        !results.sms.attempted) {
      
      console.log(`Email failed for ${type}, attempting SMS fallback`)
      
      try {
        let smsResult
        switch (type) {
          case 'bookingConfirmation':
            smsResult = await smsService.sendBookingConfirmation(data)
            break
          case 'bookingReminder':
            smsResult = await smsService.sendBookingReminder(data)
            break
          case 'bookingCancellation':
            smsResult = await smsService.sendBookingCancellation(data)
            break
          default:
            console.log(`No SMS fallback available for ${type}`)
            return
        }
        
        results.sms.attempted = true
        results.sms.success = smsResult.success
        results.sms.messageId = smsResult.messageId
        results.sms.error = smsResult.error
        results.sms.fallback = true
      } catch (error) {
        console.error(`SMS fallback failed for ${type}:`, error)
        results.sms.attempted = true
        results.sms.success = false
        results.sms.error = error.message
        results.sms.fallback = true
      }
    }
  }

  /**
   * Get notification service status
   */
  async getStatus() {
    const settings = await this.getSettings()
    const emailStatus = emailService.getStatus()
    const smsStatus = smsService.getStatus()
    
    return {
      settings,
      email: emailStatus,
      sms: smsStatus,
      settingsLastFetched: this.settingsLastFetched
    }
  }
}

module.exports = new NotificationService()
