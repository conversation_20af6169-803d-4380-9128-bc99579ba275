/**
 * Ocean Soul Sparkles Admin - PWA Fixes Test Script
 * Tests the fixes for critical production PWA errors
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Testing PWA Fixes for Ocean Soul Sparkles Admin\n');

// Test 1: Check if service worker file exists
function testServiceWorkerFile() {
  console.log('1. Testing Service Worker File...');
  
  const swPath = path.join(__dirname, '..', 'public', 'sw.js');
  
  if (fs.existsSync(swPath)) {
    const swContent = fs.readFileSync(swPath, 'utf8');
    
    // Check for essential service worker features
    const requiredFeatures = [
      'addEventListener',
      'install',
      'activate',
      'fetch',
      'caches',
      'CACHE_NAME',
      'background sync',
      'push notification'
    ];
    
    const missingFeatures = requiredFeatures.filter(feature => 
      !swContent.toLowerCase().includes(feature.toLowerCase())
    );
    
    if (missingFeatures.length === 0) {
      console.log('   ✅ Service worker file exists and contains all required features');
      return true;
    } else {
      console.log('   ❌ Service worker missing features:', missingFeatures.join(', '));
      return false;
    }
  } else {
    console.log('   ❌ Service worker file not found at /public/sw.js');
    return false;
  }
}

// Test 2: Check if PWA manifest exists
function testManifestFile() {
  console.log('\n2. Testing PWA Manifest File...');
  
  const manifestPath = path.join(__dirname, '..', 'public', 'manifest.json');
  
  if (fs.existsSync(manifestPath)) {
    try {
      const manifestContent = fs.readFileSync(manifestPath, 'utf8');
      const manifest = JSON.parse(manifestContent);
      
      // Check for essential manifest properties
      const requiredProperties = [
        'name',
        'short_name',
        'start_url',
        'display',
        'background_color',
        'theme_color',
        'icons',
        'shortcuts'
      ];
      
      const missingProperties = requiredProperties.filter(prop => 
        !manifest.hasOwnProperty(prop)
      );
      
      if (missingProperties.length === 0) {
        console.log('   ✅ PWA manifest file exists and contains all required properties');
        console.log(`   📱 App name: ${manifest.name}`);
        console.log(`   🎯 Start URL: ${manifest.start_url}`);
        console.log(`   📱 Display mode: ${manifest.display}`);
        console.log(`   🎨 Theme color: ${manifest.theme_color}`);
        console.log(`   🔗 Shortcuts: ${manifest.shortcuts?.length || 0} defined`);
        return true;
      } else {
        console.log('   ❌ Manifest missing properties:', missingProperties.join(', '));
        return false;
      }
    } catch (error) {
      console.log('   ❌ Manifest file exists but contains invalid JSON:', error.message);
      return false;
    }
  } else {
    console.log('   ❌ PWA manifest file not found at /public/manifest.json');
    return false;
  }
}

// Test 3: Check PWAManager component
function testPWAManagerComponent() {
  console.log('\n3. Testing PWAManager Component...');
  
  const pwaManagerPath = path.join(__dirname, '..', 'components', 'admin', 'PWAManager.tsx');
  
  if (fs.existsSync(pwaManagerPath)) {
    const pwaManagerContent = fs.readFileSync(pwaManagerPath, 'utf8');
    
    // Check for the fix for customer data caching
    const hasCustomerArrayFix = pwaManagerContent.includes('customersData.customers || []') &&
                               pwaManagerContent.includes('Array.isArray(customers)');
    
    // Check for service worker error handling
    const hasServiceWorkerErrorHandling = pwaManagerContent.includes('addEventListener(\'message\'') &&
                                         pwaManagerContent.includes('catch (error)');
    
    if (hasCustomerArrayFix && hasServiceWorkerErrorHandling) {
      console.log('   ✅ PWAManager component has proper error handling and data type fixes');
      return true;
    } else {
      console.log('   ❌ PWAManager component missing fixes:');
      if (!hasCustomerArrayFix) {
        console.log('      - Customer data array validation fix');
      }
      if (!hasServiceWorkerErrorHandling) {
        console.log('      - Service worker error handling');
      }
      return false;
    }
  } else {
    console.log('   ❌ PWAManager component not found');
    return false;
  }
}

// Test 4: Check cache manager fixes
function testCacheManagerFixes() {
  console.log('\n4. Testing Cache Manager Fixes...');
  
  const cacheManagerPath = path.join(__dirname, '..', 'lib', 'pwa', 'cache-manager.ts');
  
  if (fs.existsSync(cacheManagerPath)) {
    const cacheManagerContent = fs.readFileSync(cacheManagerPath, 'utf8');
    
    // Check for array validation fix
    const hasArrayValidation = cacheManagerContent.includes('Array.isArray(customers)') &&
                              cacheManagerContent.includes('customers parameter must be an array');
    
    if (hasArrayValidation) {
      console.log('   ✅ Cache manager has proper array validation to prevent forEach errors');
      return true;
    } else {
      console.log('   ❌ Cache manager missing array validation fix');
      return false;
    }
  } else {
    console.log('   ❌ Cache manager file not found');
    return false;
  }
}

// Test 5: Check _app.tsx integration
function testAppIntegration() {
  console.log('\n5. Testing _app.tsx Integration...');
  
  const appPath = path.join(__dirname, '..', 'pages', '_app.tsx');
  
  if (fs.existsSync(appPath)) {
    const appContent = fs.readFileSync(appPath, 'utf8');
    
    // Check for PWAManager import and usage
    const hasPWAManagerImport = appContent.includes('import PWAManager');
    const hasPWAManagerUsage = appContent.includes('<PWAManager>');
    const hasManifestLink = appContent.includes('rel="manifest"');
    
    if (hasPWAManagerImport && hasPWAManagerUsage && hasManifestLink) {
      console.log('   ✅ _app.tsx properly integrates PWAManager and manifest link');
      return true;
    } else {
      console.log('   ❌ _app.tsx missing PWA integration:');
      if (!hasPWAManagerImport) {
        console.log('      - PWAManager import');
      }
      if (!hasPWAManagerUsage) {
        console.log('      - PWAManager component usage');
      }
      if (!hasManifestLink) {
        console.log('      - Manifest link in head');
      }
      return false;
    }
  } else {
    console.log('   ❌ _app.tsx file not found');
    return false;
  }
}

// Test 6: Check Next.js configuration
function testNextConfigPWA() {
  console.log('\n6. Testing Next.js PWA Configuration...');
  
  const nextConfigPath = path.join(__dirname, '..', 'next.config.js');
  
  if (fs.existsSync(nextConfigPath)) {
    const nextConfigContent = fs.readFileSync(nextConfigPath, 'utf8');
    
    // Check for service worker headers
    const hasServiceWorkerHeaders = nextConfigContent.includes('/sw.js') &&
                                   nextConfigContent.includes('Service-Worker-Allowed');
    
    // Check for manifest headers
    const hasManifestHeaders = nextConfigContent.includes('/manifest.json') &&
                              nextConfigContent.includes('application/manifest+json');
    
    if (hasServiceWorkerHeaders && hasManifestHeaders) {
      console.log('   ✅ Next.js configuration has proper PWA headers');
      return true;
    } else {
      console.log('   ❌ Next.js configuration missing PWA headers:');
      if (!hasServiceWorkerHeaders) {
        console.log('      - Service worker headers');
      }
      if (!hasManifestHeaders) {
        console.log('      - Manifest headers');
      }
      return false;
    }
  } else {
    console.log('   ❌ next.config.js file not found');
    return false;
  }
}

// Test 7: Check for potential runtime errors
function testPotentialRuntimeErrors() {
  console.log('\n7. Testing for Potential Runtime Errors...');
  
  const filesToCheck = [
    'components/admin/PWAManager.tsx',
    'lib/pwa/cache-manager.ts'
  ];
  
  let hasRuntimeErrorPrevention = true;
  
  filesToCheck.forEach(filePath => {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // Check for proper error handling patterns
      const hasTryCatch = content.includes('try {') && content.includes('catch (error)');
      const hasTypeChecking = content.includes('typeof') || content.includes('Array.isArray');
      const hasNullChecks = content.includes('!==') || content.includes('===');
      
      if (!hasTryCatch || !hasTypeChecking || !hasNullChecks) {
        console.log(`   ❌ ${filePath} missing error prevention patterns`);
        hasRuntimeErrorPrevention = false;
      }
    }
  });
  
  if (hasRuntimeErrorPrevention) {
    console.log('   ✅ All PWA files have proper error prevention patterns');
    return true;
  } else {
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting PWA Fixes Verification...\n');
  
  const tests = [
    { name: 'Service Worker File', test: testServiceWorkerFile },
    { name: 'PWA Manifest File', test: testManifestFile },
    { name: 'PWAManager Component', test: testPWAManagerComponent },
    { name: 'Cache Manager Fixes', test: testCacheManagerFixes },
    { name: 'App Integration', test: testAppIntegration },
    { name: 'Next.js PWA Config', test: testNextConfigPWA },
    { name: 'Runtime Error Prevention', test: testPotentialRuntimeErrors }
  ];
  
  const results = tests.map(({ name, test }) => ({
    name,
    passed: test()
  }));
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 PWA FIXES TEST RESULTS');
  console.log('='.repeat(60));
  
  results.forEach(({ name, passed }) => {
    console.log(`${passed ? '✅' : '❌'} ${name}`);
  });
  
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  const passRate = Math.round((passedTests / totalTests) * 100);
  
  console.log('\n' + '-'.repeat(60));
  console.log(`📈 Overall Results: ${passedTests}/${totalTests} tests passed (${passRate}%)`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All PWA fixes have been successfully implemented!');
    console.log('🚀 The production errors should now be resolved.');
    console.log('\n📋 Next Steps:');
    console.log('   1. Deploy the fixes to production');
    console.log('   2. Test the PWA functionality in production');
    console.log('   3. Monitor for any remaining errors');
    console.log('   4. Verify service worker registration');
    console.log('   5. Test offline functionality');
  } else {
    console.log('⚠️  Some PWA fixes are incomplete. Please review the failed tests above.');
  }
  
  console.log('\n' + '='.repeat(60));
}

// Execute the tests
runAllTests().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
