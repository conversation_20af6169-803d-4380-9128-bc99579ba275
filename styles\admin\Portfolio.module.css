/* Portfolio Management Styles */
.portfolioManager {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e1e5e9;
}

.header h2 {
  color: #2c3e50;
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.addButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error button {
  background: none;
  border: none;
  color: #c33;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filterGroup label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.filterGroup select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.modalHeader h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: #f8f9fa;
  color: #495057;
}

.form {
  padding: 30px;
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.checkboxGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.checkboxGroup label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: normal;
  cursor: pointer;
}

.checkboxGroup input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.formActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.cancelButton {
  padding: 12px 24px;
  border: 1px solid #ced4da;
  background: white;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.submitButton {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.submitButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.portfolioGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.portfolioCard {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.portfolioCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.imageContainer {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.portfolioImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.portfolioCard:hover .portfolioImage {
  transform: scale(1.05);
}

.featuredBadge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #8b5a00;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.privateBadge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(108, 117, 125, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.cardContent {
  padding: 20px;
}

.cardContent h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.category {
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 12px 0;
}

.description {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.tag {
  background: #f8f9fa;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  border: 1px solid #e9ecef;
}

.cardActions {
  display: flex;
  gap: 8px;
}

.editButton {
  flex: 1;
  padding: 8px 16px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.editButton:hover {
  background: #138496;
  transform: translateY(-1px);
}

.deleteButton {
  flex: 1;
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.deleteButton:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.emptyState {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.emptyState p {
  font-size: 18px;
  margin-bottom: 20px;
}

.emptyState button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.emptyState button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Portfolio Page Styles */
.portfolioPage {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.pageHeader {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e1e5e9;
}

.headerContent h1 {
  color: #2c3e50;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
}

.headerContent p {
  color: #6c757d;
  margin: 0;
  font-size: 16px;
}

.accessDenied {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.accessDenied h2 {
  color: #dc3545;
  margin-bottom: 16px;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.statCard {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.statIcon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.statContent h3 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
}

.statContent p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

.categoriesOverview {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 30px;
  border: 1px solid #e9ecef;
}

.categoriesOverview h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.categoryTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.categoryTag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Individual Artist Portfolio Styles */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #6c757d;
}

.breadcrumb a {
  color: #667eea;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb a:hover {
  color: #764ba2;
  text-decoration: underline;
}

.breadcrumb span:last-child {
  color: #2c3e50;
  font-weight: 500;
}

.artistHeader {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
}

.artistInfo h1 {
  color: #2c3e50;
  margin: 0 0 16px 0;
  font-size: 28px;
  font-weight: 700;
}

.artistDetails {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.email {
  color: #667eea;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.specializations {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.specializations strong {
  color: #495057;
  font-size: 14px;
}

.specializationTags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.specializationTag {
  background: #f8f9fa;
  color: #495057;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

.bio {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  max-width: 500px;
}

.artistStats {
  display: flex;
  gap: 24px;
  flex-shrink: 0;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.statValue {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.statLabel {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.backButton {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.backButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .portfolioManager {
    padding: 15px;
  }

  .portfolioPage {
    padding: 15px;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .filters {
    flex-direction: column;
    gap: 15px;
  }

  .formRow {
    grid-template-columns: 1fr;
  }

  .portfolioGrid {
    grid-template-columns: 1fr;
  }

  .modal {
    padding: 10px;
  }

  .modalContent {
    max-height: 95vh;
  }

  .form {
    padding: 20px;
  }

  .formActions {
    flex-direction: column;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .statCard {
    padding: 20px;
  }

  .headerContent h1 {
    font-size: 24px;
  }

  .categoriesOverview {
    padding: 20px;
  }
}
