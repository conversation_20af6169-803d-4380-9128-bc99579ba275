/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Input Styles
 * Touch-friendly input styling optimized for mobile devices
 */

.container {
  position: relative;
  width: 100%;
  margin-bottom: 1rem;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: color 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.required {
  color: #ef4444;
  margin-left: 0.25rem;
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  transition: all 0.2s ease;
  overflow: hidden;
}

.input {
  flex: 1;
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #111827;
  background: transparent;
  border: none;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  
  /* Mobile-specific optimizations */
  min-height: 44px; /* iOS minimum touch target */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  
  /* Prevent zoom on iOS */
  font-size: 16px;
}

.input::placeholder {
  color: #9ca3af;
  opacity: 1;
}

.input:focus {
  outline: none;
}

.withIcon {
  padding-left: 3rem;
}

.withSuffix {
  padding-right: 3rem;
}

.icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
  z-index: 1;
}

.suffix {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 1;
}

.passwordToggle {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  font-size: 1.25rem;
  color: #6b7280;
  transition: color 0.2s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  -webkit-tap-highlight-color: transparent;
}

.passwordToggle:hover {
  color: #374151;
  background: #f3f4f6;
}

.passwordToggle:active {
  background: #e5e7eb;
}

/* Focus state */
.focused .inputWrapper {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.focused .label {
  color: #3b82f6;
}

.focused .icon {
  color: #3b82f6;
}

/* Error state */
.error .inputWrapper {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error .label {
  color: #ef4444;
}

.error .icon {
  color: #ef4444;
}

/* Success state */
.success .inputWrapper {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.success .label {
  color: #10b981;
}

.success .icon {
  color: #10b981;
}

/* Disabled state */
.disabled .inputWrapper {
  background: #f9fafb;
  border-color: #e5e7eb;
  cursor: not-allowed;
}

.disabled .input {
  color: #9ca3af;
  cursor: not-allowed;
}

.disabled .label {
  color: #9ca3af;
  cursor: not-allowed;
}

.disabled .icon {
  color: #d1d5db;
}

/* Read-only state */
.readOnly .inputWrapper {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.readOnly .input {
  cursor: default;
}

.readOnly .label {
  cursor: default;
}

/* Error message */
.errorMessage {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #ef4444;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  animation: slideDown 0.2s ease;
}

.errorMessage::before {
  content: '⚠️';
  font-size: 0.75rem;
}

/* Help text */
.helpText {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .container {
    margin-bottom: 1.25rem;
  }
  
  .input {
    padding: 1.25rem 1rem;
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 48px; /* Larger touch target on mobile */
  }
  
  .withIcon {
    padding-left: 3.5rem;
  }
  
  .withSuffix {
    padding-right: 3.5rem;
  }
  
  .icon {
    left: 1.25rem;
    font-size: 1.25rem;
  }
  
  .suffix {
    right: 1.25rem;
  }
  
  .label {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }
  
  .errorMessage,
  .helpText {
    font-size: 1rem;
    margin-top: 0.75rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .input {
    padding: 1.5rem 1rem;
    min-height: 52px;
  }
  
  .withIcon {
    padding-left: 4rem;
  }
  
  .withSuffix {
    padding-right: 4rem;
  }
  
  .icon {
    left: 1.5rem;
    font-size: 1.5rem;
  }
  
  .suffix {
    right: 1.5rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .inputWrapper {
    border-width: 3px;
  }
  
  .focused .inputWrapper {
    border-color: #000000;
  }
  
  .error .inputWrapper {
    border-color: #cc0000;
  }
  
  .success .inputWrapper {
    border-color: #006600;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .inputWrapper,
  .label,
  .icon,
  .passwordToggle {
    transition: none;
  }
  
  .errorMessage {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .label {
    color: #e5e7eb;
  }
  
  .inputWrapper {
    background: #1f2937;
    border-color: #4b5563;
  }
  
  .input {
    color: #f9fafb;
  }
  
  .input::placeholder {
    color: #9ca3af;
  }
  
  .icon,
  .suffix {
    color: #9ca3af;
  }
  
  .helpText {
    color: #9ca3af;
  }
  
  .disabled .inputWrapper {
    background: #111827;
    border-color: #374151;
  }
  
  .readOnly .inputWrapper {
    background: #111827;
    border-color: #374151;
  }
}
