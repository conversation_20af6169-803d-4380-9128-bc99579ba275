/**
 * Ocean Soul Sparkles Admin Dashboard - Performance Monitoring Page
 * Admin interface for viewing performance metrics and alerts
 */

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import PerformanceDashboard from '../../components/admin/PerformanceDashboard';
import styles from '../../styles/admin/AdminLayout.module.css';

const MonitoringPage: React.FC = () => {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/verify');
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        router.push('/admin/login');
        return;
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      router.push('/admin/login');
      return;
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      router.push('/admin/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <>
      <Head>
        <title>Performance Monitoring - Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Performance monitoring and system health dashboard" />
      </Head>

      <div className={styles.container}>
        {/* Header */}
        <header className={styles.header}>
          <div className={styles.headerContent}>
            <div className={styles.headerLeft}>
              <h1 className={styles.headerTitle}>Performance Monitoring</h1>
              <p className={styles.headerSubtitle}>System health and performance metrics</p>
            </div>
            <div className={styles.headerRight}>
              <div className={styles.userInfo}>
                <span className={styles.userName}>{user.email}</span>
                <span className={styles.userRole}>{user.role}</span>
              </div>
              <button onClick={handleLogout} className={styles.logoutButton}>
                Logout
              </button>
            </div>
          </div>
        </header>

        {/* Navigation */}
        <nav className={styles.nav}>
          <div className={styles.navContent}>
            <a href="/admin/dashboard" className={styles.navLink}>
              Dashboard
            </a>
            <a href="/admin/customers" className={styles.navLink}>
              Customers
            </a>
            <a href="/admin/bookings" className={styles.navLink}>
              Bookings
            </a>
            <a href="/admin/services" className={styles.navLink}>
              Services
            </a>
            <a href="/admin/inventory" className={styles.navLink}>
              Inventory
            </a>
            <a href="/admin/staff" className={styles.navLink}>
              Staff
            </a>
            <a href="/admin/reports" className={styles.navLink}>
              Reports
            </a>
            <a href="/admin/monitoring" className={`${styles.navLink} ${styles.navLinkActive}`}>
              Monitoring
            </a>
            <a href="/admin/settings" className={styles.navLink}>
              Settings
            </a>
          </div>
        </nav>

        {/* Main Content */}
        <main className={styles.main}>
          <PerformanceDashboard />
        </main>

        {/* Footer */}
        <footer className={styles.footer}>
          <div className={styles.footerContent}>
            <p>&copy; 2025 Ocean Soul Sparkles. All rights reserved.</p>
            <div className={styles.footerLinks}>
              <a href="/admin/monitoring" className={styles.footerLink}>System Health</a>
              <a href="/api/health" target="_blank" className={styles.footerLink}>Health Check</a>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
};

export default MonitoringPage;
