/* Customer Feedback Page Styles */

.feedbackContainer {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.headerLeft {
  flex: 1;
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.errorMessage {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.closeError {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.averagesSection {
  margin-bottom: 32px;
}

.averagesTitle {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
}

.averagesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.averageCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.averageCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.averageValue {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 8px;
}

.averageStars {
  margin-bottom: 12px;
}

.averageLabel {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.starFilled {
  color: #fbbf24;
  font-size: 18px;
}

.starEmpty {
  color: #d1d5db;
  font-size: 18px;
}

.filters {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-bottom: 32px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filterGroup label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.checkboxLabel input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.feedbackList {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feedbackCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.feedbackCard:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #d1d5db;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.customerInfo {
  flex: 1;
}

.customerName {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.bookingInfo {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 4px 0;
}

.artistInfo {
  font-size: 14px;
  color: #374151;
  margin: 0;
  font-weight: 500;
}

.cardMeta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.overallRating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ratingValue {
  font-size: 24px;
  font-weight: 700;
}

.ratingStars {
  display: flex;
}

.feedbackDate {
  font-size: 12px;
  color: #6b7280;
}

.publicBadge {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.detailedRatings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.ratingDetail {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.ratingNumber {
  color: #6b7280;
  font-size: 12px;
}

.feedbackText,
.suggestions {
  margin-bottom: 16px;
}

.feedbackText h4,
.suggestions h4 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.feedbackText p,
.suggestions p {
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.recommendation {
  margin-bottom: 16px;
  font-size: 14px;
}

.recommendYes {
  color: #059669;
  font-weight: 600;
}

.recommendNo {
  color: #dc2626;
  font-weight: 600;
}

.adminResponse {
  background: #eff6ff;
  border: 1px solid #dbeafe;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.adminResponse h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1d4ed8;
  margin: 0 0 8px 0;
}

.adminResponse p {
  color: #374151;
  line-height: 1.6;
  margin: 0 0 8px 0;
}

.responseDate {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.emptyState h3 {
  font-size: 20px;
  color: #374151;
  margin: 0 0 8px 0;
}

.emptyState p {
  margin: 0;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 32px;
  padding: 20px;
}

.paginationBtn {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.paginationBtn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.paginationBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.paginationInfo {
  font-size: 14px;
  color: #6b7280;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .cardHeader {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .cardMeta {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .feedbackContainer {
    padding: 16px;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .averagesGrid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .detailedRatings {
    grid-template-columns: 1fr;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
  }

  .paginationBtn {
    width: 100%;
    max-width: 200px;
  }
}
