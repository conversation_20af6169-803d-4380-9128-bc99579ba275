const { default: fetch } = await import('node-fetch');

async function testLogin() {
  const { default: fetch } = await import('node-fetch');
  
  try {
    console.log('Testing login API...');
    
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    });

    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', data);
    
    if (response.ok) {
      console.log('✓ Login API is working correctly');
    } else {
      console.log('✗ Login API returned an error');
    }
    
  } catch (error) {
    console.error('Error testing login:', error);
  }
}

testLogin();
