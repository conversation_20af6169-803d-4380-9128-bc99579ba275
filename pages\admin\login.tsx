import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { toast } from 'react-toastify';
import LoginForm from '../../components/auth/LoginForm';
import MFAForm from '../../components/auth/MFAForm';
import styles from '../../styles/admin/Login.module.css';

interface LoginState {
  step: 'login' | 'mfa';
  user?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  error?: string;
}

export default function AdminLogin() {
  const router = useRouter();
  const [loginState, setLoginState] = useState<LoginState>({ step: 'login' });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Check if user is already logged in
    const token = localStorage.getItem('admin-token');
    if (token) {
      router.push('/admin/dashboard');
    }

    // Handle logout reason
    const { reason, error } = router.query;
    if (reason === 'timeout') {
      toast.warning('Your session has expired. Please log in again.');
    } else if (error === 'system') {
      toast.error('A system error occurred. Please try logging in again.');
    }
  }, [router]);

  const handleLoginSubmit = async (email: string, password: string) => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Login failed');
      }

      if (data.requiresMFA) {
        // Move to MFA step
        setLoginState({
          step: 'mfa',
          user: data.user
        });
        toast.info('Please enter your MFA code to complete login.');
      } else {
        // Login successful
        localStorage.setItem('admin-token', data.token);
        document.cookie = `admin-token=${data.token}; path=/; secure; samesite=strict`;
        
        toast.success(`Welcome back, ${data.user.firstName}!`);
        router.push('/admin/dashboard');
      }
    } catch (error) {
      console.error('Login error:', error);
      setLoginState({ 
        step: 'login', 
        error: error instanceof Error ? error.message : 'Login failed' 
      });
      toast.error(error instanceof Error ? error.message : 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMFASubmit = async (mfaCode: string) => {
    if (!loginState.user) return;

    setIsLoading(true);
    
    try {
      const response = await fetch('/api/auth/mfa-verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: loginState.user.id, 
          mfaCode 
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'MFA verification failed');
      }

      // Login successful
      localStorage.setItem('admin-token', data.token);
      document.cookie = `admin-token=${data.token}; path=/; secure; samesite=strict`;
      
      toast.success(`Welcome back, ${data.user.firstName}!`);
      router.push('/admin/dashboard');
    } catch (error) {
      console.error('MFA error:', error);
      toast.error(error instanceof Error ? error.message : 'MFA verification failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    setLoginState({ step: 'login' });
  };

  return (
    <>
      <Head>
        <title>Admin Login - Ocean Soul Sparkles</title>
        <meta name="description" content="Secure admin portal login for Ocean Soul Sparkles staff" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.logoSection}>
            <div className={styles.logo}>
              <h1>Ocean Soul Sparkles</h1>
              <p>Admin Portal</p>
            </div>
          </div>

          <div className={styles.formSection}>
            {loginState.step === 'login' ? (
              <LoginForm
                onSubmit={handleLoginSubmit}
                isLoading={isLoading}
                error={loginState.error}
              />
            ) : (
              <MFAForm
                user={loginState.user!}
                onSubmit={handleMFASubmit}
                onBack={handleBackToLogin}
                isLoading={isLoading}
              />
            )}
          </div>

          <div className={styles.securityNotice}>
            <div className={styles.securityIcon}>🔒</div>
            <div className={styles.securityText}>
              <p>This is a secure admin portal.</p>
              <p>All access is monitored and logged.</p>
            </div>
          </div>
        </div>

        <div className={styles.backgroundPattern}>
          <div className={styles.sparkle}></div>
          <div className={styles.sparkle}></div>
          <div className={styles.sparkle}></div>
          <div className={styles.sparkle}></div>
          <div className={styles.sparkle}></div>
        </div>
      </div>
    </>
  );
}
