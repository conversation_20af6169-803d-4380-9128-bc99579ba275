const jsPDF = require('jspdf').jsPDF;
require('jspdf-autotable');

/**
 * Generate PDF report from report data
 */
export async function generatePDFReport(reportData, dateRange, rangeName) {
  const doc = new jsPDF();
  
  // Set up document properties
  doc.setProperties({
    title: 'Ocean Soul Sparkles - Business Report',
    subject: `Business Analytics Report - ${rangeName}`,
    author: 'Ocean Soul Sparkles Admin System',
    creator: 'Ocean Soul Sparkles Admin Dashboard'
  });

  // Add header
  addHeader(doc, rangeName, dateRange);
  
  let yPosition = 60;

  // Add overview section
  yPosition = addOverviewSection(doc, reportData.overview, yPosition);
  
  // Add revenue section
  if (yPosition > 200) {
    doc.addPage();
    yPosition = 20;
  }
  yPosition = addRevenueSection(doc, reportData.revenue, yPosition);
  
  // Add bookings section
  if (yPosition > 200) {
    doc.addPage();
    yPosition = 20;
  }
  yPosition = addBookingsSection(doc, reportData.bookings, yPosition);
  
  // Add customers section
  if (yPosition > 200) {
    doc.addPage();
    yPosition = 20;
  }
  yPosition = addCustomersSection(doc, reportData.customers, yPosition);

  // Add footer
  addFooter(doc, reportData.metadata);

  return doc.output('arraybuffer');
}

/**
 * Add document header
 */
function addHeader(doc, rangeName, dateRange) {
  // Company logo area (placeholder)
  doc.setFillColor(102, 126, 234);
  doc.rect(20, 10, 170, 30, 'F');
  
  // Company name
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('Ocean Soul Sparkles', 25, 25);
  
  // Report title
  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  doc.text('Business Analytics Report', 25, 35);
  
  // Date range
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(12);
  doc.text(`Report Period: ${formatDateRange(rangeName)}`, 20, 50);
  doc.text(`Generated: ${new Date().toLocaleDateString('en-AU')}`, 120, 50);
}

/**
 * Add overview metrics section
 */
function addOverviewSection(doc, overview, yPosition) {
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(102, 126, 234);
  doc.text('Executive Summary', 20, yPosition);
  
  yPosition += 15;
  
  // Create overview table
  const overviewData = [
    ['Total Revenue', formatCurrency(overview.totalRevenue), `${overview.revenueGrowth >= 0 ? '+' : ''}${overview.revenueGrowth.toFixed(1)}%`],
    ['Total Bookings', overview.totalBookings.toString(), `${overview.bookingGrowth >= 0 ? '+' : ''}${overview.bookingGrowth.toFixed(1)}%`],
    ['Total Customers', overview.totalCustomers.toString(), '-'],
    ['Average Booking Value', formatCurrency(overview.averageBookingValue), '-']
  ];

  doc.autoTable({
    startY: yPosition,
    head: [['Metric', 'Value', 'Growth']],
    body: overviewData,
    theme: 'grid',
    headStyles: { 
      fillColor: [102, 126, 234],
      textColor: [255, 255, 255],
      fontStyle: 'bold'
    },
    styles: { 
      fontSize: 10,
      cellPadding: 5
    },
    columnStyles: {
      0: { cellWidth: 60 },
      1: { cellWidth: 50, halign: 'right' },
      2: { cellWidth: 30, halign: 'center' }
    }
  });

  return doc.lastAutoTable.finalY + 20;
}

/**
 * Add revenue analysis section
 */
function addRevenueSection(doc, revenue, yPosition) {
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(102, 126, 234);
  doc.text('Revenue Analysis', 20, yPosition);
  
  yPosition += 15;

  // Revenue by service table
  if (revenue.byService && revenue.byService.length > 0) {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('Revenue by Service', 20, yPosition);
    yPosition += 10;

    const serviceData = revenue.byService.map(service => [
      service.service,
      formatCurrency(service.amount),
      `${service.percentage.toFixed(1)}%`
    ]);

    doc.autoTable({
      startY: yPosition,
      head: [['Service', 'Revenue', 'Percentage']],
      body: serviceData,
      theme: 'striped',
      headStyles: { 
        fillColor: [118, 75, 162],
        textColor: [255, 255, 255],
        fontStyle: 'bold'
      },
      styles: { 
        fontSize: 9,
        cellPadding: 4
      },
      columnStyles: {
        0: { cellWidth: 80 },
        1: { cellWidth: 40, halign: 'right' },
        2: { cellWidth: 30, halign: 'center' }
      }
    });

    yPosition = doc.lastAutoTable.finalY + 15;
  }

  return yPosition;
}

/**
 * Add bookings analysis section
 */
function addBookingsSection(doc, bookings, yPosition) {
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(102, 126, 234);
  doc.text('Booking Analysis', 20, yPosition);
  
  yPosition += 15;

  // Booking status breakdown
  if (bookings.statusBreakdown && bookings.statusBreakdown.length > 0) {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('Booking Status Breakdown', 20, yPosition);
    yPosition += 10;

    const statusData = bookings.statusBreakdown.map(status => [
      status.status,
      status.count.toString(),
      `${status.percentage.toFixed(1)}%`
    ]);

    doc.autoTable({
      startY: yPosition,
      head: [['Status', 'Count', 'Percentage']],
      body: statusData,
      theme: 'striped',
      headStyles: { 
        fillColor: [34, 197, 94],
        textColor: [255, 255, 255],
        fontStyle: 'bold'
      },
      styles: { 
        fontSize: 9,
        cellPadding: 4
      },
      columnStyles: {
        0: { cellWidth: 60 },
        1: { cellWidth: 30, halign: 'center' },
        2: { cellWidth: 30, halign: 'center' }
      }
    });

    yPosition = doc.lastAutoTable.finalY + 10;
  }

  // Cancellation rate
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text(`Cancellation Rate: ${bookings.cancellationRate.toFixed(1)}%`, 20, yPosition);

  return yPosition + 15;
}

/**
 * Add customers analysis section
 */
function addCustomersSection(doc, customers, yPosition) {
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(102, 126, 234);
  doc.text('Customer Analysis', 20, yPosition);
  
  yPosition += 15;

  // Customer metrics table
  const customerData = [
    ['New Customers', customers.newCustomers.toString()],
    ['Returning Customers', customers.returningCustomers.toString()],
    ['Customer Lifetime Value', formatCurrency(customers.customerLifetimeValue)]
  ];

  doc.autoTable({
    startY: yPosition,
    head: [['Metric', 'Value']],
    body: customerData,
    theme: 'grid',
    headStyles: { 
      fillColor: [59, 130, 246],
      textColor: [255, 255, 255],
      fontStyle: 'bold'
    },
    styles: { 
      fontSize: 10,
      cellPadding: 5
    },
    columnStyles: {
      0: { cellWidth: 80 },
      1: { cellWidth: 50, halign: 'right' }
    }
  });

  return doc.lastAutoTable.finalY + 20;
}

/**
 * Add document footer
 */
function addFooter(doc, metadata) {
  const pageCount = doc.internal.getNumberOfPages();
  
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    
    // Footer line
    doc.setDrawColor(200, 200, 200);
    doc.line(20, 280, 190, 280);
    
    // Footer text
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100, 100, 100);
    doc.text('Ocean Soul Sparkles - Confidential Business Report', 20, 285);
    doc.text(`Page ${i} of ${pageCount}`, 160, 285);
    doc.text(`Generated: ${new Date(metadata.generatedAt).toLocaleString('en-AU')}`, 20, 290);
  }
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD'
  }).format(amount);
}

/**
 * Format date range for display
 */
function formatDateRange(rangeName) {
  const ranges = {
    'last7days': 'Last 7 Days',
    'last30days': 'Last 30 Days',
    'last90days': 'Last 90 Days',
    'thisyear': 'This Year'
  };
  
  return ranges[rangeName] || rangeName;
}
