/**
 * Ocean Soul Sparkles Admin - Bulk Actions Component
 * Provides bulk operations for list pages (delete, update, export)
 */

import React, { useState, useRef, useEffect } from 'react';
import { QuickExportButton } from './ExportButton';
import styles from '../../styles/admin/BulkActions.module.css';

export type BulkActionType = 'delete' | 'activate' | 'deactivate' | 'export' | 'update_status' | 'assign_category';

interface BulkAction {
  id: BulkActionType;
  label: string;
  icon: string;
  variant: 'primary' | 'secondary' | 'danger' | 'warning';
  requiresConfirmation?: boolean;
  confirmationMessage?: string;
  disabled?: boolean;
}

interface BulkActionsProps {
  selectedItems: any[];
  totalItems: number;
  onSelectAll: (selected: boolean) => void;
  onBulkAction: (action: BulkActionType, items: any[], options?: any) => Promise<void>;
  actions?: BulkAction[];
  exportType?: 'customers' | 'bookings' | 'services' | 'products' | 'inventory';
  className?: string;
  disabled?: boolean;
}

const DEFAULT_ACTIONS: BulkAction[] = [
  {
    id: 'activate',
    label: 'Activate',
    icon: '✅',
    variant: 'primary',
    requiresConfirmation: true,
    confirmationMessage: 'Are you sure you want to activate the selected items?'
  },
  {
    id: 'deactivate',
    label: 'Deactivate',
    icon: '❌',
    variant: 'warning',
    requiresConfirmation: true,
    confirmationMessage: 'Are you sure you want to deactivate the selected items?'
  },
  {
    id: 'delete',
    label: 'Delete',
    icon: '🗑️',
    variant: 'danger',
    requiresConfirmation: true,
    confirmationMessage: 'Are you sure you want to delete the selected items? This action cannot be undone.'
  }
];

export default function BulkActions({
  selectedItems,
  totalItems,
  onSelectAll,
  onBulkAction,
  actions = DEFAULT_ACTIONS,
  exportType,
  className = '',
  disabled = false
}: BulkActionsProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [processingAction, setProcessingAction] = useState<BulkActionType | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const hasSelection = selectedItems.length > 0;
  const isAllSelected = selectedItems.length === totalItems && totalItems > 0;
  const isPartiallySelected = selectedItems.length > 0 && selectedItems.length < totalItems;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelectAll = () => {
    onSelectAll(!isAllSelected);
  };

  const handleBulkAction = async (action: BulkAction) => {
    if (!hasSelection || isProcessing) return;

    // Show confirmation if required
    if (action.requiresConfirmation) {
      const message = action.confirmationMessage || `Are you sure you want to ${action.label.toLowerCase()} ${selectedItems.length} item(s)?`;
      if (!confirm(message)) {
        setShowDropdown(false);
        return;
      }
    }

    setIsProcessing(true);
    setProcessingAction(action.id);
    setShowDropdown(false);

    try {
      await onBulkAction(action.id, selectedItems);
    } catch (error) {
      console.error(`Bulk ${action.label} error:`, error);
      alert(`Failed to ${action.label.toLowerCase()} items: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
      setProcessingAction(null);
    }
  };

  const getCheckboxState = () => {
    if (isAllSelected) return 'checked';
    if (isPartiallySelected) return 'indeterminate';
    return 'unchecked';
  };

  const getSelectionText = () => {
    if (selectedItems.length === 0) return 'Select items';
    if (selectedItems.length === 1) return '1 item selected';
    return `${selectedItems.length} items selected`;
  };

  return (
    <div className={`${styles.bulkActions} ${className} ${hasSelection ? styles.hasSelection : ''}`}>
      <div className={styles.selectionControls}>
        <label className={styles.selectAllLabel}>
          <input
            type="checkbox"
            checked={isAllSelected}
            ref={(input) => {
              if (input) {
                input.indeterminate = isPartiallySelected;
              }
            }}
            onChange={handleSelectAll}
            disabled={disabled || totalItems === 0}
            className={styles.selectAllCheckbox}
          />
          <span className={styles.checkboxCustom}>
            {getCheckboxState() === 'indeterminate' && <span className={styles.indeterminate}>−</span>}
            {getCheckboxState() === 'checked' && <span className={styles.checked}>✓</span>}
          </span>
          <span className={styles.selectionText}>{getSelectionText()}</span>
        </label>

        {totalItems > 0 && (
          <span className={styles.totalCount}>
            of {totalItems} total
          </span>
        )}
      </div>

      {hasSelection && (
        <div className={styles.actionControls}>
          {/* Export button */}
          {exportType && (
            <QuickExportButton
              data={selectedItems}
              type={exportType}
              className={styles.exportBtn}
              disabled={isProcessing}
            />
          )}

          {/* Bulk actions dropdown */}
          <div className={styles.actionsDropdown} ref={dropdownRef}>
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              disabled={disabled || isProcessing || !hasSelection}
              className={`${styles.actionsBtn} ${isProcessing ? styles.processing : ''}`}
              title={`Bulk actions for ${selectedItems.length} selected items`}
            >
              {isProcessing && processingAction && (
                <div className={styles.spinner}></div>
              )}
              <span>Actions</span>
              <span className={styles.dropdownArrow}>▼</span>
            </button>

            {showDropdown && (
              <div className={styles.actionsMenu}>
                <div className={styles.menuHeader}>
                  <span>Bulk Actions ({selectedItems.length} items)</span>
                </div>
                
                {actions.map((action) => (
                  <button
                    key={action.id}
                    onClick={() => handleBulkAction(action)}
                    disabled={action.disabled || isProcessing}
                    className={`${styles.actionItem} ${styles[action.variant]}`}
                    title={action.confirmationMessage}
                  >
                    <span className={styles.actionIcon}>{action.icon}</span>
                    <span className={styles.actionLabel}>{action.label}</span>
                    {action.requiresConfirmation && (
                      <span className={styles.confirmationIndicator}>⚠️</span>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Clear selection */}
          <button
            onClick={() => onSelectAll(false)}
            className={styles.clearBtn}
            disabled={isProcessing}
            title="Clear selection"
          >
            Clear
          </button>
        </div>
      )}
    </div>
  );
}

// Hook for managing bulk selection state
export function useBulkSelection<T extends { id: string | number }>(items: T[]) {
  const [selectedIds, setSelectedIds] = useState<Set<string | number>>(new Set());

  const selectedItems = items.filter(item => selectedIds.has(item.id));

  const toggleItem = (id: string | number) => {
    const newSelected = new Set(selectedIds);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedIds(newSelected);
  };

  const selectAll = (selected: boolean) => {
    if (selected) {
      setSelectedIds(new Set(items.map(item => item.id)));
    } else {
      setSelectedIds(new Set());
    }
  };

  const clearSelection = () => {
    setSelectedIds(new Set());
  };

  const isSelected = (id: string | number) => selectedIds.has(id);

  return {
    selectedItems,
    selectedIds,
    toggleItem,
    selectAll,
    clearSelection,
    isSelected,
    hasSelection: selectedIds.size > 0,
    selectionCount: selectedIds.size
  };
}
