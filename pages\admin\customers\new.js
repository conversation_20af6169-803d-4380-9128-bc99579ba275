import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import styles from '@/styles/admin/NewCustomer.module.css';

/**
 * New Customer Creation Page
 * 
 * This page provides a form interface for adding new customers to the database.
 */
export default function NewCustomer() {
  const router = useRouter();
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check authentication
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        setAuthenticated(isLoggedIn);
        if (!isLoggedIn) {
          router.push('/login');
          return;
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading...</p>
      </div>
    );
  }

  if (!authenticated) {
    return null; // Will redirect to login
  }

  return (
    <>
      <Head>
        <title>New Customer | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Add a new customer to the database" />
      </Head>

      <div className={styles.newCustomerContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Add New Customer</h1>
          <div className={styles.headerActions}>
            <Link href="/admin/customers" className={styles.backButton}>
              ← Back to Customers
            </Link>
          </div>
        </header>

        <div className={styles.content}>
          <div className={styles.comingSoon}>
            <h2>New Customer Form</h2>
            <p>This page will contain a customer registration form with:</p>
            <ul>
              <li>Personal information (name, email, phone)</li>
              <li>Address and contact details</li>
              <li>Preferences and notes</li>
              <li>Emergency contact information</li>
              <li>Marketing preferences</li>
              <li>Customer status and tags</li>
            </ul>
            <div className={styles.actions}>
              <Link href="/admin/customers" className={styles.backBtn}>
                Back to Customers
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
