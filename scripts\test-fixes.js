/**
 * Ocean Soul Sparkles Admin - Test Fixes Script
 * Tests all the fixes implemented for the critical errors
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Ocean Soul Sparkles Admin Fixes\n');

// Test 1: Check VAPID keys are configured
function testVAPIDConfiguration() {
  console.log('1. Testing VAPID Configuration...');
  
  const envPath = path.join(__dirname, '..', '.env.local');
  
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    const hasPublicKey = envContent.includes('NEXT_PUBLIC_VAPID_PUBLIC_KEY=');
    const hasPrivateKey = envContent.includes('VAPID_PRIVATE_KEY=');
    const hasSubject = envContent.includes('VAPID_SUBJECT=');
    
    if (hasPublicKey && hasPrivateKey && hasSubject) {
      console.log('   ✅ VAPID keys are properly configured');
      return true;
    } else {
      console.log('   ❌ VAPID keys are missing or incomplete');
      console.log(`      Public Key: ${hasPublicKey ? '✅' : '❌'}`);
      console.log(`      Private Key: ${hasPrivateKey ? '✅' : '❌'}`);
      console.log(`      Subject: ${hasSubject ? '✅' : '❌'}`);
      return false;
    }
  } else {
    console.log('   ❌ .env.local file not found');
    return false;
  }
}

// Test 2: Check booking API improvements
function testBookingAPIImprovements() {
  console.log('\n2. Testing Booking API Improvements...');
  
  const apiPath = path.join(__dirname, '..', 'pages', 'api', 'admin', 'bookings.ts');
  
  if (fs.existsSync(apiPath)) {
    const apiContent = fs.readFileSync(apiPath, 'utf8');
    
    // Check for improved error handling
    const hasDetailedLogging = apiContent.includes('console.log(\'Creating booking with request body:\'');
    const hasDateValidation = apiContent.includes('isNaN(startDate.getTime())');
    const hasImprovedConflictCheck = apiContent.includes('gte(\'end_time\', start_time)');
    const hasSpecificErrorCodes = apiContent.includes('bookingError.code === \'23503\'');
    
    const improvements = [
      { name: 'Detailed Request Logging', status: hasDetailedLogging },
      { name: 'Date/Time Validation', status: hasDateValidation },
      { name: 'Improved Conflict Check', status: hasImprovedConflictCheck },
      { name: 'Specific Error Codes', status: hasSpecificErrorCodes }
    ];
    
    const passedCount = improvements.filter(i => i.status).length;
    
    if (passedCount === improvements.length) {
      console.log('   ✅ All booking API improvements implemented');
      return true;
    } else {
      console.log(`   ⚠️  ${passedCount}/${improvements.length} improvements implemented:`);
      improvements.forEach(improvement => {
        console.log(`      ${improvement.status ? '✅' : '❌'} ${improvement.name}`);
      });
      return passedCount > 0;
    }
  } else {
    console.log('   ❌ Booking API file not found');
    return false;
  }
}

// Test 3: Check service worker message handling
function testServiceWorkerMessageHandling() {
  console.log('\n3. Testing Service Worker Message Handling...');
  
  const swPath = path.join(__dirname, '..', 'public', 'sw.js');
  
  if (fs.existsSync(swPath)) {
    const swContent = fs.readFileSync(swPath, 'utf8');
    
    const hasImprovedMessageHandling = swContent.includes('event.ports && event.ports[0]');
    const hasErrorHandling = swContent.includes('try {') && swContent.includes('catch (error)');
    const hasResponseMessages = swContent.includes('MESSAGE_RECEIVED');
    
    const features = [
      { name: 'Port-based Message Handling', status: hasImprovedMessageHandling },
      { name: 'Error Handling in Messages', status: hasErrorHandling },
      { name: 'Response Message Types', status: hasResponseMessages }
    ];
    
    const passedCount = features.filter(f => f.status).length;
    
    if (passedCount === features.length) {
      console.log('   ✅ Service worker message handling improved');
      return true;
    } else {
      console.log(`   ⚠️  ${passedCount}/${features.length} features implemented:`);
      features.forEach(feature => {
        console.log(`      ${feature.status ? '✅' : '❌'} ${feature.name}`);
      });
      return passedCount > 0;
    }
  } else {
    console.log('   ❌ Service worker file not found');
    return false;
  }
}

// Test 4: Check error management system
function testErrorManagementSystem() {
  console.log('\n4. Testing Error Management System...');
  
  const errorManagerPath = path.join(__dirname, '..', 'lib', 'error-handling', 'error-manager.ts');
  
  if (fs.existsSync(errorManagerPath)) {
    const errorManagerContent = fs.readFileSync(errorManagerPath, 'utf8');
    
    const hasBookingErrorHandler = errorManagerContent.includes('handleBookingError');
    const hasPWAErrorHandler = errorManagerContent.includes('handlePWAError');
    const hasPaymentErrorHandler = errorManagerContent.includes('handlePaymentError');
    const hasUserFeedback = errorManagerContent.includes('showUserFeedback');
    
    const features = [
      { name: 'Booking Error Handler', status: hasBookingErrorHandler },
      { name: 'PWA Error Handler', status: hasPWAErrorHandler },
      { name: 'Payment Error Handler', status: hasPaymentErrorHandler },
      { name: 'User Feedback System', status: hasUserFeedback }
    ];
    
    const passedCount = features.filter(f => f.status).length;
    
    if (passedCount === features.length) {
      console.log('   ✅ Error management system implemented');
      return true;
    } else {
      console.log(`   ⚠️  ${passedCount}/${features.length} features implemented:`);
      features.forEach(feature => {
        console.log(`      ${feature.status ? '✅' : '❌'} ${feature.name}`);
      });
      return passedCount > 0;
    }
  } else {
    console.log('   ❌ Error management system not found');
    return false;
  }
}

// Test 5: Check PWA Manager improvements
function testPWAManagerImprovements() {
  console.log('\n5. Testing PWA Manager Improvements...');
  
  const pwaManagerPath = path.join(__dirname, '..', 'components', 'admin', 'PWAManager.tsx');
  
  if (fs.existsSync(pwaManagerPath)) {
    const pwaManagerContent = fs.readFileSync(pwaManagerPath, 'utf8');
    
    const hasErrorManagerImport = pwaManagerContent.includes('error-handling/error-manager');
    const hasMessageChannelHandling = pwaManagerContent.includes('MessageChannel');
    const hasServiceWorkerErrorHandling = pwaManagerContent.includes('errorManager.handlePWAError');
    
    const features = [
      { name: 'Error Manager Integration', status: hasErrorManagerImport },
      { name: 'Message Channel Handling', status: hasMessageChannelHandling },
      { name: 'Service Worker Error Handling', status: hasServiceWorkerErrorHandling }
    ];
    
    const passedCount = features.filter(f => f.status).length;
    
    if (passedCount === features.length) {
      console.log('   ✅ PWA Manager improvements implemented');
      return true;
    } else {
      console.log(`   ⚠️  ${passedCount}/${features.length} features implemented:`);
      features.forEach(feature => {
        console.log(`      ${feature.status ? '✅' : '❌'} ${feature.name}`);
      });
      return passedCount > 0;
    }
  } else {
    console.log('   ❌ PWA Manager file not found');
    return false;
  }
}

// Run all tests
function runAllTests() {
  const tests = [
    { name: 'VAPID Configuration', test: testVAPIDConfiguration },
    { name: 'Booking API Improvements', test: testBookingAPIImprovements },
    { name: 'Service Worker Message Handling', test: testServiceWorkerMessageHandling },
    { name: 'Error Management System', test: testErrorManagementSystem },
    { name: 'PWA Manager Improvements', test: testPWAManagerImprovements }
  ];
  
  const results = tests.map(({ name, test }) => ({
    name,
    passed: test()
  }));
  
  const passedCount = results.filter(r => r.passed).length;
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });
  
  console.log(`\n🎯 Overall: ${passedCount}/${tests.length} tests passed`);
  
  if (passedCount === tests.length) {
    console.log('🎉 All fixes have been successfully implemented!');
    console.log('\n📋 Next Steps:');
    console.log('1. Test the booking creation workflow in the browser');
    console.log('2. Verify push notifications are working');
    console.log('3. Check service worker communication');
    console.log('4. Monitor error logs for any remaining issues');
  } else {
    console.log('⚠️  Some fixes may need additional work.');
  }
  
  return passedCount === tests.length;
}

// Run the tests
runAllTests();
