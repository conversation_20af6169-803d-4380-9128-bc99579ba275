/**
 * Ocean Soul Sparkles Admin Dashboard - Backup Verification API
 * Performs backup integrity checks and validation
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { withMiddleware, authenticatedMiddleware } from '../../../../lib/errors/api-error-middleware';
import { createClient } from '@supabase/supabase-js';
import { ApiResponse } from '../../../../types/api';

interface BackupVerificationResult {
  verification_id: string;
  timestamp: string;
  overall_status: 'healthy' | 'warning' | 'error';
  tables_checked: number;
  total_rows: number;
  total_size: string;
  checks_performed: {
    data_integrity: boolean;
    table_structure: boolean;
    row_counts: boolean;
    recent_activity: boolean;
  };
  table_details: Array<{
    table_name: string;
    row_count: number;
    last_updated: string;
    size: string;
    status: 'healthy' | 'warning' | 'error';
    issues: string[];
  }>;
  recommendations: string[];
  next_verification: string;
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

async function handler(req: NextApiRequest, res: NextApiResponse<ApiResponse<BackupVerificationResult>>) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: `Method ${req.method} not allowed`
      }
    });
  }

  try {
    const verificationId = generateVerificationId();
    const timestamp = new Date().toISOString();

    // Log verification start
    await logBackupOperation('manual', 'started', null, null, 'Backup verification initiated', {
      verification_id: verificationId,
      type: 'integrity_check'
    });

    // Perform comprehensive verification
    const verificationResult = await performBackupVerification(verificationId, timestamp);

    // Log verification completion
    await logBackupOperation('manual', 'completed', null, null, null, {
      verification_id: verificationId,
      type: 'integrity_check',
      status: verificationResult.overall_status,
      tables_checked: verificationResult.tables_checked
    });

    res.status(200).json({
      success: true,
      data: verificationResult,
      meta: {
        requestId: (req as any).context?.requestId || 'unknown',
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });

  } catch (error) {
    console.error('Error performing backup verification:', error);
    
    // Log verification failure
    await logBackupOperation('manual', 'failed', null, null, error instanceof Error ? error.message : 'Unknown error', {
      type: 'integrity_check'
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'VERIFICATION_FAILED',
        message: 'Failed to perform backup verification'
      }
    });
  }
}

async function performBackupVerification(verificationId: string, timestamp: string): Promise<BackupVerificationResult> {
  // Get table integrity data
  const { data: integrityData, error: integrityError } = await supabase
    .rpc('verify_backup_integrity');

  if (integrityError) {
    throw new Error(`Integrity check failed: ${integrityError.message}`);
  }

  // Process table details
  const tableDetails = await processTableDetails(integrityData || []);
  
  // Calculate overall metrics
  const totalRows = tableDetails.reduce((sum, table) => sum + table.row_count, 0);
  const totalSize = calculateTotalSize(tableDetails);
  
  // Determine overall status
  const overallStatus = determineOverallStatus(tableDetails);
  
  // Generate recommendations
  const recommendations = generateRecommendations(tableDetails, overallStatus);
  
  // Calculate next verification time (24 hours from now)
  const nextVerification = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();

  return {
    verification_id: verificationId,
    timestamp,
    overall_status: overallStatus,
    tables_checked: tableDetails.length,
    total_rows: totalRows,
    total_size: totalSize,
    checks_performed: {
      data_integrity: true,
      table_structure: true,
      row_counts: true,
      recent_activity: true
    },
    table_details: tableDetails,
    recommendations,
    next_verification: nextVerification
  };
}

async function processTableDetails(integrityData: any[]): Promise<Array<{
  table_name: string;
  row_count: number;
  last_updated: string;
  size: string;
  status: 'healthy' | 'warning' | 'error';
  issues: string[];
}>> {
  return integrityData.map(table => {
    const issues: string[] = [];
    let status: 'healthy' | 'warning' | 'error' = 'healthy';

    // Check for potential issues
    if (table.row_count === 0) {
      issues.push('Table is empty');
      status = 'warning';
    }

    if (!table.last_updated) {
      issues.push('No recent activity detected');
      status = 'warning';
    } else {
      const lastUpdated = new Date(table.last_updated);
      const daysSinceUpdate = (Date.now() - lastUpdated.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceUpdate > 30) {
        issues.push(`No activity for ${Math.round(daysSinceUpdate)} days`);
        status = status === 'healthy' ? 'warning' : status;
      }
    }

    // Check table size
    const sizeInBytes = parseSizeString(table.table_size);
    if (sizeInBytes > 100 * 1024 * 1024) { // > 100MB
      issues.push('Large table - monitor backup performance');
    }

    return {
      table_name: table.table_name,
      row_count: table.row_count,
      last_updated: table.last_updated || 'Never',
      size: table.table_size,
      status,
      issues
    };
  });
}

function parseSizeString(sizeStr: string): number {
  if (!sizeStr) return 0;
  
  const match = sizeStr.match(/(\d+(?:\.\d+)?)\s*(\w+)/);
  if (!match) return 0;
  
  const value = parseFloat(match[1]);
  const unit = match[2].toLowerCase();
  
  const multipliers: { [key: string]: number } = {
    'bytes': 1,
    'kb': 1024,
    'mb': 1024 * 1024,
    'gb': 1024 * 1024 * 1024
  };
  
  return value * (multipliers[unit] || 1);
}

function calculateTotalSize(tableDetails: any[]): string {
  const totalBytes = tableDetails.reduce((sum, table) => {
    return sum + parseSizeString(table.size);
  }, 0);
  
  if (totalBytes < 1024) return `${totalBytes} bytes`;
  if (totalBytes < 1024 * 1024) return `${(totalBytes / 1024).toFixed(1)} KB`;
  if (totalBytes < 1024 * 1024 * 1024) return `${(totalBytes / (1024 * 1024)).toFixed(1)} MB`;
  return `${(totalBytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
}

function determineOverallStatus(tableDetails: any[]): 'healthy' | 'warning' | 'error' {
  const errorTables = tableDetails.filter(table => table.status === 'error');
  const warningTables = tableDetails.filter(table => table.status === 'warning');
  
  if (errorTables.length > 0) return 'error';
  if (warningTables.length > 0) return 'warning';
  return 'healthy';
}

function generateRecommendations(tableDetails: any[], overallStatus: string): string[] {
  const recommendations: string[] = [];
  
  // General recommendations based on status
  if (overallStatus === 'error') {
    recommendations.push('URGENT: Address critical issues before proceeding with production deployment');
    recommendations.push('Consider immediate backup of healthy tables');
  } else if (overallStatus === 'warning') {
    recommendations.push('Review warning issues and plan remediation');
    recommendations.push('Monitor tables with no recent activity');
  } else {
    recommendations.push('Database integrity is healthy - continue regular monitoring');
  }
  
  // Specific recommendations based on table analysis
  const emptyTables = tableDetails.filter(table => table.row_count === 0);
  if (emptyTables.length > 0) {
    recommendations.push(`Consider if empty tables (${emptyTables.map(t => t.table_name).join(', ')}) are expected`);
  }
  
  const largeTables = tableDetails.filter(table => parseSizeString(table.size) > 50 * 1024 * 1024);
  if (largeTables.length > 0) {
    recommendations.push(`Monitor backup performance for large tables: ${largeTables.map(t => t.table_name).join(', ')}`);
  }
  
  const staleTables = tableDetails.filter(table => {
    if (!table.last_updated || table.last_updated === 'Never') return false;
    const daysSinceUpdate = (Date.now() - new Date(table.last_updated).getTime()) / (1000 * 60 * 60 * 24);
    return daysSinceUpdate > 7;
  });
  
  if (staleTables.length > 0) {
    recommendations.push(`Review tables with no recent activity: ${staleTables.map(t => t.table_name).join(', ')}`);
  }
  
  // Backup-specific recommendations
  recommendations.push('Enable Point-in-Time Recovery (PITR) for comprehensive data protection');
  recommendations.push('Schedule automated daily backups with 90-day retention');
  recommendations.push('Test recovery procedures monthly to ensure backup reliability');
  recommendations.push('Set up backup monitoring alerts for immediate failure notification');
  
  return recommendations;
}

async function logBackupOperation(
  type: string,
  status: string,
  sizeMb: number | null,
  durationSeconds: number | null,
  errorMessage: string | null,
  metadata: any
) {
  try {
    await supabase.rpc('log_backup_status', {
      p_backup_type: type,
      p_status: status,
      p_size_mb: sizeMb,
      p_duration_seconds: durationSeconds,
      p_error_message: errorMessage,
      p_metadata: metadata
    });
  } catch (error) {
    console.error('Failed to log backup operation:', error);
  }
}

function generateVerificationId(): string {
  return `verify_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export default withMiddleware(handler, authenticatedMiddleware);
