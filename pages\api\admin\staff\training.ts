import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuth } from '../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Staff training API called - ${req.method}`);

  try {
    // Authenticate admin request
    const authResult = await verifyAdminAuth(req);
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Authentication required',
        message: authResult.message || 'Authentication failed',
        requestId
      });
    }

    const { user } = authResult;

    if (!user) {
      return res.status(401).json({
        error: 'User not found',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { staff_id, module_id } = req.query;

      if (staff_id) {
        // Get training progress for specific staff member
        const { data: progress, error } = await supabase
          .from('staff_training_progress')
          .select(`
            id,
            status,
            started_at,
            completed_at,
            score,
            attempts,
            notes,
            assigned_at,
            staff_training_modules!inner(
              id,
              name,
              description,
              category,
              is_required,
              duration_minutes,
              passing_score
            )
          `)
          .eq('staff_id', staff_id)
          .order('assigned_at', { ascending: false });

        if (error) {
          console.error(`[${requestId}] Database error:`, error);
          return res.status(500).json({
            error: 'Failed to fetch training progress',
            message: error.message,
            requestId
          });
        }

        // Calculate statistics
        const totalModules = progress?.length || 0;
        const completedModules = progress?.filter((p: any) => p.status === 'completed').length || 0;
        const requiredModules = progress?.filter((p: any) => p.staff_training_modules?.is_required).length || 0;
        const completedRequiredModules = progress?.filter((p: any) => p.staff_training_modules?.is_required && p.status === 'completed').length || 0;

        return res.status(200).json({
          progress: progress || [],
          statistics: {
            total: totalModules,
            completed: completedModules,
            required: requiredModules,
            completedRequired: completedRequiredModules,
            completionPercentage: totalModules > 0 ? Math.round((completedModules / totalModules) * 100) : 0,
            requiredCompletionPercentage: requiredModules > 0 ? Math.round((completedRequiredModules / requiredModules) * 100) : 0
          },
          requestId
        });
      } else {
        // Get all training modules
        const { data: modules, error } = await supabase
          .from('staff_training_modules')
          .select(`
            id,
            name,
            description,
            category,
            is_required,
            duration_minutes,
            content_url,
            passing_score,
            is_active,
            created_at
          `)
          .eq('is_active', true)
          .order('category', { ascending: true })
          .order('name', { ascending: true });

        if (error) {
          console.error(`[${requestId}] Database error:`, error);
          return res.status(500).json({
            error: 'Failed to fetch training modules',
            message: error.message,
            requestId
          });
        }

        return res.status(200).json({
          modules: modules || [],
          requestId
        });
      }
    }

    if (req.method === 'POST') {
      const { action, staff_id, module_id, score, notes } = req.body;

      if (!action || !staff_id) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Action and staff ID are required',
          requestId
        });
      }

      if (action === 'assign_module') {
        if (!module_id) {
          return res.status(400).json({
            error: 'Module ID required',
            message: 'Module ID is required for assignment',
            requestId
          });
        }

        // Check if already assigned
        const { data: existing } = await supabase
          .from('staff_training_progress')
          .select('id')
          .eq('staff_id', staff_id)
          .eq('module_id', module_id)
          .single();

        if (existing) {
          return res.status(409).json({
            error: 'Module already assigned',
            message: 'This training module is already assigned to the staff member',
            requestId
          });
        }

        const { data: assignment, error } = await supabase
          .from('staff_training_progress')
          .insert([
            {
              staff_id,
              module_id,
              status: 'not_started',
              assigned_by: user.id,
              assigned_at: new Date().toISOString()
            }
          ])
          .select(`
            id,
            status,
            assigned_at,
            staff_training_modules!inner(
              id,
              name,
              description,
              category,
              is_required,
              duration_minutes
            )
          `)
          .single();

        if (error) {
          console.error(`[${requestId}] Error assigning module:`, error);
          return res.status(500).json({
            error: 'Failed to assign training module',
            message: error.message,
            requestId
          });
        }

        return res.status(201).json({
          assignment,
          message: 'Training module assigned successfully',
          requestId
        });
      }

      if (action === 'assign_all_required') {
        // Assign all required modules to staff member
        const { data: requiredModules } = await supabase
          .from('staff_training_modules')
          .select('id')
          .eq('is_required', true)
          .eq('is_active', true);

        if (!requiredModules || requiredModules.length === 0) {
          return res.status(200).json({
            message: 'No required modules to assign',
            requestId
          });
        }

        // Get already assigned modules
        const { data: existingAssignments } = await supabase
          .from('staff_training_progress')
          .select('module_id')
          .eq('staff_id', staff_id);

        const existingModuleIds = existingAssignments?.map(a => a.module_id) || [];
        const newModules = requiredModules.filter(m => !existingModuleIds.includes(m.id));

        if (newModules.length === 0) {
          return res.status(200).json({
            message: 'All required modules already assigned',
            requestId
          });
        }

        const assignmentData = newModules.map(module => ({
          staff_id,
          module_id: module.id,
          status: 'not_started',
          assigned_by: user.id,
          assigned_at: new Date().toISOString()
        }));

        const { data: assignments, error } = await supabase
          .from('staff_training_progress')
          .insert(assignmentData)
          .select();

        if (error) {
          console.error(`[${requestId}] Error assigning modules:`, error);
          return res.status(500).json({
            error: 'Failed to assign required modules',
            message: error.message,
            requestId
          });
        }

        return res.status(201).json({
          assignments,
          message: `${assignments?.length || 0} required modules assigned successfully`,
          requestId
        });
      }

      if (action === 'start_training') {
        if (!module_id) {
          return res.status(400).json({
            error: 'Module ID required',
            message: 'Module ID is required to start training',
            requestId
          });
        }

        const { data: updatedProgress, error } = await supabase
          .from('staff_training_progress')
          .update({
            status: 'in_progress',
            started_at: new Date().toISOString()
          })
          .eq('staff_id', staff_id)
          .eq('module_id', module_id)
          .select()
          .single();

        if (error) {
          console.error(`[${requestId}] Error starting training:`, error);
          return res.status(500).json({
            error: 'Failed to start training',
            message: error.message,
            requestId
          });
        }

        return res.status(200).json({
          progress: updatedProgress,
          message: 'Training started successfully',
          requestId
        });
      }

      if (action === 'complete_training') {
        if (!module_id) {
          return res.status(400).json({
            error: 'Module ID required',
            message: 'Module ID is required to complete training',
            requestId
          });
        }

        // Get module details to check passing score
        const { data: module } = await supabase
          .from('staff_training_modules')
          .select('passing_score')
          .eq('id', module_id)
          .single();

        const status = (score && module?.passing_score && score >= module.passing_score) ? 'completed' : 'failed';

        // First get current attempts count
        const { data: currentProgress } = await supabase
          .from('staff_training_progress')
          .select('attempts')
          .eq('staff_id', staff_id)
          .eq('module_id', module_id)
          .single();

        const { data: updatedProgress, error } = await supabase
          .from('staff_training_progress')
          .update({
            status,
            completed_at: new Date().toISOString(),
            score: score || null,
            notes: notes || null,
            attempts: (currentProgress?.attempts || 0) + 1
          })
          .eq('staff_id', staff_id)
          .eq('module_id', module_id)
          .select()
          .single();

        if (error) {
          console.error(`[${requestId}] Error completing training:`, error);
          return res.status(500).json({
            error: 'Failed to complete training',
            message: error.message,
            requestId
          });
        }

        return res.status(200).json({
          progress: updatedProgress,
          message: `Training ${status} successfully`,
          requestId
        });
      }

      return res.status(400).json({
        error: 'Invalid action',
        message: 'Action must be assign_module, assign_all_required, start_training, or complete_training',
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
}
