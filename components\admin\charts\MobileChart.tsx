/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Chart Component
 * Mobile-optimized chart wrapper with responsive design and touch interactions
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  Chart as ChartJS,
  ChartOptions,
  ChartData,
  ChartType,
  LegendItem,
  TooltipModel,
  TooltipCallbacks,
  ChartEvent,
  ActiveElement
} from 'chart.js';
import { Bar, Line, Doughnut, Pie } from 'react-chartjs-2';
import styles from './MobileChart.module.css';

export interface MobileChartProps {
  type: 'bar' | 'line' | 'doughnut' | 'pie';
  data: ChartData<any>;
  options?: ChartOptions<any>;
  title?: string;
  subtitle?: string;
  height?: number;
  className?: string;
  loading?: boolean;
  error?: string;
  onDataPointClick?: (dataPoint: any, index: number) => void;
  showLegend?: boolean;
  showTooltips?: boolean;
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  'data-testid'?: string;
}

export const MobileChart: React.FC<MobileChartProps> = ({
  type,
  data,
  options = {},
  title,
  subtitle,
  height = 300,
  className = '',
  loading = false,
  error,
  onDataPointClick,
  showLegend = true,
  showTooltips = true,
  responsive = true,
  maintainAspectRatio = false,
  'data-testid': testId,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chartDimensions, setChartDimensions] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Responsive chart dimensions
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width } = containerRef.current.getBoundingClientRect();
        setChartDimensions({
          width,
          height: isFullscreen ? window.innerHeight - 100 : height
        });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    window.addEventListener('orientationchange', updateDimensions);

    return () => {
      window.removeEventListener('resize', updateDimensions);
      window.removeEventListener('orientationchange', updateDimensions);
    };
  }, [height, isFullscreen]);

  // Mobile-optimized chart options
  const mobileOptions: ChartOptions<any> = {
    responsive: true,
    maintainAspectRatio: false,
    devicePixelRatio: window.devicePixelRatio || 1,
    
    // Touch interactions
    interaction: {
      intersect: false,
      mode: 'index',
    },
    
    // Mobile-friendly plugins
    plugins: {
      legend: {
        display: showLegend,
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: window.innerWidth < 768 ? 14 : 12,
          },
          generateLabels: (chart: ChartJS): LegendItem[] => {
            const original = ChartJS.defaults.plugins.legend.labels.generateLabels;
            const labels = original.call(chart, chart);

            // Limit legend items on mobile
            if (window.innerWidth < 768 && labels.length > 4) {
              return labels.slice(0, 3).concat([{
                text: `+${labels.length - 3} more`,
                fillStyle: '#9ca3af',
                hidden: false,
                index: -1,
                fontColor: '#9ca3af',
                strokeStyle: '#9ca3af',
                lineWidth: 0,
                pointStyle: 'circle'
              } as LegendItem]);
            }

            return labels;
          }
        }
      },
      
      tooltip: {
        enabled: showTooltips,
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#3b82f6',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        titleFont: {
          size: window.innerWidth < 768 ? 16 : 14,
          weight: 'bold'
        },
        bodyFont: {
          size: window.innerWidth < 768 ? 14 : 12
        },
        padding: window.innerWidth < 768 ? 12 : 8,
        caretSize: 8,
        
        // Custom tooltip for mobile
        external: window.innerWidth < 768 ? (context: { chart: ChartJS; tooltip: TooltipModel<any> }) => {
          const { chart, tooltip } = context;
          
          if (tooltip.opacity === 0) {
            return;
          }
          
          // Create custom mobile tooltip
          const tooltipEl = document.getElementById('mobile-chart-tooltip') || 
            (() => {
              const div = document.createElement('div');
              div.id = 'mobile-chart-tooltip';
              div.className = styles.mobileTooltip;
              document.body.appendChild(div);
              return div;
            })();
          
          // Set tooltip content
          if (tooltip.body) {
            const titleLines = tooltip.title || [];
            const bodyLines = tooltip.body.map(b => b.lines);
            
            let innerHtml = '<div class="' + styles.tooltipHeader + '">';
            titleLines.forEach(title => {
              innerHtml += '<div>' + title + '</div>';
            });
            innerHtml += '</div>';
            
            innerHtml += '<div class="' + styles.tooltipBody + '">';
            bodyLines.forEach((body, i) => {
              const colors = tooltip.labelColors[i];
              innerHtml += '<div class="' + styles.tooltipItem + '">';
              innerHtml += '<span class="' + styles.tooltipColor + '" style="background:' + colors.backgroundColor + '"></span>';
              innerHtml += body;
              innerHtml += '</div>';
            });
            innerHtml += '</div>';
            
            tooltipEl.innerHTML = innerHtml;
          }
          
          // Position tooltip
          const position = chart.canvas.getBoundingClientRect();
          tooltipEl.style.opacity = '1';
          tooltipEl.style.position = 'absolute';
          tooltipEl.style.left = position.left + window.pageXOffset + tooltip.caretX + 'px';
          tooltipEl.style.top = position.top + window.pageYOffset + tooltip.caretY + 'px';
          tooltipEl.style.pointerEvents = 'none';
        } : undefined
      }
    },
    
    // Mobile-friendly scales
    scales: type === 'bar' || type === 'line' ? {
      x: {
        grid: {
          display: window.innerWidth >= 768,
        },
        ticks: {
          font: {
            size: window.innerWidth < 768 ? 12 : 10,
          },
          maxRotation: window.innerWidth < 768 ? 45 : 0,
          minRotation: 0,
        }
      },
      y: {
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          font: {
            size: window.innerWidth < 768 ? 12 : 10,
          },
        }
      }
    } : undefined,
    
    // Click handler
    onClick: onDataPointClick ? (event: ChartEvent, elements: ActiveElement[]) => {
      if (elements.length > 0) {
        const element = elements[0];
        const dataPoint = data.datasets[element.datasetIndex].data[element.index];
        onDataPointClick(dataPoint, element.index);
      }
    } : undefined,
    
    // Merge with custom options
    ...options,
  };

  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleExport = () => {
    // Find the canvas element in the chart wrapper
    const canvas = containerRef.current?.querySelector('canvas');
    if (canvas) {
      const url = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.download = `${title || 'chart'}.png`;
      link.href = url;
      link.click();
    }
  };

  const containerClasses = [
    styles.container,
    isFullscreen && styles.fullscreen,
    loading && styles.loading,
    className
  ].filter(Boolean).join(' ');

  const ChartComponent = {
    bar: Bar,
    line: Line,
    doughnut: Doughnut,
    pie: Pie
  }[type];

  if (error) {
    return (
      <div className={containerClasses} data-testid={testId}>
        {title && (
          <div className={styles.header}>
            <h3 className={styles.title}>{title}</h3>
            {subtitle && <p className={styles.subtitle}>{subtitle}</p>}
          </div>
        )}
        <div className={styles.error}>
          <div className={styles.errorIcon}>⚠️</div>
          <div className={styles.errorMessage}>
            Failed to load chart: {error}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={containerClasses} ref={containerRef} data-testid={testId}>
      {title && (
        <div className={styles.header}>
          <div className={styles.titleSection}>
            <h3 className={styles.title}>{title}</h3>
            {subtitle && <p className={styles.subtitle}>{subtitle}</p>}
          </div>
          
          <div className={styles.actions}>
            <button
              className={styles.actionButton}
              onClick={handleFullscreenToggle}
              aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              {isFullscreen ? '⤓' : '⤢'}
            </button>
            
            <button
              className={styles.actionButton}
              onClick={handleExport}
              aria-label="Export chart"
            >
              📥
            </button>
          </div>
        </div>
      )}
      
      <div 
        className={styles.chartWrapper}
        style={{ 
          height: chartDimensions.height || height,
          minHeight: window.innerWidth < 768 ? 250 : 200
        }}
      >
        {loading ? (
          <div className={styles.loadingState}>
            <div className={styles.spinner} />
            <p>Loading chart...</p>
          </div>
        ) : (
          <ChartComponent
            data={data}
            options={mobileOptions}
            height={chartDimensions.height || height}
          />
        )}
      </div>
      
      {isFullscreen && (
        <div className={styles.fullscreenOverlay} onClick={handleFullscreenToggle} />
      )}
    </div>
  );
};

export default MobileChart;
