# Ocean Soul Sparkles Admin Dashboard - Disaster Recovery Runbook

## 🚨 **EMERGENCY RESPONSE PROCEDURES**

### **CRITICAL CONTACT INFORMATION**
**Keep this information accessible at all times**

#### **Primary Response Team**
- **Technical Lead:** [Name] - [Mobile] - [Email]
- **Business Owner:** [Name] - [Mobile] - [Email]
- **Database Admin:** [Name] - [Mobile] - [Email]

#### **External Support**
- **Supabase Support:** <EMAIL>
- **Emergency Hotline:** [24/7 Support Number]
- **Hosting Provider:** [Contact Information]

#### **Communication Channels**
- **Primary:** Phone calls for immediate response
- **Secondary:** Email for documentation
- **Emergency:** SMS for critical alerts

---

## 📋 **DISASTER CLASSIFICATION**

### **Level 1: Minor Incident (Green)**
**Examples:** Single table corruption, recent data loss (< 1 hour)
- **Response Time:** 30 minutes
- **Recovery Target:** 1 hour
- **Escalation:** Technical Lead

### **Level 2: Major Incident (Yellow)**
**Examples:** Database corruption, significant data loss (1-24 hours)
- **Response Time:** 15 minutes
- **Recovery Target:** 4 hours
- **Escalation:** Business Owner + Technical Lead

### **Level 3: Critical Disaster (Red)**
**Examples:** Complete system failure, data center outage, security breach
- **Response Time:** 5 minutes
- **Recovery Target:** 8 hours
- **Escalation:** All stakeholders + external support

---

## 🔥 **IMMEDIATE RESPONSE CHECKLIST**

### **Step 1: Incident Assessment (First 5 Minutes)**
- [ ] **Identify the scope of the problem**
  - What systems are affected?
  - How many users are impacted?
  - Is data at risk?

- [ ] **Classify the incident level**
  - Level 1 (Green), Level 2 (Yellow), or Level 3 (Red)
  - Determine if this is a disaster recovery situation

- [ ] **Activate response team**
  - Contact appropriate team members based on classification
  - Establish communication channel
  - Assign incident commander

### **Step 2: Immediate Containment (Next 10 Minutes)**
- [ ] **Stop further damage**
  - Disable affected services if necessary
  - Prevent additional data loss
  - Secure the environment

- [ ] **Document the incident**
  - Record time of discovery
  - Note symptoms and error messages
  - Take screenshots of error conditions

- [ ] **Communicate status**
  - Notify stakeholders of the incident
  - Provide initial assessment
  - Set expectations for updates

---

## 🛠 **RECOVERY PROCEDURES BY SCENARIO**

### **Scenario A: Accidental Data Deletion**

#### **Symptoms:**
- Missing customer records
- Deleted bookings or services
- User reports of lost data

#### **Immediate Actions:**
1. **STOP all write operations immediately**
   ```sql
   -- Revoke write permissions temporarily
   REVOKE INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public FROM authenticated;
   ```

2. **Identify the scope of deletion**
   - Which tables are affected?
   - What time period is impacted?
   - How much data is missing?

3. **Determine recovery point**
   - Find the last known good state
   - Identify the exact time before deletion
   - Verify backup availability

#### **Recovery Steps:**
1. **Point-in-Time Recovery (if < 30 days)**
   ```bash
   # Via Supabase Dashboard:
   # 1. Go to Database > Backups
   # 2. Select "Point-in-time Recovery"
   # 3. Choose timestamp before deletion
   # 4. Confirm recovery operation
   ```

2. **Validate recovered data**
   ```sql
   -- Run integrity check
   SELECT * FROM verify_backup_integrity();
   
   -- Check specific data
   SELECT COUNT(*) FROM [affected_table] 
   WHERE created_at >= '[recovery_point]';
   ```

3. **Restore write permissions**
   ```sql
   -- Restore normal permissions
   GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO authenticated;
   ```

**Expected Recovery Time:** 15-30 minutes  
**Data Loss:** Minimal (seconds to minutes)

---

### **Scenario B: Database Corruption**

#### **Symptoms:**
- Database connection errors
- Inconsistent query results
- Application crashes
- Data integrity violations

#### **Immediate Actions:**
1. **Isolate the database**
   - Stop all application connections
   - Prevent further corruption
   - Document error messages

2. **Assess corruption scope**
   - Run database integrity checks
   - Identify affected tables
   - Determine if corruption is spreading

#### **Recovery Steps:**
1. **Create new database instance**
   ```bash
   # Via Supabase Dashboard:
   # 1. Create new project
   # 2. Configure same region and settings
   # 3. Note new connection details
   ```

2. **Restore from latest backup**
   ```bash
   # Restore from daily backup
   # 1. Access backup files
   # 2. Restore to new instance
   # 3. Verify data integrity
   ```

3. **Update application configuration**
   ```bash
   # Update environment variables
   NEXT_PUBLIC_SUPABASE_URL=new_url
   SUPABASE_SERVICE_ROLE_KEY=new_key
   ```

4. **Validate and switch over**
   ```sql
   -- Verify all data is present
   SELECT * FROM verify_backup_integrity();
   
   -- Test critical functions
   SELECT COUNT(*) FROM admin_users;
   SELECT COUNT(*) FROM audit_logs;
   ```

**Expected Recovery Time:** 1-2 hours  
**Data Loss:** Up to 24 hours (last backup)

---

### **Scenario C: Complete System Failure**

#### **Symptoms:**
- Total system unavailability
- Cannot access Supabase dashboard
- All services down
- Network connectivity issues

#### **Immediate Actions:**
1. **Activate disaster recovery team**
   - Contact all team members
   - Establish war room (physical or virtual)
   - Assign roles and responsibilities

2. **Assess infrastructure status**
   - Check Supabase status page
   - Verify network connectivity
   - Identify root cause if possible

#### **Recovery Steps:**
1. **Set up emergency infrastructure**
   ```bash
   # Create new Supabase project
   # 1. New project in different region
   # 2. Configure database settings
   # 3. Set up authentication
   ```

2. **Restore from backup**
   ```bash
   # Use most recent backup available
   # 1. Weekly backup if daily unavailable
   # 2. Monthly backup if weekly unavailable
   # 3. Manual backup if automated unavailable
   ```

3. **Reconfigure application**
   ```bash
   # Update all configuration
   # 1. Database connection strings
   # 2. API endpoints
   # 3. Authentication settings
   # 4. DNS records if necessary
   ```

4. **Validate system functionality**
   ```bash
   # Test all critical functions
   # 1. User authentication
   # 2. Data access
   # 3. Core business operations
   # 4. Performance monitoring
   ```

**Expected Recovery Time:** 2-8 hours  
**Data Loss:** Up to 7 days (depending on backup availability)

---

## 📞 **COMMUNICATION PROCEDURES**

### **Internal Communication**

#### **Incident Declaration**
**Template Message:**
```
INCIDENT ALERT - [LEVEL] - [TIME]

System: Ocean Soul Sparkles Admin Dashboard
Issue: [Brief description]
Impact: [User/business impact]
ETA: [Estimated resolution time]

Response Team:
- Incident Commander: [Name]
- Technical Lead: [Name]
- Business Owner: [Name]

Next Update: [Time]
```

#### **Status Updates (Every 30 minutes)**
**Template Message:**
```
INCIDENT UPDATE - [TIME]

Status: [In Progress/Resolved/Escalated]
Progress: [What has been done]
Next Steps: [What's happening next]
ETA: [Updated estimate]

Issues: [Any blockers or complications]
Next Update: [Time]
```

### **External Communication**

#### **Customer Notification (if applicable)**
**Template Message:**
```
Service Notice - Ocean Soul Sparkles

We are currently experiencing technical difficulties with our admin system. 

Impact: [Customer-facing impact]
Expected Resolution: [Time]

We apologize for any inconvenience and will provide updates as available.

For urgent matters, please contact: [Emergency contact]
```

---

## 🔍 **POST-INCIDENT PROCEDURES**

### **Immediate Post-Recovery (First Hour)**
- [ ] **Verify system stability**
  - Monitor for 1 hour minimum
  - Check all critical functions
  - Validate data integrity

- [ ] **Document the incident**
  - Timeline of events
  - Actions taken
  - Lessons learned

- [ ] **Communicate resolution**
  - Notify all stakeholders
  - Provide final status update
  - Thank response team

### **Post-Incident Review (Within 48 Hours)**
- [ ] **Conduct post-mortem meeting**
  - What went well?
  - What could be improved?
  - What prevented faster recovery?

- [ ] **Update procedures**
  - Revise runbook based on learnings
  - Update contact information
  - Improve monitoring and alerts

- [ ] **Implement improvements**
  - Address root causes
  - Enhance backup procedures
  - Improve monitoring

---

## 📊 **RECOVERY METRICS**

### **Target Metrics**
- **RTO (Recovery Time Objective):** 4 hours maximum
- **RPO (Recovery Point Objective):** 1 hour maximum
- **MTTR (Mean Time to Recovery):** 2 hours average
- **Availability Target:** 99.9% uptime

### **Measurement and Reporting**
- **Incident Response Time:** Time from detection to response
- **Recovery Time:** Time from incident to full restoration
- **Data Loss:** Amount of data lost (time period)
- **Business Impact:** Revenue/operations affected

---

## 🧪 **TESTING AND VALIDATION**

### **Monthly Disaster Recovery Tests**
- [ ] **Test backup restoration**
- [ ] **Validate recovery procedures**
- [ ] **Update contact information**
- [ ] **Review and update runbook**

### **Quarterly Full DR Exercises**
- [ ] **Simulate complete system failure**
- [ ] **Test all recovery scenarios**
- [ ] **Validate communication procedures**
- [ ] **Train new team members**

---

## 📚 **REFERENCE INFORMATION**

### **Critical System Information**
- **Supabase Project ID:** ndlgbcsbidyhxbpqzgqp
- **Database Version:** PostgreSQL 15.8.1
- **Region:** ap-southeast-2 (Sydney)
- **Application URL:** [Production URL]

### **Backup Locations**
- **Primary:** Supabase managed storage
- **Secondary:** AWS S3 (us-west-2)
- **Tertiary:** Local encrypted storage

### **Key Database Tables**
- **admin_users:** User authentication and roles
- **audit_logs:** System activity and security logs
- **performance_metrics:** System performance data
- **performance_alerts:** Performance monitoring alerts
- **backup_monitoring:** Backup operation logs

---

---

## 🧪 **BACKUP TESTING PROCEDURES**

### **Weekly Backup Verification**
```sql
-- Run this query weekly to verify backup integrity
SELECT
    table_name,
    row_count,
    last_updated,
    table_size,
    CASE
        WHEN last_updated > NOW() - INTERVAL '7 days' THEN 'Active'
        WHEN last_updated > NOW() - INTERVAL '30 days' THEN 'Stale'
        ELSE 'Inactive'
    END as status
FROM verify_backup_integrity()
ORDER BY row_count DESC;
```

### **Monthly Recovery Test**
```sql
-- Log the start of a recovery test
SELECT log_backup_status(
    'manual',
    'started',
    NULL,
    NULL,
    'Monthly recovery test initiated',
    '{"test_type": "recovery_drill", "test_date": "' || NOW()::date || '"}'
);

-- After test completion, log results
SELECT log_backup_status(
    'manual',
    'completed',
    NULL,
    300, -- 5 minutes
    NULL,
    '{"test_type": "recovery_drill", "success": true, "data_verified": true}'
);
```

---

**Document Version:** 1.0
**Last Updated:** [Date]
**Next Review:** Monthly
**Owner:** Technical Team
**Approved By:** Business Owner

**🚨 REMEMBER: In a disaster, stay calm, follow procedures, and communicate clearly. Every minute counts!**
