import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import styles from '@/styles/admin/ServiceDetails.module.css';

export default function ServiceDetails() {
  const router = useRouter();
  const { id } = router.query;
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [service, setService] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (id && !authLoading && user) {
      loadService();
    }
  }, [id, authLoading, user]);

  const loadService = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/services/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch service details');
      }

      const data = await response.json();
      setService(data.service);
    } catch (error) {
      console.error('Error loading service:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/services/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete service');
      }

      router.push('/admin/services');
    } catch (error) {
      console.error('Error deleting service:', error);
      alert('Failed to delete service: ' + error.message);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatDuration = (minutes) => {
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading service details...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className={styles.errorContainer}>
          <h2>Error Loading Service</h2>
          <p>{error}</p>
          <Link href="/admin/services" className={styles.backButton}>
            ← Back to Services
          </Link>
        </div>
      </AdminLayout>
    );
  }

  if (!service) {
    return (
      <AdminLayout>
        <div className={styles.notFoundContainer}>
          <h2>Service Not Found</h2>
          <p>The service you're looking for doesn't exist.</p>
          <Link href="/admin/services" className={styles.backButton}>
            ← Back to Services
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>{service.name} - Service Details | Ocean Soul Sparkles Admin</title>
        <meta name="description" content={`Details for ${service.name} service`} />
      </Head>

      <div className={styles.serviceDetailsContainer}>
        <header className={styles.header}>
          <div className={styles.breadcrumb}>
            <Link href="/admin/services">Services</Link>
            <span>/</span>
            <span>{service.name}</span>
          </div>
          
          <div className={styles.headerActions}>
            <Link href={`/admin/services/${service.id}/edit`} className={styles.editButton}>
              ✏️ Edit Service
            </Link>
            <button onClick={handleDelete} className={styles.deleteButton}>
              🗑️ Delete
            </button>
            <Link href="/admin/services" className={styles.backButton}>
              ← Back to Services
            </Link>
          </div>
        </header>

        <div className={styles.serviceContent}>
          <div className={styles.mainInfo}>
            <div className={styles.serviceHeader}>
              <h1 className={styles.serviceName}>{service.name}</h1>
              <span className={`${styles.statusBadge} ${styles[service.status]}`}>
                {service.status}
              </span>
            </div>

            {service.description && (
              <div className={styles.description}>
                <h3>Description</h3>
                <p>{service.description}</p>
              </div>
            )}

            <div className={styles.detailsGrid}>
              <div className={styles.detailCard}>
                <h4>Pricing</h4>
                <div className={styles.price}>
                  {service.price ? formatCurrency(service.price) : 'Contact for pricing'}
                </div>
              </div>

              <div className={styles.detailCard}>
                <h4>Duration</h4>
                <div className={styles.duration}>
                  {service.duration ? formatDuration(service.duration) : 'Variable'}
                </div>
              </div>

              <div className={styles.detailCard}>
                <h4>Category</h4>
                <div className={styles.category}>{service.category}</div>
              </div>

              <div className={styles.detailCard}>
                <h4>Total Bookings</h4>
                <div className={styles.bookings}>{service.total_bookings || 0}</div>
              </div>
            </div>

            <div className={styles.metaInfo}>
              <div className={styles.metaItem}>
                <strong>Created:</strong> {new Date(service.created_at).toLocaleDateString()}
              </div>
              <div className={styles.metaItem}>
                <strong>Last Updated:</strong> {new Date(service.updated_at).toLocaleDateString()}
              </div>
              <div className={styles.metaItem}>
                <strong>Service ID:</strong> {service.id}
              </div>
            </div>
          </div>

          <div className={styles.sidebar}>
            <div className={styles.quickActions}>
              <h3>Quick Actions</h3>
              <Link href={`/admin/services/${service.id}/edit`} className={styles.actionButton}>
                Edit Service Details
              </Link>
              <Link href={`/admin/bookings/new?service=${service.id}`} className={styles.actionButton}>
                Create Booking
              </Link>
              <Link href={`/admin/services/${service.id}/analytics`} className={styles.actionButton}>
                View Analytics
              </Link>
            </div>

            <div className={styles.relatedInfo}>
              <h3>Service Visibility</h3>
              <div className={styles.visibilityOptions}>
                <div className={styles.visibilityItem}>
                  <span>Public Website:</span>
                  <span className={service.visible_on_public ? styles.enabled : styles.disabled}>
                    {service.visible_on_public ? 'Visible' : 'Hidden'}
                  </span>
                </div>
                <div className={styles.visibilityItem}>
                  <span>POS System:</span>
                  <span className={service.visible_on_pos ? styles.enabled : styles.disabled}>
                    {service.visible_on_pos ? 'Available' : 'Hidden'}
                  </span>
                </div>
                <div className={styles.visibilityItem}>
                  <span>Events:</span>
                  <span className={service.visible_on_events ? styles.enabled : styles.disabled}>
                    {service.visible_on_events ? 'Available' : 'Hidden'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
