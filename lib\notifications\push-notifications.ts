/**
 * Ocean Soul Sparkles Admin - Push Notifications Manager
 * Handles mobile push notifications for PWA
 */

interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  data?: any;
  actions?: NotificationAction[];
  tag?: string;
  requireInteraction?: boolean;
  silent?: boolean;
  vibrate?: number[];
}

interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

interface PushSubscriptionData {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}

class PushNotificationManager {
  private vapidPublicKey: string = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || '';
  private subscription: PushSubscription | null = null;

  /**
   * Initialize push notifications
   */
  async initialize(): Promise<boolean> {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      console.warn('Push notifications not supported');
      return false;
    }

    try {
      // Request notification permission
      const permission = await this.requestPermission();
      if (permission !== 'granted') {
        console.warn('Notification permission not granted');
        return false;
      }

      // Get service worker registration
      const registration = await navigator.serviceWorker.ready;
      
      // Subscribe to push notifications
      await this.subscribeToPush(registration);
      
      console.log('Push notifications initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
      return false;
    }
  }

  /**
   * Request notification permission
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('Notifications not supported');
    }

    let permission = Notification.permission;

    if (permission === 'default') {
      permission = await Notification.requestPermission();
    }

    return permission;
  }

  /**
   * Subscribe to push notifications
   */
  async subscribeToPush(registration: ServiceWorkerRegistration): Promise<PushSubscription | null> {
    try {
      // Validate VAPID key
      if (!this.vapidPublicKey) {
        console.error('VAPID public key not configured. Please set NEXT_PUBLIC_VAPID_PUBLIC_KEY in environment variables.');
        throw new Error('VAPID public key not configured');
      }

      // Check if already subscribed
      let subscription = await registration.pushManager.getSubscription();

      if (!subscription) {
        console.log('Creating new push subscription with VAPID key:', this.vapidPublicKey.substring(0, 20) + '...');

        // Create new subscription
        subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
        });

        console.log('Push subscription created successfully');
      } else {
        console.log('Using existing push subscription');
      }

      this.subscription = subscription;

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);

      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('InvalidAccessError') || error.message.includes('applicationServerKey')) {
          console.error('Invalid VAPID key. Please check NEXT_PUBLIC_VAPID_PUBLIC_KEY environment variable.');
        } else if (error.message.includes('NotAllowedError')) {
          console.error('Push notifications permission denied by user.');
        } else if (error.message.includes('NotSupportedError')) {
          console.error('Push notifications not supported in this browser.');
        }
      }

      return null;
    }
  }

  /**
   * Send subscription to server
   */
  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    try {
      const subscriptionData: PushSubscriptionData = {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: this.arrayBufferToBase64(subscription.getKey('auth')!)
        }
      };

      const response = await fetch('/api/admin/notifications/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscriptionData)
      });

      if (!response.ok) {
        throw new Error('Failed to send subscription to server');
      }

      console.log('Push subscription sent to server');
    } catch (error) {
      console.error('Failed to send subscription to server:', error);
    }
  }

  /**
   * Show local notification
   */
  async showNotification(payload: NotificationPayload): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      throw new Error('Service worker not supported');
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      
      const options: NotificationOptions = {
        body: payload.body,
        icon: payload.icon || '/icons/icon-192x192.png',
        badge: payload.badge || '/icons/badge-72x72.png',
        data: payload.data,
        tag: payload.tag,
        requireInteraction: payload.requireInteraction || false,
        silent: payload.silent || false
      };

      // Add optional properties that may not be supported in all TypeScript versions
      const extendedOptions = options as any;

      if (payload.image) {
        extendedOptions.image = payload.image;
      }

      if (payload.vibrate) {
        extendedOptions.vibrate = payload.vibrate;
      } else {
        extendedOptions.vibrate = [100, 50, 100];
      }

      if (payload.actions) {
        extendedOptions.actions = payload.actions;
      }

      await registration.showNotification(payload.title, extendedOptions);
    } catch (error) {
      console.error('Failed to show notification:', error);
    }
  }

  /**
   * Send push notification via server
   */
  async sendPushNotification(
    userId: string, 
    payload: NotificationPayload
  ): Promise<boolean> {
    try {
      const response = await fetch('/api/admin/notifications/push/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          payload
        })
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to send push notification:', error);
      return false;
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(): Promise<boolean> {
    if (!this.subscription) {
      return true;
    }

    try {
      const success = await this.subscription.unsubscribe();
      
      if (success) {
        // Notify server about unsubscription
        await fetch('/api/admin/notifications/push/unsubscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            endpoint: this.subscription.endpoint
          })
        });

        this.subscription = null;
        console.log('Unsubscribed from push notifications');
      }

      return success;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  }

  /**
   * Get current subscription status
   */
  async getSubscriptionStatus(): Promise<{
    supported: boolean;
    permission: NotificationPermission;
    subscribed: boolean;
    subscription?: PushSubscription;
  }> {
    const supported = 'serviceWorker' in navigator && 'PushManager' in window;
    const permission = supported ? Notification.permission : 'denied';

    let subscribed = false;
    let subscription: PushSubscription | undefined;

    if (supported && permission === 'granted') {
      try {
        const registration = await navigator.serviceWorker.ready;
        const sub = await registration.pushManager.getSubscription();
        subscription = sub || undefined;
        subscribed = !!subscription;
      } catch (error) {
        console.error('Failed to get subscription status:', error);
      }
    }

    return {
      supported,
      permission,
      subscribed,
      subscription
    };
  }

  /**
   * Predefined notification templates
   */
  getNotificationTemplates() {
    return {
      newBooking: (customerName: string, service: string, time: string): NotificationPayload => ({
        title: 'New Booking Received',
        body: `${customerName} booked ${service} for ${time}`,
        icon: '/icons/booking-notification.png',
        tag: 'new-booking',
        data: { type: 'booking', action: 'view' },
        actions: [
          { action: 'view', title: 'View Booking', icon: '/icons/view.png' },
          { action: 'dismiss', title: 'Dismiss', icon: '/icons/dismiss.png' }
        ],
        vibrate: [100, 50, 100, 50, 100]
      }),

      lowInventory: (productName: string, quantity: number): NotificationPayload => ({
        title: 'Low Inventory Alert',
        body: `${productName} is running low (${quantity} remaining)`,
        icon: '/icons/inventory-alert.png',
        tag: 'low-inventory',
        requireInteraction: true,
        data: { type: 'inventory', product: productName },
        actions: [
          { action: 'reorder', title: 'Reorder', icon: '/icons/reorder.png' },
          { action: 'dismiss', title: 'Dismiss', icon: '/icons/dismiss.png' }
        ],
        vibrate: [200, 100, 200]
      }),

      staffSchedule: (message: string): NotificationPayload => ({
        title: 'Schedule Update',
        body: message,
        icon: '/icons/schedule-notification.png',
        tag: 'staff-schedule',
        data: { type: 'schedule' },
        actions: [
          { action: 'view', title: 'View Schedule', icon: '/icons/calendar.png' },
          { action: 'dismiss', title: 'Dismiss', icon: '/icons/dismiss.png' }
        ]
      }),

      paymentReceived: (amount: string, customer: string): NotificationPayload => ({
        title: 'Payment Received',
        body: `$${amount} payment received from ${customer}`,
        icon: '/icons/payment-notification.png',
        tag: 'payment-received',
        data: { type: 'payment', amount, customer },
        actions: [
          { action: 'view', title: 'View Transaction', icon: '/icons/receipt.png' },
          { action: 'dismiss', title: 'Dismiss', icon: '/icons/dismiss.png' }
        ],
        vibrate: [100, 50, 100]
      })
    };
  }

  /**
   * Utility functions
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }
}

// Export singleton instance
export const pushNotificationManager = new PushNotificationManager();
export default pushNotificationManager;
