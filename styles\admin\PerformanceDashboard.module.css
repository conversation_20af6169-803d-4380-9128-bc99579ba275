/**
 * Ocean Soul Sparkles Admin Dashboard - Performance Dashboard Styles
 */

.container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.header h2 {
  color: #2c3e50;
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.timeRangeSelect {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.autoRefreshLabel {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.refreshButton {
  padding: 8px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.refreshButton:hover {
  background: #2980b9;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
}

.error {
  color: #e74c3c;
}

/* Summary Grid */
.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summaryCard {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3498db;
  transition: transform 0.2s;
}

.summaryCard:hover {
  transform: translateY(-2px);
}

.summaryCard h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summaryValue {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
}

.summaryCard.excellent {
  border-left-color: #27ae60;
}

.summaryCard.good {
  border-left-color: #f39c12;
}

.summaryCard.warning {
  border-left-color: #e67e22;
}

.summaryCard.critical {
  border-left-color: #e74c3c;
}

/* Sections */
.section {
  background: white;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

/* Alerts */
.alertsList {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.alert {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid;
}

.alert.warning {
  background: #fff3cd;
  border-left-color: #ffc107;
}

.alert.critical {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.alertContent {
  flex: 1;
}

.alertMessage {
  font-weight: 600;
  margin-bottom: 8px;
  color: #2c3e50;
}

.alertDetails {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #666;
}

.alertDetails span {
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

.resolveButton {
  padding: 8px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.resolveButton:hover {
  background: #218838;
}

/* Metrics Table */
.metricsTable {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.tableHeader {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr 1fr 1fr 1fr;
  background: #f8f9fa;
  padding: 15px;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 1px solid #e0e0e0;
}

.tableBody {
  max-height: 400px;
  overflow-y: auto;
}

.tableRow {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr 1fr 1fr 1fr;
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.tableRow:hover {
  background: #f8f9fa;
}

.tableRow:last-child {
  border-bottom: none;
}

.slowDuration {
  color: #e67e22;
  font-weight: 600;
}

.errorStatus {
  color: #e74c3c;
  font-weight: 600;
}

/* Tips */
.tips {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.tip {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;
  font-size: 14px;
  line-height: 1.5;
}

.tip strong {
  color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .controls {
    justify-content: center;
  }

  .summaryGrid {
    grid-template-columns: 1fr;
  }

  .tableHeader,
  .tableRow {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .tableHeader {
    display: none; /* Hide header on mobile */
  }

  .tableRow {
    display: block;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 10px;
  }

  .alertDetails {
    flex-direction: column;
    gap: 5px;
  }

  .alert {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .tips {
    grid-template-columns: 1fr;
  }
}

/* Animation for loading states */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}
