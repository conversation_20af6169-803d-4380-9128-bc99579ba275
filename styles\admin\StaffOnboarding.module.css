/* Staff Onboarding Page Styles */

.onboardingContainer {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.headerLeft {
  flex: 1;
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.backBtn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.backBtn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.errorMessage {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.closeError {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emptyState {
  text-align: center;
  padding: 80px 20px;
  background: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
}

.emptyState h3 {
  font-size: 24px;
  color: #374151;
  margin: 0 0 8px 0;
}

.emptyState p {
  color: #6b7280;
  margin: 0 0 24px 0;
  font-size: 16px;
}

.initializeBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.initializeBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.progressSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.progressCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progressCard h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.progressBar {
  width: 100%;
  height: 12px;
  background: #f3f4f6;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progressFill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 6px;
}

.progressStats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #6b7280;
}

.filters {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-bottom: 32px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filterGroup label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
}

.checklistContent {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.categorySection {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.categoryHeader {
  background: #f9fafb;
  padding: 20px 24px;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 12px;
}

.categoryIcon {
  font-size: 24px;
}

.categoryCount {
  font-size: 14px;
  color: #6b7280;
  font-weight: 400;
  margin-left: auto;
}

.checklistItems {
  padding: 0;
}

.checklistItem {
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
}

.checklistItem:last-child {
  border-bottom: none;
}

.checklistItem:hover {
  background: #f9fafb;
}

.checklistItem.completed {
  background: #f0fdf4;
  border-left: 4px solid #10b981;
}

.itemHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.checkboxLabel {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  flex: 1;
}

.checkbox {
  width: 18px;
  height: 18px;
  margin-top: 2px;
  cursor: pointer;
}

.itemTitle {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.5;
  display: flex;
  align-items: center;
  gap: 8px;
}

.requiredBadge {
  background: #fbbf24;
  color: #92400e;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.dueDate {
  font-size: 12px;
  color: #dc2626;
  font-weight: 500;
  white-space: nowrap;
}

.itemDescription {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 8px 0 0 30px;
}

.completionInfo {
  color: #059669;
  font-size: 14px;
  font-weight: 500;
  margin: 8px 0 0 30px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.itemNotes {
  background: #eff6ff;
  border: 1px solid #dbeafe;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0 0 30px;
  font-size: 14px;
  color: #374151;
}

.itemNotes strong {
  color: #1d4ed8;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .onboardingContainer {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .headerActions {
    justify-content: flex-start;
  }

  .progressSection {
    grid-template-columns: 1fr;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .itemHeader {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .dueDate {
    margin-left: 30px;
  }

  .categoryHeader {
    padding: 16px 20px;
    font-size: 18px;
  }

  .checklistItem {
    padding: 16px 20px;
  }

  .itemDescription,
  .completionInfo,
  .itemNotes {
    margin-left: 20px;
  }
}
