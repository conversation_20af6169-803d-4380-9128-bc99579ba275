/* Booking Analytics Styles */

.analyticsContainer {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.analyticsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.analyticsHeader h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.timeRangeSelector {
  display: flex;
  gap: 0.5rem;
  background: #f1f5f9;
  padding: 0.25rem;
  border-radius: 8px;
}

.timeRangeSelector button {
  background: transparent;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeRangeSelector button:hover {
  background: #e2e8f0;
  color: #475569;
}

.timeRangeSelector button.active {
  background: #3b82f6;
  color: white;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metricCard {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.metricValue {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.metricLabel {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.chartCard {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
}

.chartCard h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.statusChart {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.statusItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.statusIndicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.statusLabel {
  flex: 1;
  font-weight: 500;
  color: #374151;
  text-transform: capitalize;
}

.statusCount {
  font-weight: 600;
  color: #1e293b;
  background: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.topList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.topItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.topItem:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.topRank {
  background: #3b82f6;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.topInfo {
  flex: 1;
}

.topName {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.topStats {
  font-size: 0.85rem;
  color: #64748b;
}

.trendChart {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.trendItem {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 1rem;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.trendMonth {
  font-weight: 600;
  color: #1e293b;
}

.trendBookings {
  font-size: 0.9rem;
  color: #64748b;
  text-align: right;
}

.trendRevenue {
  font-weight: 600;
  color: #059669;
  text-align: right;
}

.emptyState {
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.emptyState p {
  font-size: 1.1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analyticsContainer {
    padding: 1rem;
  }

  .analyticsHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .timeRangeSelector {
    justify-content: center;
  }

  .metricsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .metricCard {
    padding: 1rem;
  }

  .metricValue {
    font-size: 1.5rem;
  }

  .chartsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .chartCard {
    padding: 1rem;
  }

  .trendItem {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: center;
  }

  .trendBookings,
  .trendRevenue {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .metricsGrid {
    grid-template-columns: 1fr;
  }

  .timeRangeSelector {
    flex-wrap: wrap;
  }

  .timeRangeSelector button {
    flex: 1;
    min-width: 80px;
  }

  .topItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .topRank {
    align-self: center;
  }
}
