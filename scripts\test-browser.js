const { default: fetch } = await import('node-fetch');

async function simulateBrowserLogin() {
  console.log('🖥️  Simulating browser login flow...\n');
  
  try {
    // Step 1: Login and get cookie
    console.log('1. Logging in to get authentication cookie...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed');
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful!');
    
    // Step 2: Test dashboard page access
    console.log('\n2. Testing dashboard page access...');
    const dashboardPageResponse = await fetch('http://localhost:3001/admin/dashboard', {
      headers: {
        'Cookie': `admin-token=${loginData.token}`
      }
    });

    console.log(`   Status: ${dashboardPageResponse.status}`);
    console.log(`   Content-Type: ${dashboardPageResponse.headers.get('content-type')}`);
    
    if (dashboardPageResponse.ok && dashboardPageResponse.headers.get('content-type')?.includes('text/html')) {
      console.log('✅ Dashboard page loads successfully!');
      
      // Check if the page contains expected content
      const htmlContent = await dashboardPageResponse.text();
      if (htmlContent.includes('Welcome back') || htmlContent.includes('dashboard')) {
        console.log('✅ Dashboard content detected!');
      }
    } else {
      console.log('❌ Dashboard page failed to load properly');
    }

    // Step 3: Test various admin pages
    const pagesToTest = [
      '/admin/bookings',
      '/admin/customers', 
      '/admin/artists',
      '/admin/services',
      '/admin/inventory',
      '/admin/pos'
    ];

    console.log('\n3. Testing access to other admin pages...');
    for (const page of pagesToTest) {
      const pageResponse = await fetch(`http://localhost:3001${page}`, {
        headers: {
          'Cookie': `admin-token=${loginData.token}`
        }
      });
      
      const status = pageResponse.status === 200 ? '✅' : '❌';
      console.log(`   ${status} ${page}: ${pageResponse.status}`);
    }

    console.log('\n🎉 Browser simulation complete!');
    console.log('\n🌐 You can now open http://localhost:3001/admin/login in your browser and login with:');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔐 Password: admin123');
    
  } catch (error) {
    console.error('❌ Simulation failed:', error);
  }
}

simulateBrowserLogin();
