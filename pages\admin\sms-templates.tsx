/**
 * SMS Templates Management Page
 * 
 * Provides interface for managing SMS message templates with live preview,
 * variable substitution, and template categories.
 */

import { useState, useEffect } from 'react'
import { useAuth } from '../../hooks/useAuth'
import AdminLayout from '../../components/admin/AdminLayout'
import SMSTestPanel from '../../components/admin/SMSTestPanel'
import styles from '../../styles/admin/SMSTemplates.module.css'

interface SMSTemplate {
  id: string
  type: string
  name: string
  description: string
  template: string
  variables: string[]
  category: string
  is_active: boolean
  is_default?: boolean
}

export default function SMSTemplatesManagement() {
  const { user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(true)
  const [templates, setTemplates] = useState<SMSTemplate[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<SMSTemplate | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [saving, setSaving] = useState(false)
  const [previewData, setPreviewData] = useState<Record<string, string>>({})
  const [activeCategory, setActiveCategory] = useState('all')
  const [showTestPanel, setShowTestPanel] = useState(false)

  // Form state for editing/creating templates
  const [formData, setFormData] = useState({
    type: '',
    name: '',
    description: '',
    template: '',
    variables: [] as string[],
    category: 'custom',
    is_active: true
  })

  useEffect(() => {
    if (user) {
      fetchTemplates()
    }
  }, [user])

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/sms-templates')
      const data = await response.json()
      
      if (response.ok) {
        setTemplates(data.templates || [])
      } else {
        console.error('Failed to fetch SMS templates:', data.message)
      }
    } catch (error) {
      console.error('Error fetching SMS templates:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTemplateSelect = (template: SMSTemplate) => {
    setSelectedTemplate(template)
    setIsEditing(false)
    setIsCreating(false)
    
    // Initialize preview data with sample values
    const sampleData: Record<string, string> = {}
    template.variables?.forEach(variable => {
      switch (variable) {
        case 'customerName':
        case 'name':
          sampleData[variable] = 'Sarah Johnson'
          break
        case 'serviceName':
          sampleData[variable] = 'Hair Braiding'
          break
        case 'date':
          sampleData[variable] = 'March 15, 2025'
          break
        case 'time':
          sampleData[variable] = '2:30 PM'
          break
        case 'amount':
          sampleData[variable] = '150.00'
          break
        case 'receiptNumber':
          sampleData[variable] = 'OSS-2025-001'
          break
        default:
          sampleData[variable] = `[${variable}]`
      }
    })
    setPreviewData(sampleData)
  }

  const handleEdit = () => {
    if (!selectedTemplate) return
    
    setFormData({
      type: selectedTemplate.type,
      name: selectedTemplate.name,
      description: selectedTemplate.description,
      template: selectedTemplate.template,
      variables: selectedTemplate.variables || [],
      category: selectedTemplate.category,
      is_active: selectedTemplate.is_active
    })
    setIsEditing(true)
    setIsCreating(false)
  }

  const handleCreate = () => {
    setFormData({
      type: '',
      name: '',
      description: '',
      template: '',
      variables: [],
      category: 'custom',
      is_active: true
    })
    setIsCreating(true)
    setIsEditing(false)
    setSelectedTemplate(null)
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      const url = isCreating ? '/api/admin/sms-templates' : '/api/admin/sms-templates'
      const method = isCreating ? 'POST' : 'PUT'
      const body = isCreating ? formData : { ...formData, id: selectedTemplate?.id }
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
      
      const data = await response.json()
      
      if (response.ok) {
        await fetchTemplates()
        setIsEditing(false)
        setIsCreating(false)
        if (data.template) {
          setSelectedTemplate(data.template)
        }
        alert(`SMS template ${isCreating ? 'created' : 'updated'} successfully!`)
      } else {
        alert(`Failed to ${isCreating ? 'create' : 'update'} SMS template: ${data.message}`)
      }
    } catch (error) {
      console.error('Error saving SMS template:', error)
      alert('Error saving SMS template. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async () => {
    if (!selectedTemplate || selectedTemplate.is_default) return
    
    if (!confirm('Are you sure you want to delete this SMS template?')) return
    
    try {
      const response = await fetch(`/api/admin/sms-templates?id=${selectedTemplate.id}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        await fetchTemplates()
        setSelectedTemplate(null)
        alert('SMS template deleted successfully!')
      } else {
        const data = await response.json()
        alert(`Failed to delete SMS template: ${data.message}`)
      }
    } catch (error) {
      console.error('Error deleting SMS template:', error)
      alert('Error deleting SMS template. Please try again.')
    }
  }

  const generatePreview = () => {
    if (!selectedTemplate && !isEditing && !isCreating) return ''
    
    const template = isEditing || isCreating ? formData.template : selectedTemplate?.template || ''
    let preview = template
    
    Object.keys(previewData).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g')
      preview = preview.replace(regex, previewData[key])
    })
    
    return preview
  }

  const extractVariables = (template: string): string[] => {
    const matches = template.match(/{{(\w+)}}/g)
    if (!matches) return []

    const variables = matches.map(match => match.replace(/[{}]/g, ''))
    return Array.from(new Set(variables))
  }

  const handleTemplateChange = (template: string) => {
    const variables = extractVariables(template)
    setFormData(prev => ({ ...prev, template, variables }))
  }

  const categories = ['all', 'booking', 'payment', 'staff', 'customer', 'marketing', 'admin', 'custom']
  const filteredTemplates = activeCategory === 'all' 
    ? templates 
    : templates.filter(t => t.category === activeCategory)

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loading}>Loading SMS templates...</div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={styles.container}>
        <header className={styles.header}>
          <h1 className={styles.title}>SMS Templates</h1>
          <div className={styles.headerActions}>
            <button
              onClick={() => setShowTestPanel(true)}
              className={styles.testBtn}
            >
              Test SMS
            </button>
            <button
              onClick={handleCreate}
              className={styles.createBtn}
            >
              Create Template
            </button>
          </div>
        </header>

        <div className={styles.content}>
          {/* Template List */}
          <div className={styles.templateList}>
            <div className={styles.categoryFilter}>
              {categories.map(category => (
                <button
                  key={category}
                  className={`${styles.categoryBtn} ${activeCategory === category ? styles.active : ''}`}
                  onClick={() => setActiveCategory(category)}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </button>
              ))}
            </div>

            <div className={styles.templates}>
              {filteredTemplates.map(template => (
                <div
                  key={template.id}
                  className={`${styles.templateItem} ${selectedTemplate?.id === template.id ? styles.selected : ''}`}
                  onClick={() => handleTemplateSelect(template)}
                >
                  <div className={styles.templateHeader}>
                    <h3>{template.name}</h3>
                    <span className={`${styles.category} ${styles[template.category]}`}>
                      {template.category}
                    </span>
                  </div>
                  <p className={styles.templateDescription}>{template.description}</p>
                  <div className={styles.templatePreview}>
                    {template.template.substring(0, 100)}...
                  </div>
                  <div className={styles.templateMeta}>
                    <span className={`${styles.status} ${template.is_active ? styles.active : styles.inactive}`}>
                      {template.is_active ? 'Active' : 'Inactive'}
                    </span>
                    {template.is_default && (
                      <span className={styles.defaultBadge}>Default</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Template Editor/Viewer */}
          <div className={styles.templateEditor}>
            {(selectedTemplate || isEditing || isCreating) ? (
              <>
                <div className={styles.editorHeader}>
                  <h2>
                    {isCreating ? 'Create SMS Template' : 
                     isEditing ? 'Edit SMS Template' : 
                     selectedTemplate?.name}
                  </h2>
                  <div className={styles.editorActions}>
                    {!isEditing && !isCreating && (
                      <>
                        <button 
                          onClick={handleEdit}
                          className={styles.editBtn}
                          disabled={selectedTemplate?.is_default}
                        >
                          Edit
                        </button>
                        <button 
                          onClick={handleDelete}
                          className={styles.deleteBtn}
                          disabled={selectedTemplate?.is_default}
                        >
                          Delete
                        </button>
                      </>
                    )}
                    {(isEditing || isCreating) && (
                      <>
                        <button 
                          onClick={() => {
                            setIsEditing(false)
                            setIsCreating(false)
                          }}
                          className={styles.cancelBtn}
                        >
                          Cancel
                        </button>
                        <button 
                          onClick={handleSave}
                          className={styles.saveBtn}
                          disabled={saving}
                        >
                          {saving ? 'Saving...' : 'Save'}
                        </button>
                      </>
                    )}
                  </div>
                </div>

                {(isEditing || isCreating) ? (
                  <div className={styles.editForm}>
                    <div className={styles.formGroup}>
                      <label>Template Type</label>
                      <input
                        type="text"
                        value={formData.type}
                        onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                        placeholder="e.g., booking_confirmation"
                      />
                    </div>
                    <div className={styles.formGroup}>
                      <label>Name</label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Template name"
                      />
                    </div>
                    <div className={styles.formGroup}>
                      <label>Description</label>
                      <input
                        type="text"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Template description"
                      />
                    </div>
                    <div className={styles.formGroup}>
                      <label>Category</label>
                      <select
                        value={formData.category}
                        onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                      >
                        <option value="booking">Booking</option>
                        <option value="payment">Payment</option>
                        <option value="staff">Staff</option>
                        <option value="customer">Customer</option>
                        <option value="marketing">Marketing</option>
                        <option value="admin">Admin</option>
                        <option value="custom">Custom</option>
                      </select>
                    </div>
                    <div className={styles.formGroup}>
                      <label>Template Message</label>
                      <textarea
                        value={formData.template}
                        onChange={(e) => handleTemplateChange(e.target.value)}
                        placeholder="SMS message template with {{variables}}"
                        rows={4}
                      />
                      <small>Use {'{{'} variableName {'}}'}  for dynamic content</small>
                    </div>
                    <div className={styles.formGroup}>
                      <label>
                        <input
                          type="checkbox"
                          checked={formData.is_active}
                          onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                        />
                        Active
                      </label>
                    </div>
                    {formData.variables.length > 0 && (
                      <div className={styles.variablesList}>
                        <h4>Variables:</h4>
                        <div className={styles.variables}>
                          {formData.variables.map(variable => (
                            <span key={variable} className={styles.variable}>
                              {variable}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className={styles.templateView}>
                    <div className={styles.templateDetails}>
                      <p><strong>Type:</strong> {selectedTemplate?.type}</p>
                      <p><strong>Category:</strong> {selectedTemplate?.category}</p>
                      <p><strong>Description:</strong> {selectedTemplate?.description}</p>
                    </div>
                    
                    <div className={styles.templateContent}>
                      <h4>Template:</h4>
                      <div className={styles.templateText}>
                        {selectedTemplate?.template}
                      </div>
                    </div>

                    {selectedTemplate?.variables && selectedTemplate.variables.length > 0 && (
                      <div className={styles.variablesList}>
                        <h4>Variables:</h4>
                        <div className={styles.variables}>
                          {selectedTemplate.variables.map(variable => (
                            <span key={variable} className={styles.variable}>
                              {variable}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Live Preview */}
                <div className={styles.preview}>
                  <h4>Preview:</h4>
                  <div className={styles.previewContent}>
                    {generatePreview()}
                  </div>
                  <div className={styles.previewControls}>
                    <h5>Preview Data:</h5>
                    {(selectedTemplate?.variables || formData.variables).map(variable => (
                      <div key={variable} className={styles.previewInput}>
                        <label>{variable}:</label>
                        <input
                          type="text"
                          value={previewData[variable] || ''}
                          onChange={(e) => setPreviewData(prev => ({ 
                            ...prev, 
                            [variable]: e.target.value 
                          }))}
                          placeholder={`Enter ${variable}`}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </>
            ) : (
              <div className={styles.noSelection}>
                <p>Select a template to view or edit, or create a new one.</p>
              </div>
            )}
          </div>
        </div>

        {/* SMS Test Panel */}
        {showTestPanel && (
          <SMSTestPanel onClose={() => setShowTestPanel(false)} />
        )}
      </div>
    </AdminLayout>
  )
}
