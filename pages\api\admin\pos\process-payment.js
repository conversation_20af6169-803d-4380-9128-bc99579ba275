import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth/admin-auth'

/**
 * API endpoint for processing Square payments in POS terminal
 * This endpoint handles Square payment processing and returns transaction details
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Square payment processing API called`)

  try {
    // Authenticate admin request
    const { user, error: authError } = await authenticateAdminRequest(req)
    if (authError || !user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: authError?.message || 'Authentication failed',
        requestId
      })
    }

    const { sourceId, amountMoney, orderDetails, billingContact } = req.body

    // Validate required fields
    if (!sourceId || !amountMoney) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'sourceId and amountMoney are required',
        requestId
      })
    }

    const paymentAmount = amountMoney.amount
    const paymentCurrency = amountMoney.currency || 'AUD'

    if (!paymentAmount || paymentAmount <= 0) {
      return res.status(400).json({
        error: 'Invalid payment amount',
        message: 'Payment amount must be greater than 0',
        requestId
      })
    }

    console.log(`[${requestId}] Processing Square payment for amount: ${paymentAmount} ${paymentCurrency}`)

    // Check if Square SDK is configured
    const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN
    const squareApplicationId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
    const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID
    const squareEnvironment = process.env.SQUARE_ENVIRONMENT || 'sandbox'

    if (!squareAccessToken || !squareApplicationId || !squareLocationId) {
      console.error(`[${requestId}] Square configuration missing:`, {
        hasAccessToken: !!squareAccessToken,
        hasApplicationId: !!squareApplicationId,
        hasLocationId: !!squareLocationId
      })
      return res.status(500).json({
        error: 'Square configuration missing',
        message: 'Square payment processing is not properly configured',
        requestId
      })
    }

    // Initialize Square Client
    const { Client } = require('square')
    const squareClient = new Client({
      accessToken: squareAccessToken,
      environment: squareEnvironment === 'production' ? 'production' : 'sandbox'
    })

    const paymentsApi = squareClient.paymentsApi

    // Create payment request
    const paymentRequest = {
      sourceId: sourceId,
      idempotencyKey: `pos_${requestId}_${Date.now()}`,
      amountMoney: {
        amount: paymentAmount,
        currency: paymentCurrency
      },
      locationId: squareLocationId,
      note: orderDetails?.service || 'POS Payment',
      buyerEmailAddress: orderDetails?.customerEmail || undefined
    }

    // Add billing contact if provided (for AVS verification)
    if (billingContact) {
      paymentRequest.billingAddress = {
        addressLine1: billingContact.addressLines?.[0],
        addressLine2: billingContact.addressLines?.[1],
        locality: billingContact.locality,
        administrativeDistrictLevel1: billingContact.administrativeDistrictLevel1,
        postalCode: billingContact.postalCode,
        country: billingContact.country
      }
    }

    console.log(`[${requestId}] Making Square API request:`, {
      amount: paymentAmount,
      currency: paymentCurrency,
      locationId: squareLocationId,
      environment: squareEnvironment
    })

    // Process payment with Square
    const { result, statusCode } = await paymentsApi.createPayment(paymentRequest)
    
    console.log(`[${requestId}] Square API response status:`, statusCode)

    if (statusCode === 200 && result.payment) {
      const payment = result.payment
      
      console.log(`[${requestId}] Square payment successful:`, payment.id)

      // Return success response
      return res.status(200).json({
        success: true,
        paymentId: payment.id,
        paymentStatus: payment.status,
        paymentDetails: {
          id: payment.id,
          status: payment.status,
          totalMoney: payment.totalMoney,
          approvedMoney: payment.approvedMoney,
          processingFee: payment.processingFee,
          cardDetails: payment.cardDetails ? {
            status: payment.cardDetails.status,
            card: {
              cardBrand: payment.cardDetails.card?.cardBrand,
              last4: payment.cardDetails.card?.last4,
              cardType: payment.cardDetails.card?.cardType
            },
            avsStatus: payment.cardDetails.avsStatus,
            cvvStatus: payment.cardDetails.cvvStatus
          } : undefined,
          receiptNumber: payment.receiptNumber,
          receiptUrl: payment.receiptUrl,
          createdAt: payment.createdAt,
          updatedAt: payment.updatedAt
        },
        requestId
      })

    } else {
      console.error(`[${requestId}] Square payment failed:`, result)
      
      const errorMessage = result.errors?.[0]?.detail || 'Payment processing failed'
      const errorCode = result.errors?.[0]?.code || 'UNKNOWN_ERROR'

      return res.status(400).json({
        error: 'Payment failed',
        message: errorMessage,
        errorCode: errorCode,
        requestId
      })
    }

  } catch (error) {
    console.error(`[${requestId}] Square payment processing error:`, error)

    // Handle specific Square errors
    if (error.errors && Array.isArray(error.errors)) {
      const squareError = error.errors[0]
      return res.status(400).json({
        error: 'Square payment error',
        message: squareError.detail || 'Payment processing failed',
        errorCode: squareError.code || 'SQUARE_ERROR',
        requestId
      })
    }

    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred during payment processing',
      requestId
    })
  }
}
