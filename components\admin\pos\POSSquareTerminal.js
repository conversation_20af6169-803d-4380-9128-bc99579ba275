/**
 * POSSquareTerminal - Square Terminal hardware payment integration
 * Handles device selection, checkout creation, and payment status monitoring
 */

import { useState, useEffect, useCallback } from 'react'
import styles from '@/styles/admin/POS.module.css'

export default function POSSquareTerminal({ 
  amount, 
  currency = 'AUD', 
  orderDetails, 
  onSuccess, 
  onError, 
  onCancel 
}) {
  const [terminalDevices, setTerminalDevices] = useState([])
  const [selectedDevice, setSelectedDevice] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [checkoutId, setCheckoutId] = useState(null)
  const [checkoutStatus, setCheckoutStatus] = useState(null)
  const [errorMessage, setErrorMessage] = useState('')
  const [loadingDevices, setLoadingDevices] = useState(true)

  // Load terminal devices on component mount
  useEffect(() => {
    loadTerminalDevices()
  }, [])

  // Poll checkout status when checkout is created
  useEffect(() => {
    if (checkoutId && isProcessing) {
      const pollInterval = setInterval(() => {
        pollCheckoutStatus()
      }, 2000) // Poll every 2 seconds

      return () => clearInterval(pollInterval)
    }
  }, [checkoutId, isProcessing])

  const loadTerminalDevices = async () => {
    try {
      setLoadingDevices(true)
      const response = await fetch('/api/admin/pos/terminal-devices', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setTerminalDevices(data.devices || [])
      } else {
        console.error('Failed to load terminal devices')
        setTerminalDevices([])
      }
    } catch (error) {
      console.error('Error loading terminal devices:', error)
      setTerminalDevices([])
    } finally {
      setLoadingDevices(false)
    }
  }

  const createTerminalCheckout = async () => {
    if (!selectedDevice) {
      setErrorMessage('Please select a terminal device')
      return
    }

    try {
      setIsProcessing(true)
      setErrorMessage('')

      const response = await fetch('/api/admin/pos/terminal-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({
          deviceId: selectedDevice.id,
          amountMoney: {
            amount: Math.round(parseFloat(amount) * 100), // Convert to cents
            currency: currency
          },
          note: orderDetails?.service || 'POS Payment',
          orderId: orderDetails?.orderId || `pos_${Date.now()}`,
          paymentOptions: {
            autocomplete: true,
            collectSignature: true,
            allowTipping: false
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create terminal checkout')
      }

      const result = await response.json()
      setCheckoutId(result.checkout.id)
      setCheckoutStatus(result.checkout.status)

      console.log('Terminal checkout created:', result.checkout)

    } catch (error) {
      console.error('Error creating terminal checkout:', error)
      setErrorMessage(error.message)
      setIsProcessing(false)
      onError(error)
    }
  }

  const pollCheckoutStatus = async () => {
    if (!checkoutId) return

    try {
      const response = await fetch(`/api/admin/pos/terminal-checkout?checkoutId=${checkoutId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        const checkout = data.checkout
        setCheckoutStatus(checkout.status)

        console.log('Checkout status update:', checkout.status)

        // Handle completed checkout
        if (checkout.status === 'COMPLETED') {
          setIsProcessing(false)
          onSuccess({
            paymentId: checkout.paymentId,
            paymentStatus: 'COMPLETED',
            paymentDetails: {
              checkoutId: checkout.id,
              amount: checkout.amountMoney.amount / 100,
              currency: checkout.amountMoney.currency,
              transactionId: checkout.paymentId,
              deviceId: checkout.deviceId
            }
          })
        }

        // Handle cancelled or failed checkout
        if (['CANCELED', 'ERROR'].includes(checkout.status)) {
          setIsProcessing(false)
          const errorMsg = checkout.cancelReason || 'Terminal checkout was cancelled or failed'
          setErrorMessage(errorMsg)
          onError(new Error(errorMsg))
        }
      }
    } catch (error) {
      console.error('Error polling checkout status:', error)
    }
  }

  const handleCancel = () => {
    setIsProcessing(false)
    setCheckoutId(null)
    setCheckoutStatus(null)
    onCancel()
  }

  const getStatusMessage = () => {
    switch (checkoutStatus) {
      case 'PENDING':
        return 'Waiting for customer to complete payment on terminal...'
      case 'IN_PROGRESS':
        return 'Payment in progress on terminal...'
      case 'COMPLETED':
        return 'Payment completed successfully!'
      case 'CANCELED':
        return 'Payment was cancelled'
      case 'ERROR':
        return 'Payment failed'
      default:
        return 'Preparing terminal checkout...'
    }
  }

  const getStatusIcon = () => {
    switch (checkoutStatus) {
      case 'PENDING':
      case 'IN_PROGRESS':
        return '⏳'
      case 'COMPLETED':
        return '✅'
      case 'CANCELED':
      case 'ERROR':
        return '❌'
      default:
        return '📱'
    }
  }

  if (loadingDevices) {
    return (
      <div className={styles.terminalPaymentContainer}>
        <div className={styles.loadingState}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading terminal devices...</p>
        </div>
      </div>
    )
  }

  if (terminalDevices.length === 0) {
    return (
      <div className={styles.terminalPaymentContainer}>
        <div className={styles.noDevicesState}>
          <div className={styles.noDevicesIcon}>📱</div>
          <h3>No Terminal Devices Available</h3>
          <p>No paired Square Terminal devices found. Please pair a device first.</p>
          <button 
            className={styles.refreshButton}
            onClick={loadTerminalDevices}
          >
            Refresh Devices
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.terminalPaymentContainer}>
      <div className={styles.terminalHeader}>
        <h3>Square Terminal Payment</h3>
        <div className={styles.paymentAmount}>
          ${parseFloat(amount || 0).toFixed(2)} {currency}
        </div>
      </div>

      {!isProcessing && (
        <div className={styles.deviceSelection}>
          <h4>Select Terminal Device</h4>
          <div className={styles.deviceList}>
            {terminalDevices.map((device) => (
              <div
                key={device.id}
                className={`${styles.deviceCard} ${
                  selectedDevice?.id === device.id ? styles.selected : ''
                }`}
                onClick={() => setSelectedDevice(device)}
              >
                <div className={styles.deviceIcon}>📱</div>
                <div className={styles.deviceInfo}>
                  <div className={styles.deviceName}>
                    {device.name || `Terminal ${device.id.slice(-4)}`}
                  </div>
                  <div className={styles.deviceStatus}>
                    🟢 {device.status}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {isProcessing && (
        <div className={styles.processingState}>
          <div className={styles.statusIcon}>{getStatusIcon()}</div>
          <div className={styles.statusMessage}>{getStatusMessage()}</div>
          
          {checkoutStatus === 'PENDING' && (
            <div className={styles.terminalInstructions}>
              <p>Customer should now see the payment screen on the terminal device.</p>
              <p>They can insert their card, tap for contactless, or use mobile payment.</p>
            </div>
          )}
        </div>
      )}

      {errorMessage && (
        <div className={styles.errorMessage}>
          <div className={styles.errorIcon}>⚠️</div>
          <div className={styles.errorText}>{errorMessage}</div>
        </div>
      )}

      <div className={styles.terminalActions}>
        {!isProcessing ? (
          <button
            className={styles.startPaymentButton}
            onClick={createTerminalCheckout}
            disabled={!selectedDevice}
          >
            Start Terminal Payment
          </button>
        ) : (
          <button
            className={styles.cancelButton}
            onClick={handleCancel}
          >
            Cancel Payment
          </button>
        )}
      </div>
    </div>
  )
}
