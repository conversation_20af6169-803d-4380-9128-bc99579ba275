/**
 * Ocean Soul Sparkles Admin Dashboard - Common Types
 * Shared types used across multiple domains
 */

// Base types defined locally to avoid circular dependencies
export type ID = string;
export type Timestamp = string; // ISO 8601 format
export type Currency = number; // Always in cents/smallest unit
export type Percentage = number; // 0-100
export type Email = string;
export type PhoneNumber = string;
export type URL = string;

// Common status types
export type Status = 'active' | 'inactive' | 'pending' | 'suspended';
export type BookingStatus = 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded' | 'partially_refunded';
export type UserRole = 'Admin' | 'Manager' | 'Artist' | 'Braider' | 'Staff' | 'DEV';

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter and sort types
export interface FilterParams {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  [key: string]: any;
}

export interface SortParams {
  field: string;
  direction: 'asc' | 'desc';
}

// Address and location types
export interface Address {
  street: string;
  suburb: string;
  city: string;
  state: string;
  postcode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

// Contact information
export interface ContactInfo {
  email?: string;
  phone?: string;
  mobile?: string;
  alternatePhone?: string;
  preferredContact: 'email' | 'phone' | 'sms';
}

// File and media types
export interface MediaFile {
  id: ID;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  alt?: string;
  caption?: string;
  uploadedBy: ID;
  uploadedAt: Timestamp;
}

export interface ImageGallery {
  primary: MediaFile;
  gallery: MediaFile[];
  maxImages?: number;
}

// Money and pricing types
export interface Money {
  amount: number; // In cents
  currency: string; // ISO 4217 code
  formatted?: string; // Display format
}

export interface PriceRange {
  min: Money;
  max: Money;
}

export interface Discount {
  type: 'percentage' | 'fixed_amount';
  value: number;
  description?: string;
  conditions?: string[];
}

// Time and scheduling types
export interface TimeSlot {
  start: string; // HH:MM format
  end: string; // HH:MM format
  duration: number; // Minutes
}

export interface BusinessHours {
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
}

export interface DateRange {
  start: string; // ISO date
  end: string; // ISO date
}

// User and authentication types
export interface UserProfile {
  id: ID;
  email: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  avatar?: MediaFile;
  role: UserRole;
  permissions: string[];
  isActive: boolean;
  lastLoginAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface AuthToken {
  token: string;
  type: 'Bearer';
  expiresAt: Timestamp;
  refreshToken?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
  mfaCode?: string;
}

// Notification preferences
export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  inApp: boolean;
  categories: {
    bookings: boolean;
    payments: boolean;
    marketing: boolean;
    system: boolean;
  };
}

// Analytics and metrics
export interface MetricValue {
  current: number;
  previous?: number;
  change?: number;
  changePercent?: number;
  trend: 'up' | 'down' | 'stable';
}

export interface KPI {
  name: string;
  value: MetricValue;
  unit?: string;
  target?: number;
  description?: string;
  color?: string;
}

// Location and geography
export interface Location {
  name: string;
  address: Address;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  timezone: string;
  isDefault?: boolean;
}

// Social media and external links
export interface SocialLinks {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
  tiktok?: string;
  website?: string;
}

// Rating and review types
export interface Rating {
  value: number; // 1-5
  maxValue: number;
  count: number;
  distribution?: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export interface Review {
  id: ID;
  rating: number;
  title?: string;
  comment: string;
  reviewerName: string;
  reviewerEmail?: string;
  isVerified: boolean;
  isPublic: boolean;
  createdAt: Timestamp;
  response?: {
    comment: string;
    respondedBy: ID;
    respondedAt: Timestamp;
  };
}

// Tags and categories
export interface Tag {
  id: ID;
  name: string;
  slug: string;
  color?: string;
  description?: string;
  count?: number;
}

export interface Category {
  id: ID;
  name: string;
  slug: string;
  description?: string;
  parentId?: ID;
  children?: Category[];
  image?: MediaFile;
  isActive: boolean;
  sortOrder: number;
}

// SEO and metadata
export interface SEOMetadata {
  title?: string;
  description?: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: MediaFile;
  canonicalUrl?: string;
  noIndex?: boolean;
  noFollow?: boolean;
}

// Custom fields and attributes
export interface CustomField {
  key: string;
  label: string;
  type: 'text' | 'number' | 'boolean' | 'date' | 'select' | 'multiselect';
  value: any;
  options?: string[]; // For select/multiselect
  required?: boolean;
  validation?: string; // Regex pattern
}

// Bulk operations
export interface BulkOperation {
  action: string;
  ids: ID[];
  data?: Record<string, any>;
  filters?: Record<string, any>;
}

export interface BulkOperationResult {
  success: ID[];
  failed: Array<{
    id: ID;
    error: string;
  }>;
  total: number;
  successCount: number;
  failedCount: number;
}

// Export and import types
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf' | 'json';
  fields?: string[];
  filters?: Record<string, any>;
  dateRange?: DateRange;
  includeHeaders?: boolean;
}

export interface ImportResult {
  totalRows: number;
  successRows: number;
  failedRows: number;
  errors: Array<{
    row: number;
    field?: string;
    message: string;
  }>;
  warnings: Array<{
    row: number;
    field?: string;
    message: string;
  }>;
}

// System health and status
export interface SystemStatus {
  status: 'healthy' | 'degraded' | 'down';
  services: {
    database: 'up' | 'down';
    api: 'up' | 'down';
    storage: 'up' | 'down';
    email: 'up' | 'down';
    sms: 'up' | 'down';
    payments: 'up' | 'down';
  };
  lastChecked: Timestamp;
  uptime: number; // Seconds
}

// Feature flags and configuration
export interface FeatureFlag {
  key: string;
  name: string;
  description?: string;
  enabled: boolean;
  rolloutPercentage?: number;
  conditions?: Record<string, any>;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Generic response wrapper - moved to api.ts to avoid conflicts
