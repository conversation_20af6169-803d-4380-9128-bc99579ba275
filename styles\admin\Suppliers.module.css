/* Supplier Management Styles */

.supplierManagement {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.headerContent {
  flex: 1;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  font-family: 'Inter', sans-serif;
}

.subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.addBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.addBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Controls */
.controls {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  align-items: center;
}

.searchSection {
  flex: 1;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters {
  display: flex;
  gap: 1rem;
}

.filterSelect,
.sortSelect {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filterSelect:focus,
.sortSelect:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Suppliers Container */
.suppliersContainer {
  min-height: 400px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyState {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.emptyState h3 {
  font-size: 1.5rem;
  color: #374151;
  margin-bottom: 0.5rem;
}

.emptyState p {
  font-size: 1rem;
  margin-bottom: 2rem;
}

.addFirstBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  display: inline-block;
  transition: all 0.3s ease;
}

.addFirstBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Suppliers Grid */
.suppliersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.supplierCard {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.supplierCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.cardHeader {
  margin-bottom: 1rem;
}

.supplierInfo {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.supplierName {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  flex: 1;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusBadge.active {
  background: #dcfce7;
  color: #166534;
}

.statusBadge.inactive {
  background: #fee2e2;
  color: #991b1b;
}

.cardBody {
  margin-bottom: 1.5rem;
}

.contactInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.businessInfo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin: 1rem 0;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.value {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.notes {
  margin-top: 1rem;
  padding: 1rem;
  background: #f1f5f9;
  border-radius: 8px;
}

.notesText {
  font-size: 0.875rem;
  color: #475569;
  margin: 0.5rem 0 0 0;
  line-height: 1.5;
}

.cardFooter {
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
}

.cardActions {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.viewBtn,
.editBtn {
  flex: 1;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
}

.viewBtn {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.viewBtn:hover {
  background: #e2e8f0;
  color: #334155;
}

.editBtn {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.editBtn:hover {
  background: #fde68a;
  color: #78350f;
}

.deleteBtn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
  cursor: pointer;
  transition: all 0.3s ease;
}

.deleteBtn:hover:not(:disabled) {
  background: #fecaca;
  color: #7f1d1d;
}

.deleteBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.cardMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.createdDate {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .suppliersGrid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .supplierManagement {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .controls {
    flex-direction: column;
    gap: 1rem;
  }

  .filters {
    flex-direction: column;
    gap: 0.75rem;
  }

  .suppliersGrid {
    grid-template-columns: 1fr;
  }

  .businessInfo {
    grid-template-columns: 1fr;
  }

  .cardActions {
    flex-direction: column;
  }

  .supplierInfo {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.5rem;
  }

  .supplierCard {
    padding: 1rem;
  }

  .contactInfo {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* Form Styles */
.supplierForm {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup.fullWidth {
  grid-column: 1 / -1;
}

.formLabel {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.formInput,
.formTextarea,
.formSelect {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.formInput:focus,
.formTextarea:focus,
.formSelect:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.formTextarea {
  resize: vertical;
  min-height: 100px;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.saveBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.saveBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.cancelBtn {
  background: #f8fafc;
  color: #64748b;
  border: 2px solid #e2e8f0;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelBtn:hover {
  background: #f1f5f9;
  color: #475569;
}
