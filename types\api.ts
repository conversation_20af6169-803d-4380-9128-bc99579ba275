/**
 * Ocean Soul Sparkles Admin Dashboard - API Types
 * Request/Response types for all API endpoints
 */

import { NextApiRequest, NextApiResponse } from 'next';
import {
  ID,
  Timestamp,
  UserRole,
  PaginationParams,
  PaginatedResponse,
  FilterParams,
  SortParams
} from './common';
import { Customer, CustomerSearchParams } from './customer';
import { Booking, BookingSearchParams } from './booking';
import { Service, ServiceSearchParams } from './service';

// Base API types
export interface ApiRequest extends NextApiRequest {
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
    field?: string;
  };
  meta?: {
    requestId: string;
    timestamp: Timestamp;
    version: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

export type ApiHandler<T = any> = (
  req: ApiRequest,
  res: NextApiResponse<ApiResponse<T>>
) => Promise<void> | void;

// Authentication API types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
  mfaCode?: string;
}

export interface LoginResponse {
  user: {
    id: ID;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    permissions: string[];
    lastLoginAt?: Timestamp;
  };
  token: string;
  refreshToken?: string;
  expiresAt: Timestamp;
  mfaRequired?: boolean;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  expiresAt: Timestamp;
}

export interface LogoutRequest {
  token: string;
}

// Customer API types
export interface GetCustomersRequest {
  query: CustomerSearchParams & PaginationParams & {
    sort?: string;
    order?: 'asc' | 'desc';
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface GetCustomersResponse extends PaginatedResponse<Customer> {}

export interface GetCustomerRequest {
  query: {
    id: ID;
    include?: string[]; // Related data to include
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface GetCustomerResponse {
  customer: Customer;
  bookings?: Booking[];
  totalSpent?: number;
  lastVisit?: Timestamp;
}

export interface CreateCustomerRequest extends ApiRequest {
  body: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    mobile?: string;
    dateOfBirth?: string;
    address?: any;
    notes?: string;
    tags?: string[];
    marketingConsent?: boolean;
  };
}

export interface CreateCustomerResponse {
  customer: Customer;
}

export interface UpdateCustomerRequest {
  query: { id: ID };
  body: Partial<CreateCustomerRequest['body']> & {
    status?: 'active' | 'inactive';
    isVip?: boolean;
    isBlacklisted?: boolean;
    blacklistReason?: string;
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface UpdateCustomerResponse {
  customer: Customer;
}

export interface DeleteCustomerRequest {
  query: { id: ID };
  body?: { reason?: string };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface DeleteCustomerResponse {
  success: boolean;
  message: string;
}

// Booking API types
export interface GetBookingsRequest {
  query: BookingSearchParams & PaginationParams & {
    sort?: string;
    order?: 'asc' | 'desc';
    view?: 'calendar' | 'list';
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface GetBookingsResponse extends PaginatedResponse<Booking> {}

export interface GetBookingRequest {
  query: {
    id: ID;
    include?: string[];
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface GetBookingResponse {
  booking: Booking;
  customer?: Customer;
  service?: Service;
  artist?: any;
  payments?: any[];
}

export interface CreateBookingRequest extends ApiRequest {
  body: {
    customerId: ID;
    serviceId: ID;
    artistId: ID;
    bookingDate: string;
    startTime: string;
    endTime: string;
    location?: 'studio' | 'mobile' | 'client_home';
    notes?: string;
    specialRequirements?: string[];
    tierName?: string;
    tierPrice?: number;
  };
}

export interface CreateBookingResponse {
  booking: Booking;
  conflicts?: any[];
}

export interface UpdateBookingRequest extends ApiRequest {
  query: { id: ID };
  body: Partial<CreateBookingRequest['body']> & {
    status?: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
    paymentStatus?: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
    totalAmount?: number;
    cancellationReason?: string;
    internalNotes?: string;
  };
}

export interface UpdateBookingResponse {
  booking: Booking;
}

// Service API types
export interface GetServicesRequest {
  query: ServiceSearchParams & PaginationParams & {
    sort?: string;
    order?: 'asc' | 'desc';
    include?: string[];
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface GetServicesResponse extends PaginatedResponse<Service> {}

export interface GetServiceRequest {
  query: {
    id: ID;
    include?: string[];
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface GetServiceResponse {
  service: Service;
  tiers?: any[];
  addons?: any[];
  reviews?: any[];
  analytics?: any;
}

export interface CreateServiceRequest extends ApiRequest {
  body: {
    name: string;
    description: string;
    categoryId?: ID;
    basePrice: number;
    duration: number;
    isActive?: boolean;
    isBookable?: boolean;
    tags?: string[];
  };
}

export interface CreateServiceResponse {
  service: Service;
}

// Dashboard API types
export interface GetDashboardRequest {
  query?: {
    period?: 'today' | 'week' | 'month' | 'year';
    artistId?: ID;
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface GetDashboardResponse {
  stats: {
    totalBookings: number;
    totalRevenue: number;
    activeCustomers: number;
    pendingBookings: number;
    completedBookings: number;
    cancelledBookings: number;
    averageBookingValue: number;
    monthlyGrowth: number;
  };
  recentBookings: Booking[];
  recentActivity: Array<{
    id: ID;
    type: string;
    description: string;
    timestamp: Timestamp;
    userId?: ID;
    userName?: string;
  }>;
  artistStats?: {
    totalBookings: number;
    totalRevenue: number;
    averageRating: number;
    upcomingBookings: number;
  };
  systemStats?: {
    uptime: number;
    responseTime: number;
    errorRate: number;
  };
}

// Search API types
export interface SearchRequest {
  query: {
    q: string;
    types?: string[]; // customer, booking, service, etc.
    limit?: number;
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface SearchResponse {
  results: Array<{
    id: ID;
    type: 'customer' | 'booking' | 'service' | 'product' | 'staff';
    title: string;
    subtitle: string;
    description?: string;
    url: string;
    metadata?: Record<string, any>;
  }>;
  total: number;
  query: string;
}

// Analytics API types
export interface GetAnalyticsRequest {
  query: {
    type: 'bookings' | 'revenue' | 'customers' | 'services' | 'artists';
    period: 'day' | 'week' | 'month' | 'quarter' | 'year';
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
    filters?: Record<string, any>;
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface GetAnalyticsResponse {
  data: Array<{
    date: string;
    value: number;
    label?: string;
    metadata?: Record<string, any>;
  }>;
  summary: {
    total: number;
    average: number;
    change: number;
    changePercent: number;
    trend: 'up' | 'down' | 'stable';
  };
  period: string;
  filters: Record<string, any>;
}

// File upload API types
export interface UploadFileRequest {
  body: FormData;
  query?: {
    type?: 'image' | 'document' | 'avatar';
    resize?: boolean;
    maxWidth?: number;
    maxHeight?: number;
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface UploadFileResponse {
  file: {
    id: ID;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    url: string;
    thumbnailUrl?: string;
  };
}

// Bulk operations API types
export interface BulkOperationRequest extends ApiRequest {
  body: {
    action: 'delete' | 'update' | 'export';
    ids?: ID[];
    filters?: Record<string, any>;
    data?: Record<string, any>;
    options?: Record<string, any>;
  };
}

export interface BulkOperationResponse {
  success: ID[];
  failed: Array<{
    id: ID;
    error: string;
  }>;
  total: number;
  successCount: number;
  failedCount: number;
  downloadUrl?: string; // For export operations
}

// Settings API types
export interface GetSettingsRequest {
  query?: {
    category?: string;
    keys?: string[];
  };
  user?: {
    id: ID;
    email: string;
    role: UserRole;
    permissions: string[];
  };
}

export interface GetSettingsResponse {
  settings: Record<string, any>;
}

export interface UpdateSettingsRequest extends ApiRequest {
  body: {
    settings: Record<string, any>;
  };
}

export interface UpdateSettingsResponse {
  settings: Record<string, any>;
  updated: string[];
}

// Error response types
export interface ValidationErrorResponse {
  success: false;
  error: {
    code: 'VALIDATION_ERROR';
    message: string;
    details: Array<{
      field: string;
      message: string;
      value?: any;
    }>;
  };
}

export interface AuthErrorResponse {
  success: false;
  error: {
    code: 'UNAUTHORIZED' | 'FORBIDDEN' | 'TOKEN_EXPIRED';
    message: string;
  };
}

export interface NotFoundErrorResponse {
  success: false;
  error: {
    code: 'NOT_FOUND';
    message: string;
    resource?: string;
    id?: ID;
  };
}

export interface ServerErrorResponse {
  success: false;
  error: {
    code: 'INTERNAL_ERROR' | 'DATABASE_ERROR' | 'EXTERNAL_SERVICE_ERROR';
    message: string;
    details?: any;
  };
  meta: {
    requestId: string;
    timestamp: Timestamp;
  };
}

// Webhook types
export interface WebhookPayload {
  event: string;
  data: any;
  timestamp: Timestamp;
  signature: string;
}

export interface WebhookResponse {
  received: boolean;
  processed?: boolean;
  error?: string;
}
