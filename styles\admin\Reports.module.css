/* Reports Management Page Styles */

.reportsContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.dateRangeSelect {
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.exportBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.exportBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.reportsContent {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 80px);
}

.tabNavigation {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  gap: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  overflow-x: auto;
}

.tabButton {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  white-space: nowrap;
}

.tabButton:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tabButton.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 600;
}

.tabContent {
  flex: 1;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.overviewSection {
  margin-bottom: 2rem;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metricCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.metricCard h3 {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0 0 0.5rem 0;
}

.metricValue {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.metricChange {
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

.revenueSection,
.bookingsSection,
.customersSection {
  margin-bottom: 2rem;
}

.revenueSection h2,
.bookingsSection h2,
.customersSection h2 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
}

.chartPlaceholder {
  background: rgba(248, 250, 252, 0.5);
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
  padding: 3rem;
  text-align: center;
  color: #64748b;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.serviceRevenueList {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.serviceRevenueList h3 {
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.serviceRevenueItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.serviceRevenueItem:last-child {
  border-bottom: none;
}

.serviceName {
  font-weight: 500;
  color: #374151;
}

.serviceAmount {
  font-weight: 600;
  color: #1e293b;
}

.servicePercentage {
  color: #64748b;
  font-size: 0.875rem;
}

.bookingStats,
.customerStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.statCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.statCard h3 {
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.statusItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.statusItem:last-child {
  border-bottom: none;
}

.statusName {
  font-weight: 500;
  color: #374151;
}

.statusCount {
  font-weight: 600;
  color: #1e293b;
}

.statusPercentage {
  color: #64748b;
  font-size: 0.875rem;
}

.cancellationRate {
  font-size: 2rem;
  font-weight: 700;
  color: #ef4444;
  text-align: center;
}

.clvValue {
  font-size: 2rem;
  font-weight: 700;
  color: #10b981;
  text-align: center;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #64748b;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.accessDenied {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #64748b;
}

.accessDenied h2 {
  color: #1e293b;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .headerActions {
    flex-direction: column;
  }

  .tabNavigation {
    padding: 1rem;
  }

  .tabContent {
    padding: 1rem;
  }

  .metricsGrid {
    grid-template-columns: 1fr;
  }

  .bookingStats,
  .customerStats {
    grid-template-columns: 1fr;
  }

  .chartPlaceholder {
    padding: 2rem 1rem;
  }
}
