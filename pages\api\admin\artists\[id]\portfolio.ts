import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const requestId = uuidv4();
  const { id: artistId } = req.query;
  
  try {
    // Authentication check
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid authentication token required',
        requestId
      });
    }

    // Verify artist exists
    const { data: artist, error: artistError } = await supabase
      .from('artist_profiles')
      .select('id, name, email')
      .eq('id', artistId)
      .single();

    if (artistError || !artist) {
      return res.status(404).json({
        error: 'Artist not found',
        message: 'The specified artist does not exist',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { category, is_featured, is_public, limit = 50, offset = 0 } = req.query;

      let query = supabase
        .from('artist_portfolio_items')
        .select(`
          id,
          artist_id,
          title,
          description,
          category,
          image_url,
          thumbnail_url,
          tags,
          is_featured,
          is_public,
          display_order,
          work_date,
          customer_consent,
          created_at,
          updated_at
        `)
        .eq('artist_id', artistId)
        .order('display_order', { ascending: true })
        .order('created_at', { ascending: false });

      // Apply filters
      if (category) {
        query = query.eq('category', category);
      }
      if (is_featured !== undefined) {
        query = query.eq('is_featured', is_featured === 'true');
      }
      if (is_public !== undefined) {
        query = query.eq('is_public', is_public === 'true');
      }

      // Apply pagination
      query = query.range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1);

      const { data: portfolioItems, error } = await query;

      if (error) {
        console.error('Portfolio fetch error:', error);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to fetch portfolio items',
          requestId
        });
      }

      // Get total count for pagination
      const { count: totalCount } = await supabase
        .from('artist_portfolio_items')
        .select('*', { count: 'exact', head: true })
        .eq('artist_id', artistId);

      // Get portfolio statistics
      const categorySet = new Set(portfolioItems?.map(item => item.category) || []);
      const stats = {
        totalItems: portfolioItems?.length || 0,
        featuredItems: portfolioItems?.filter(item => item.is_featured).length || 0,
        publicItems: portfolioItems?.filter(item => item.is_public).length || 0,
        categories: Array.from(categorySet) as string[],
        lastUpdated: portfolioItems?.[0]?.updated_at || null
      };

      return res.status(200).json({
        artist,
        portfolioItems: portfolioItems || [],
        stats,
        pagination: {
          total: totalCount || 0,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: (parseInt(offset as string) + parseInt(limit as string)) < (totalCount || 0)
        },
        requestId
      });
    }

    if (req.method === 'POST') {
      const portfolioData = req.body;

      // Validate required fields
      if (!portfolioData.title || !portfolioData.category || !portfolioData.image_url) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Missing required fields: title, category, image_url',
          requestId
        });
      }

      // Get next display order if not provided
      let displayOrder = portfolioData.display_order || 0;
      if (!portfolioData.display_order) {
        const { data: maxOrder } = await supabase
          .from('artist_portfolio_items')
          .select('display_order')
          .eq('artist_id', artistId)
          .order('display_order', { ascending: false })
          .limit(1);

        if (maxOrder && maxOrder.length > 0) {
          displayOrder = (maxOrder[0].display_order || 0) + 1;
        }
      }

      const newPortfolioItem = {
        id: uuidv4(),
        artist_id: artistId,
        title: portfolioData.title,
        description: portfolioData.description || null,
        category: portfolioData.category,
        image_url: portfolioData.image_url,
        thumbnail_url: portfolioData.thumbnail_url || null,
        tags: portfolioData.tags || [],
        is_featured: portfolioData.is_featured || false,
        is_public: portfolioData.is_public !== false, // Default to true
        display_order: displayOrder,
        work_date: portfolioData.work_date || null,
        customer_consent: portfolioData.customer_consent || false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: createdItem, error: createError } = await supabase
        .from('artist_portfolio_items')
        .insert([newPortfolioItem])
        .select()
        .single();

      if (createError) {
        console.error('Portfolio creation error:', createError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to create portfolio item',
          requestId
        });
      }

      return res.status(201).json({
        portfolioItem: createdItem,
        message: 'Portfolio item created successfully',
        requestId
      });
    }

    if (req.method === 'PUT') {
      const { item_id } = req.query;
      const updateData = req.body;

      if (!item_id) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Portfolio item ID is required',
          requestId
        });
      }

      // Verify portfolio item exists and belongs to this artist
      const { data: existingItem, error: fetchError } = await supabase
        .from('artist_portfolio_items')
        .select('*')
        .eq('id', item_id)
        .eq('artist_id', artistId)
        .single();

      if (fetchError || !existingItem) {
        return res.status(404).json({
          error: 'Portfolio item not found',
          message: 'The specified portfolio item does not exist for this artist',
          requestId
        });
      }

      const updatedData = {
        ...updateData,
        updated_at: new Date().toISOString()
      };

      const { data: updatedItem, error: updateError } = await supabase
        .from('artist_portfolio_items')
        .update(updatedData)
        .eq('id', item_id)
        .eq('artist_id', artistId)
        .select()
        .single();

      if (updateError) {
        console.error('Portfolio update error:', updateError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to update portfolio item',
          requestId
        });
      }

      return res.status(200).json({
        portfolioItem: updatedItem,
        message: 'Portfolio item updated successfully',
        requestId
      });
    }

    if (req.method === 'DELETE') {
      const { item_id } = req.query;

      if (!item_id) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Portfolio item ID is required',
          requestId
        });
      }

      // Verify portfolio item exists and belongs to this artist
      const { data: existingItem, error: fetchError } = await supabase
        .from('artist_portfolio_items')
        .select('id, title')
        .eq('id', item_id)
        .eq('artist_id', artistId)
        .single();

      if (fetchError || !existingItem) {
        return res.status(404).json({
          error: 'Portfolio item not found',
          message: 'The specified portfolio item does not exist for this artist',
          requestId
        });
      }

      const { error: deleteError } = await supabase
        .from('artist_portfolio_items')
        .delete()
        .eq('id', item_id)
        .eq('artist_id', artistId);

      if (deleteError) {
        console.error('Portfolio deletion error:', deleteError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to delete portfolio item',
          requestId
        });
      }

      return res.status(200).json({
        message: 'Portfolio item deleted successfully',
        deletedItem: existingItem,
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      message: `HTTP method ${req.method} is not supported`,
      requestId
    });

  } catch (error) {
    console.error('Portfolio API error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      requestId
    });
  }
}
