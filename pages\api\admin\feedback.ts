import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuth } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Customer feedback API called - ${req.method}`);

  try {
    // Authenticate admin request
    const authResult = await verifyAdminAuth(req);
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Authentication required',
        message: authResult.message || 'Authentication failed',
        requestId
      });
    }

    const { user } = authResult;

    if (req.method === 'GET') {
      const { 
        customer_id, 
        artist_id, 
        rating_min, 
        rating_max,
        is_public,
        limit = '50', 
        offset = '0' 
      } = req.query;

      let query = supabase
        .from('customer_feedback')
        .select(`
          id,
          customer_id,
          booking_id,
          artist_id,
          rating,
          service_rating,
          cleanliness_rating,
          timeliness_rating,
          overall_experience_rating,
          feedback_text,
          would_recommend,
          improvement_suggestions,
          is_public,
          response_text,
          responded_at,
          created_at,
          customers!inner(
            id,
            first_name,
            last_name,
            email
          ),
          bookings(
            id,
            start_time,
            services(name)
          ),
          artist_profiles(
            id,
            name,
            artist_name
          )
        `)
        .order('created_at', { ascending: false })
        .range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1);

      // Apply filters
      if (customer_id) {
        query = query.eq('customer_id', customer_id);
      }
      if (artist_id) {
        query = query.eq('artist_id', artist_id);
      }
      if (rating_min) {
        query = query.gte('rating', parseInt(rating_min as string));
      }
      if (rating_max) {
        query = query.lte('rating', parseInt(rating_max as string));
      }
      if (is_public !== undefined) {
        query = query.eq('is_public', is_public === 'true');
      }

      const { data: feedback, error } = await query;

      if (error) {
        console.error(`[${requestId}] Database error:`, error);
        return res.status(500).json({
          error: 'Failed to fetch feedback',
          message: error.message,
          requestId
        });
      }

      // Get total count for pagination
      let countQuery = supabase
        .from('customer_feedback')
        .select('*', { count: 'exact', head: true });

      if (customer_id) countQuery = countQuery.eq('customer_id', customer_id);
      if (artist_id) countQuery = countQuery.eq('artist_id', artist_id);
      if (rating_min) countQuery = countQuery.gte('rating', parseInt(rating_min as string));
      if (rating_max) countQuery = countQuery.lte('rating', parseInt(rating_max as string));
      if (is_public !== undefined) countQuery = countQuery.eq('is_public', is_public === 'true');

      const { count: totalCount } = await countQuery;

      // Calculate average ratings
      const { data: avgRatings } = await supabase
        .from('customer_feedback')
        .select('rating, service_rating, cleanliness_rating, timeliness_rating, overall_experience_rating');

      let averages = {
        overall: 0,
        service: 0,
        cleanliness: 0,
        timeliness: 0,
        experience: 0
      };

      if (avgRatings && avgRatings.length > 0) {
        averages = {
          overall: avgRatings.reduce((sum, f) => sum + (f.rating || 0), 0) / avgRatings.length,
          service: avgRatings.reduce((sum, f) => sum + (f.service_rating || 0), 0) / avgRatings.length,
          cleanliness: avgRatings.reduce((sum, f) => sum + (f.cleanliness_rating || 0), 0) / avgRatings.length,
          timeliness: avgRatings.reduce((sum, f) => sum + (f.timeliness_rating || 0), 0) / avgRatings.length,
          experience: avgRatings.reduce((sum, f) => sum + (f.overall_experience_rating || 0), 0) / avgRatings.length
        };
      }

      return res.status(200).json({
        feedback: feedback || [],
        total: totalCount || 0,
        averages,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        requestId
      });
    }

    if (req.method === 'POST') {
      const {
        customer_id,
        booking_id,
        artist_id,
        rating,
        service_rating,
        cleanliness_rating,
        timeliness_rating,
        overall_experience_rating,
        feedback_text,
        would_recommend,
        improvement_suggestions,
        is_public = false
      } = req.body;

      // Validate required fields
      if (!customer_id || !booking_id || !rating) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Customer ID, booking ID, and overall rating are required',
          requestId
        });
      }

      // Validate rating values
      const ratings = [rating, service_rating, cleanliness_rating, timeliness_rating, overall_experience_rating];
      for (const r of ratings) {
        if (r !== null && r !== undefined && (r < 1 || r > 5)) {
          return res.status(400).json({
            error: 'Invalid rating value',
            message: 'All ratings must be between 1 and 5',
            requestId
          });
        }
      }

      const { data: feedbackRecord, error } = await supabase
        .from('customer_feedback')
        .insert([
          {
            customer_id,
            booking_id,
            artist_id,
            rating,
            service_rating,
            cleanliness_rating,
            timeliness_rating,
            overall_experience_rating,
            feedback_text,
            would_recommend,
            improvement_suggestions,
            is_public
          }
        ])
        .select(`
          id,
          customer_id,
          booking_id,
          artist_id,
          rating,
          service_rating,
          cleanliness_rating,
          timeliness_rating,
          overall_experience_rating,
          feedback_text,
          would_recommend,
          improvement_suggestions,
          is_public,
          created_at
        `)
        .single();

      if (error) {
        console.error(`[${requestId}] Error creating feedback:`, error);
        return res.status(500).json({
          error: 'Failed to create feedback record',
          message: error.message,
          requestId
        });
      }

      return res.status(201).json({
        feedback: feedbackRecord,
        message: 'Feedback submitted successfully',
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
}
