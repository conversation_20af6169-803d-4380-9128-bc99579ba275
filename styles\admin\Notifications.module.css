/* Notifications Management Styles */

.container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.header p {
  color: #64748b;
  font-size: 1.1rem;
  margin: 0;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status Card */
.statusCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.statusCard h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

.statusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.statusItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.statusLabel {
  font-weight: 500;
  color: #475569;
}

.statusValue {
  font-weight: 600;
}

.statusValue.success {
  color: #059669;
}

.statusValue.warning {
  color: #d97706;
}

.statusValue.error {
  color: #dc2626;
}

/* Test Email Card */
.testCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.testCard h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.testCard p {
  color: #64748b;
  margin: 0 0 1.5rem 0;
}

.testForm {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.emailInput {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.emailInput:focus {
  outline: none;
  border-color: #3b82f6;
}

.testButton {
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.testButton:hover:not(:disabled) {
  background: #2563eb;
}

.testButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.testResult {
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
}

.testResult.success {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.testResult.error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Templates Card */
.templatesCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.templatesCard h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

.templatesList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.templateItem {
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.templateItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.templateItem h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.templateItem p {
  color: #64748b;
  margin: 0;
  font-size: 0.9rem;
}

/* Help Card */
.helpCard {
  background: rgba(255, 248, 220, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #fbbf24;
}

.helpCard h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 1rem 0;
}

.helpCard p {
  color: #92400e;
  margin: 0 0 1rem 0;
}

.envVars {
  background: #451a03;
  color: #fbbf24;
  padding: 1rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  margin: 1rem 0;
}

.envVars code {
  display: block;
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .testForm {
    flex-direction: column;
  }
  
  .statusGrid {
    grid-template-columns: 1fr;
  }
  
  .templatesList {
    grid-template-columns: 1fr;
  }
}
