import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

interface SearchResult {
  id: string;
  type: 'customer' | 'booking' | 'service';
  title: string;
  subtitle: string;
  description?: string;
  url: string;
  metadata?: Record<string, any>;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;
    const { q: query, type, limit = 10 } = req.query;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const searchQuery = query.trim().toLowerCase();
    const searchLimit = Math.min(parseInt(limit as string) || 10, 50);
    const results: SearchResult[] = [];

    // Search customers (if user has permission)
    if ((!type || type === 'customer') && (user.role === 'Admin' || user.role === 'DEV')) {
      const { data: customers } = await supabase
        .from('customers')
        .select('id, first_name, last_name, email, phone, created_at')
        .or(`first_name.ilike.%${searchQuery}%,last_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%,phone.ilike.%${searchQuery}%`)
        .limit(searchLimit);

      if (customers) {
        customers.forEach(customer => {
          results.push({
            id: customer.id,
            type: 'customer',
            title: `${customer.first_name} ${customer.last_name}`,
            subtitle: customer.email,
            description: customer.phone,
            url: `/admin/customers/${customer.id}`,
            metadata: {
              created_at: customer.created_at,
              phone: customer.phone
            }
          });
        });
      }
    }

    // Search bookings
    if (!type || type === 'booking') {
      let bookingQuery = supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          start_time,
          status,
          total_amount,
          customers (first_name, last_name, email),
          services (name),
          artist_profiles!assigned_artist_id (artist_name, display_name)
        `)
        .limit(searchLimit);

      // If user is an artist, only show their bookings
      if (user.role === 'Artist' || user.role === 'Braider') {
        bookingQuery = bookingQuery.eq('assigned_artist_id', user.id);
      }

      const { data: bookings } = await bookingQuery;

      if (bookings) {
        bookings.forEach(booking => {
          const customer = Array.isArray(booking.customers) ? booking.customers[0] : booking.customers;
          const service = Array.isArray(booking.services) ? booking.services[0] : booking.services;
          const artist = Array.isArray(booking.artist_profiles) ? booking.artist_profiles[0] : booking.artist_profiles;
          
          const customerName = customer ? `${customer.first_name} ${customer.last_name}` : 'Unknown Customer';
          const serviceName = service?.name || 'Unknown Service';
          const artistName = artist?.artist_name || artist?.display_name || 'Unassigned';

          // Check if search query matches any booking data
          const searchableText = `${customerName} ${serviceName} ${artistName} ${booking.status}`.toLowerCase();
          if (searchableText.includes(searchQuery)) {
            results.push({
              id: booking.id,
              type: 'booking',
              title: `${customerName} - ${serviceName}`,
              subtitle: `${booking.booking_date} • ${artistName}`,
              description: `Status: ${booking.status} • $${booking.total_amount || 0}`,
              url: `/admin/bookings/${booking.id}`,
              metadata: {
                booking_date: booking.booking_date,
                status: booking.status,
                total_amount: booking.total_amount,
                artist_name: artistName
              }
            });
          }
        });
      }
    }

    // Search services (if user has permission)
    if ((!type || type === 'service') && (user.role === 'Admin' || user.role === 'DEV')) {
      const { data: services } = await supabase
        .from('services')
        .select('id, name, description, category, base_price, duration, is_active')
        .or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,category.ilike.%${searchQuery}%`)
        .limit(searchLimit);

      if (services) {
        services.forEach(service => {
          results.push({
            id: service.id,
            type: 'service',
            title: service.name,
            subtitle: service.category,
            description: `$${service.base_price || 0} • ${service.duration || 0} min • ${service.is_active ? 'Active' : 'Inactive'}`,
            url: `/admin/services/${service.id}`,
            metadata: {
              category: service.category,
              base_price: service.base_price,
              duration: service.duration,
              is_active: service.is_active
            }
          });
        });
      }
    }

    // Sort results by relevance (exact matches first, then partial matches)
    results.sort((a, b) => {
      const aExact = a.title.toLowerCase().includes(searchQuery) ? 1 : 0;
      const bExact = b.title.toLowerCase().includes(searchQuery) ? 1 : 0;
      return bExact - aExact;
    });

    return res.status(200).json({
      query: searchQuery,
      results: results.slice(0, searchLimit),
      total: results.length,
      types: {
        customer: results.filter(r => r.type === 'customer').length,
        booking: results.filter(r => r.type === 'booking').length,
        service: results.filter(r => r.type === 'service').length
      }
    });

  } catch (error) {
    console.error('Search API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
