/**
 * Ocean Soul Sparkles Admin Dashboard - Error Handler
 * Standardized error handling system for production-ready admin system
 */

import { NextApiResponse } from 'next';
import { v4 as uuidv4 } from 'uuid';

// Error codes enum
export enum ErrorCode {
  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  MFA_REQUIRED = 'MFA_REQUIRED',
  
  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT = 'INVALID_FORMAT',
  
  // Resource Management
  NOT_FOUND = 'NOT_FOUND',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  CONFLICT = 'CONFLICT',
  RESOURCE_LOCKED = 'RESOURCE_LOCKED',
  
  // Business Logic
  BOOKING_CONFLICT = 'BOOKING_CONFLICT',
  INSUFFICIENT_STOCK = 'INSUFFICIENT_STOCK',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  
  // System Errors
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // File Operations
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
}

// Base error class
export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly details?: any;
  public readonly field?: string;
  public readonly requestId: string;
  public readonly timestamp: string;

  constructor(
    code: ErrorCode,
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    details?: any,
    field?: string
  ) {
    super(message);
    
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.details = details;
    this.field = field;
    this.requestId = uuidv4();
    this.timestamp = new Date().toISOString();
    
    // Maintain proper stack trace
    Error.captureStackTrace(this, this.constructor);
  }
}

// Specific error classes
export class ValidationError extends AppError {
  constructor(message: string, field?: string, details?: any) {
    super(ErrorCode.VALIDATION_ERROR, message, 400, true, details, field);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required', code: ErrorCode = ErrorCode.UNAUTHORIZED) {
    super(code, message, 401, true);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(ErrorCode.FORBIDDEN, message, 403, true);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string, id?: string) {
    const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;
    super(ErrorCode.NOT_FOUND, message, 404, true, { resource, id });
  }
}

export class ConflictError extends AppError {
  constructor(message: string, details?: any) {
    super(ErrorCode.CONFLICT, message, 409, true, details);
  }
}

export class BusinessLogicError extends AppError {
  constructor(code: ErrorCode, message: string, details?: any) {
    super(code, message, 422, true, details);
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, details?: any) {
    super(
      ErrorCode.EXTERNAL_SERVICE_ERROR,
      `External service error: ${service} - ${message}`,
      503,
      true,
      { service, ...details }
    );
  }
}

// Error response interface
export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    field?: string;
  };
  meta: {
    requestId: string;
    timestamp: string;
    version: string;
  };
}

// Error handler function
export function handleError(error: Error, res: NextApiResponse): void {
  let appError: AppError;

  // Convert unknown errors to AppError
  if (error instanceof AppError) {
    appError = error;
  } else {
    // Log unexpected errors
    console.error('Unexpected error:', error);
    
    appError = new AppError(
      ErrorCode.INTERNAL_ERROR,
      'An unexpected error occurred',
      500,
      false,
      process.env.NODE_ENV === 'development' ? error.stack : undefined
    );
  }

  // Log operational errors in development
  if (process.env.NODE_ENV === 'development' || !appError.isOperational) {
    console.error(`[${appError.requestId}] ${appError.code}: ${appError.message}`, {
      statusCode: appError.statusCode,
      details: appError.details,
      stack: appError.stack
    });
  }

  // Send error response
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      code: appError.code,
      message: appError.message,
      details: appError.details,
      field: appError.field
    },
    meta: {
      requestId: appError.requestId,
      timestamp: appError.timestamp,
      version: process.env.API_VERSION || '1.0.0'
    }
  };

  res.status(appError.statusCode).json(errorResponse);
}

// Success response helper
export function sendSuccess<T>(
  res: NextApiResponse,
  data: T,
  statusCode: number = 200,
  meta?: any
): void {
  const response = {
    success: true,
    data,
    meta: {
      requestId: uuidv4(),
      timestamp: new Date().toISOString(),
      version: process.env.API_VERSION || '1.0.0',
      ...meta
    }
  };

  res.status(statusCode).json(response);
}

// Validation helper
export function validateRequired(data: any, fields: string[]): void {
  const missing = fields.filter(field => {
    const value = data[field];
    return value === undefined || value === null || value === '';
  });

  if (missing.length > 0) {
    throw new ValidationError(
      `Missing required fields: ${missing.join(', ')}`,
      missing[0],
      { missingFields: missing }
    );
  }
}

// Email validation
export function validateEmail(email: string): void {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ValidationError('Invalid email format', 'email');
  }
}

// Phone validation
export function validatePhone(phone: string): void {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  if (!phoneRegex.test(phone)) {
    throw new ValidationError('Invalid phone number format', 'phone');
  }
}

// Date validation
export function validateDate(date: string, field: string = 'date'): void {
  const parsedDate = new Date(date);
  if (isNaN(parsedDate.getTime())) {
    throw new ValidationError('Invalid date format', field);
  }
}

// Async error wrapper
export function asyncHandler(fn: Function) {
  return (req: any, res: any, next?: any) => {
    Promise.resolve(fn(req, res, next)).catch((error) => {
      handleError(error, res);
    });
  };
}

// Database error mapper
export function mapDatabaseError(error: any): AppError {
  // PostgreSQL/Supabase specific error mapping
  if (error.code === '23505') {
    return new ConflictError('Resource already exists', { constraint: error.constraint });
  }
  
  if (error.code === '23503') {
    return new ValidationError('Referenced resource does not exist', undefined, { constraint: error.constraint });
  }
  
  if (error.code === '23502') {
    return new ValidationError('Required field is missing', undefined, { column: error.column });
  }
  
  // Generic database error
  return new AppError(
    ErrorCode.DATABASE_ERROR,
    'Database operation failed',
    500,
    true,
    process.env.NODE_ENV === 'development' ? error : undefined
  );
}

// Error logging utility
export function logError(error: AppError, context?: any): void {
  const logData = {
    requestId: error.requestId,
    code: error.code,
    message: error.message,
    statusCode: error.statusCode,
    timestamp: error.timestamp,
    isOperational: error.isOperational,
    details: error.details,
    field: error.field,
    context,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
  };

  // In production, you would send this to your logging service
  if (error.statusCode >= 500) {
    console.error('Server Error:', logData);
  } else if (process.env.NODE_ENV === 'development') {
    console.warn('Client Error:', logData);
  }
}
