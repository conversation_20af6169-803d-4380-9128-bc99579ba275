import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface ScheduleRequest {
  staff_id: string;
  request_type: 'time_off' | 'schedule_change' | 'shift_swap';
  start_date: string;
  end_date: string;
  start_time?: string;
  end_time?: string;
  reason?: string;
  replacement_staff_id?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const requestId = uuidv4();
  
  try {
    // Authentication check
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid authentication token required',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { 
        staff_id, 
        request_type, 
        status, 
        start_date, 
        end_date,
        limit = 50, 
        offset = 0 
      } = req.query;

      let query = supabase
        .from('staff_schedule_requests')
        .select(`
          id,
          staff_id,
          request_type,
          start_date,
          end_date,
          start_time,
          end_time,
          reason,
          replacement_staff_id,
          status,
          priority,
          approved_by,
          approved_at,
          denial_reason,
          created_at,
          updated_at,
          admin_users!staff_id(
            id,
            first_name,
            last_name,
            email,
            role
          ),
          replacement_staff:admin_users!replacement_staff_id(
            id,
            first_name,
            last_name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      // Apply filters
      if (staff_id) {
        query = query.eq('staff_id', staff_id);
      }
      if (request_type) {
        query = query.eq('request_type', request_type);
      }
      if (status) {
        query = query.eq('status', status);
      }
      if (start_date) {
        query = query.gte('start_date', start_date);
      }
      if (end_date) {
        query = query.lte('end_date', end_date);
      }

      // Apply pagination
      query = query.range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1);

      const { data: scheduleRequests, error } = await query;

      if (error) {
        console.error('Schedule requests fetch error:', error);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to fetch schedule requests',
          requestId
        });
      }

      // Get total count for pagination
      const { count: totalCount } = await supabase
        .from('staff_schedule_requests')
        .select('*', { count: 'exact', head: true });

      // Get summary statistics
      const stats = {
        total: scheduleRequests?.length || 0,
        pending: scheduleRequests?.filter(req => req.status === 'pending').length || 0,
        approved: scheduleRequests?.filter(req => req.status === 'approved').length || 0,
        denied: scheduleRequests?.filter(req => req.status === 'denied').length || 0,
        byType: {
          time_off: scheduleRequests?.filter(req => req.request_type === 'time_off').length || 0,
          schedule_change: scheduleRequests?.filter(req => req.request_type === 'schedule_change').length || 0,
          shift_swap: scheduleRequests?.filter(req => req.request_type === 'shift_swap').length || 0
        }
      };

      return res.status(200).json({
        scheduleRequests: scheduleRequests || [],
        stats,
        pagination: {
          total: totalCount || 0,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: (parseInt(offset as string) + parseInt(limit as string)) < (totalCount || 0)
        },
        requestId
      });
    }

    if (req.method === 'POST') {
      const scheduleData: ScheduleRequest = req.body;

      // Validate required fields
      if (!scheduleData.staff_id || !scheduleData.request_type || !scheduleData.start_date || !scheduleData.end_date) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Missing required fields: staff_id, request_type, start_date, end_date',
          requestId
        });
      }

      // Verify staff member exists
      const { data: staff, error: staffError } = await supabase
        .from('admin_users')
        .select('id, first_name, last_name, email, role')
        .eq('id', scheduleData.staff_id)
        .single();

      if (staffError || !staff) {
        return res.status(404).json({
          error: 'Staff member not found',
          message: 'The specified staff member does not exist',
          requestId
        });
      }

      // Verify replacement staff if provided
      if (scheduleData.replacement_staff_id) {
        const { data: replacementStaff, error: replacementError } = await supabase
          .from('admin_users')
          .select('id, first_name, last_name')
          .eq('id', scheduleData.replacement_staff_id)
          .single();

        if (replacementError || !replacementStaff) {
          return res.status(404).json({
            error: 'Replacement staff not found',
            message: 'The specified replacement staff member does not exist',
            requestId
          });
        }
      }

      // Check for conflicting requests
      const { data: conflictingRequests, error: conflictError } = await supabase
        .from('staff_schedule_requests')
        .select('id, start_date, end_date, status')
        .eq('staff_id', scheduleData.staff_id)
        .in('status', ['pending', 'approved'])
        .or(`and(start_date.lte.${scheduleData.end_date},end_date.gte.${scheduleData.start_date})`);

      if (conflictError) {
        console.error('Conflict check error:', conflictError);
      } else if (conflictingRequests && conflictingRequests.length > 0) {
        return res.status(409).json({
          error: 'Schedule conflict',
          message: 'There are conflicting schedule requests for the selected dates',
          conflictingRequests,
          requestId
        });
      }

      const newScheduleRequest = {
        id: uuidv4(),
        staff_id: scheduleData.staff_id,
        request_type: scheduleData.request_type,
        start_date: scheduleData.start_date,
        end_date: scheduleData.end_date,
        start_time: scheduleData.start_time || null,
        end_time: scheduleData.end_time || null,
        reason: scheduleData.reason || null,
        replacement_staff_id: scheduleData.replacement_staff_id || null,
        status: 'pending',
        priority: scheduleData.priority || 'normal',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: createdRequest, error: createError } = await supabase
        .from('staff_schedule_requests')
        .insert([newScheduleRequest])
        .select(`
          *,
          admin_users!staff_id(
            id,
            first_name,
            last_name,
            email,
            role
          ),
          replacement_staff:admin_users!replacement_staff_id(
            id,
            first_name,
            last_name,
            email
          )
        `)
        .single();

      if (createError) {
        console.error('Schedule request creation error:', createError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to create schedule request',
          requestId
        });
      }

      return res.status(201).json({
        scheduleRequest: createdRequest,
        message: 'Schedule request created successfully',
        requestId
      });
    }

    if (req.method === 'PUT') {
      const { request_id } = req.query;
      const updateData = req.body;

      if (!request_id) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Schedule request ID is required',
          requestId
        });
      }

      // Verify schedule request exists
      const { data: existingRequest, error: fetchError } = await supabase
        .from('staff_schedule_requests')
        .select('*')
        .eq('id', request_id)
        .single();

      if (fetchError || !existingRequest) {
        return res.status(404).json({
          error: 'Schedule request not found',
          message: 'The specified schedule request does not exist',
          requestId
        });
      }

      // Handle approval/denial
      if (updateData.status && ['approved', 'denied'].includes(updateData.status)) {
        updateData.approved_at = new Date().toISOString();
        // Note: approved_by should be set from the authenticated user
      }

      const updatedData = {
        ...updateData,
        updated_at: new Date().toISOString()
      };

      const { data: updatedRequest, error: updateError } = await supabase
        .from('staff_schedule_requests')
        .update(updatedData)
        .eq('id', request_id)
        .select(`
          *,
          admin_users!staff_id(
            id,
            first_name,
            last_name,
            email,
            role
          ),
          replacement_staff:admin_users!replacement_staff_id(
            id,
            first_name,
            last_name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          )
        `)
        .single();

      if (updateError) {
        console.error('Schedule request update error:', updateError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to update schedule request',
          requestId
        });
      }

      return res.status(200).json({
        scheduleRequest: updatedRequest,
        message: 'Schedule request updated successfully',
        requestId
      });
    }

    if (req.method === 'DELETE') {
      const { request_id } = req.query;

      if (!request_id) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Schedule request ID is required',
          requestId
        });
      }

      // Verify schedule request exists
      const { data: existingRequest, error: fetchError } = await supabase
        .from('staff_schedule_requests')
        .select('id, staff_id, status')
        .eq('id', request_id)
        .single();

      if (fetchError || !existingRequest) {
        return res.status(404).json({
          error: 'Schedule request not found',
          message: 'The specified schedule request does not exist',
          requestId
        });
      }

      // Only allow deletion of pending requests
      if (existingRequest.status !== 'pending') {
        return res.status(400).json({
          error: 'Invalid operation',
          message: 'Only pending schedule requests can be deleted',
          requestId
        });
      }

      const { error: deleteError } = await supabase
        .from('staff_schedule_requests')
        .delete()
        .eq('id', request_id);

      if (deleteError) {
        console.error('Schedule request deletion error:', deleteError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to delete schedule request',
          requestId
        });
      }

      return res.status(200).json({
        message: 'Schedule request deleted successfully',
        deletedRequest: existingRequest,
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      message: `HTTP method ${req.method} is not supported`,
      requestId
    });

  } catch (error) {
    console.error('Schedule API error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      requestId
    });
  }
}
