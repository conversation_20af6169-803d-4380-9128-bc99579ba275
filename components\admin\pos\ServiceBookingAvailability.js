import { useState, useEffect } from 'react'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * ServiceBookingAvailability component for displaying booking availability for a specific service
 * Shows artists, tiers, and time slots in a combined view
 * 
 * @param {Object} props - Component props
 * @param {Object} props.service - Selected service with artists and pricing tiers
 * @param {Function} props.onBookingSlotSelect - Callback when booking slot is selected (artist, tier, timeSlot)
 * @param {Function} props.onBack - Callback to go back to services
 * @returns {JSX.Element}
 */
export default function ServiceBookingAvailability({ service, onBookingSlotSelect, onBack }) {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [selectedArtist, setSelectedArtist] = useState(null)
  const [selectedTier, setSelectedTier] = useState(null)
  const [timeSlots, setTimeSlots] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Get available artists for the service
  const availableArtists = service?.availableArtists?.filter(artist => artist.isAvailableToday) || []

  // Get pricing tiers for the service
  const pricingTiers = service?.pricing_tiers || []

  // Update time slots when date, artist, or tier changes
  useEffect(() => {
    if (selectedArtist && selectedDate && selectedTier?.duration) {
      setLoading(true);
      setError(null);
      setTimeSlots([]); // Clear previous slots

      // Ensure selectedDate is a string in YYYY-MM-DD format.
      let dateString = selectedDate;
      if (selectedDate instanceof Date) {
        dateString = selectedDate.toISOString().split('T')[0];
      }

      const apiUrl = `/api/admin/pos/artist-availability?artist_id=${selectedArtist.id}&date=${dateString}&service_duration_minutes=${selectedTier.duration}`;

      console.log(`Fetching availability: ${apiUrl}`);
      console.log('Params:', { artistId: selectedArtist.id, date: dateString, duration: selectedTier.duration });

      fetch(apiUrl)
        .then(async (res) => {
          if (res.ok) {
            const data = await res.json();
            console.log('Availability API Response:', data);
            setTimeSlots(data.available_slots || []);
            if (!data.available_slots || data.available_slots.length === 0) {
              console.log('API message (no slots or other info):', data.message);
            }
          } else {
            const errorData = await res.json().catch(() => ({ message: "Failed to parse error response." }));
            console.error(`Error fetching availability: ${res.status}`, errorData);
            setError(errorData.message || `Failed to fetch availability (status ${res.status}).`);
            setTimeSlots([]);
          }
        })
        .catch((err) => {
          console.error('Network error fetching availability:', err);
          setError("Network error. Failed to fetch availability.");
          setTimeSlots([]);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setTimeSlots([]);
      setLoading(false);
    }
  }, [selectedDate, selectedArtist, selectedTier]);

  // Auto-select first available artist if only one
  useEffect(() => {
    if (availableArtists.length === 1 && !selectedArtist) {
      setSelectedArtist(availableArtists[0])
    }
  }, [availableArtists, selectedArtist])

  // Auto-select first tier if only one
  useEffect(() => {
    if (pricingTiers.length === 1 && !selectedTier) {
      setSelectedTier(pricingTiers[0])
    }
  }, [pricingTiers, selectedTier])

  const handleDateChange = (event) => {
    setSelectedDate(event.target.value)
  }

  const handleArtistSelect = (artist) => {
    setSelectedArtist(artist)
  }

  const handleTierSelect = (tier) => {
    setSelectedTier(tier)
  }
  
  const handleTimeSlotClick = (slot) => {
    if (slot.status === 'available' && selectedArtist && selectedTier) {
      onBookingSlotSelect(selectedArtist, selectedTier, {
        ...slot,
        time: slot.time // slot.time is already an ISO string from the API
      })
    }
  }

  const formatTime = (isoString) => { // Now expects an ISO string
    return new Date(isoString).toLocaleTimeString('en-AU', {
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false
    })
  }

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`
  }

  const canSelectTimeSlot = selectedArtist && selectedTier

  return (
    <div className={styles.serviceBookingAvailability}>
      <div className={styles.bookingHeader}>
        <button 
          className={styles.backButton}
          onClick={onBack}
        >
          ← Back to Services
        </button>
        <div className={styles.serviceInfo}>
          <h2 className={styles.serviceName}>{safeRender(service.name, 'Service')}</h2>
          <p className={styles.serviceDescription}>{safeRender(service.description, '')}</p>
        </div>
      </div>

      <div className={styles.bookingSelectionGrid}>
        {/* Artist Selection */}
        <div className={styles.selectionSection}>
          <h3 className={styles.sectionTitle}>Choose Artist</h3>
          {availableArtists.length === 0 ? (
            <div className={styles.noOptions}>
              <p>No artists available for this service today</p>
            </div>
          ) : (
            <div className={styles.artistGrid}>
              {availableArtists.map((artist) => (
                <div
                  key={artist.id}
                  className={`${styles.artistCard} ${
                    selectedArtist?.id === artist.id ? styles.selected : ''
                  }`}
                  onClick={() => handleArtistSelect(artist)}
                >
                  <div className={styles.artistAvatar}>
                    {artist.name?.charAt(0)?.toUpperCase() || '?'}
                  </div>
                  <div className={styles.artistInfo}>
                    <h4 className={styles.artistName}>{safeRender(artist.name, 'Unknown Artist')}</h4>
                    <p className={styles.artistRole}>{safeRender(artist.role || artist.specialties?.[0], 'Artist')}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Tier Selection */}
        <div className={styles.selectionSection}>
          <h3 className={styles.sectionTitle}>Choose Duration & Price</h3>
          {pricingTiers.length === 0 ? (
            <div className={styles.noOptions}>
              <p>No pricing options available</p>
            </div>
          ) : (
            <div className={styles.tierGrid}>
              {pricingTiers.map((tier) => (
                <div
                  key={tier.id}
                  className={`${styles.tierCard} ${
                    selectedTier?.id === tier.id ? styles.selected : ''
                  }`}
                  onClick={() => handleTierSelect(tier)}
                >
                  <h4 className={styles.tierName}>{safeRender(tier.name, 'Standard')}</h4>
                  <div className={styles.tierPrice}>{formatCurrency(tier.price)}</div>
                  <div className={styles.tierDuration}>{safeRender(tier.duration, '30')} minutes</div>
                  {tier.description && (
                    <p className={styles.tierDescription}>{tier.description}</p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Time Slot Selection */}
        <div className={styles.selectionSection}>
          <h3 className={styles.sectionTitle}>Choose Time Slot</h3>
          
          <div className={styles.dateSelector}>
            <label htmlFor="booking-date-select">Date:</label>
            <input
              id="booking-date-select"
              type="date"
              value={selectedDate}
              onChange={handleDateChange}
              className={styles.dateInput}
              min={new Date().toISOString().split('T')[0]}
            />
          </div>

          {!canSelectTimeSlot ? (
            <div className={styles.selectPrompt}>
              <p>Please select an artist and duration first</p>
            </div>
          ) : loading ? (
            <div className={styles.loading}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading time slots...</p>
            </div>
          ) : error ? (
            <div className={`${styles.errorNotice} p-3 bg-red-100 border border-red-400 text-red-700 rounded-md`}>
              <p>Error: {error}</p>
            </div>
          ) : timeSlots.length > 0 ? (
            <div className={styles.timeSlots}>
              {timeSlots.map((slot) => ( // slot.time is an ISO string
                <div
                  key={slot.id || slot.time} // Use slot.time as part of key if id is not guaranteed unique from API yet
                  className={`${styles.timeSlot} ${styles[slot.status] || (slot.status === 'available' ? styles.available : styles.unavailable)} ${
                    slot.status === 'available' ? styles.clickable : ''
                  }`}
                  onClick={() => handleTimeSlotClick(slot)}
                  title={`${formatTime(slot.time)} - ${slot.status}`}
                >
                  {formatTime(slot.time)}
                </div>
              ))}
            </div>
          ) : (
             <div className={styles.noOptions}>
                <p>No available slots for this day, artist, or service duration. Try changing the date or service options.</p>
             </div>
          )}
        </div>
      </div>

      {/* Selection Summary */}
      {(selectedArtist || selectedTier) && (
        <div className={styles.selectionSummary}>
          <h4>Current Selection</h4>
          <div className={styles.summaryGrid}>
            {selectedArtist && (
              <div className={styles.summaryItem}>
                <strong>Artist:</strong> {selectedArtist.name}
              </div>
            )}
            {selectedTier && (
              <div className={styles.summaryItem}>
                <strong>Duration:</strong> {selectedTier.duration} minutes
              </div>
            )}
            {selectedTier && (
              <div className={styles.summaryItem}>
                <strong>Price:</strong> {formatCurrency(selectedTier.price)}
              </div>
            )}
          </div>
          {canSelectTimeSlot && (
            <p className={styles.nextStep}>Now select an available time slot to continue</p>
          )}
        </div>
      )}
    </div>
  )
}
