<<<<<<< HEAD
# Ocean Soul Sparkles - Admin Portal

**Secure staff management system for Ocean Soul Sparkles business operations**

## 🎯 Overview

This is the admin portal for Ocean Soul Sparkles, providing secure access for staff to manage bookings, customers, payments, and business operations. The admin portal is completely separated from the public website for enhanced security.

## 🔐 Security Features

- **5-tier role-based access control** (DE<PERSON>, Admin, Artist, Braider, User)
- **Multi-Factor Authentication (MFA)** for all admin users
- **Row Level Security (RLS)** on all database operations
- **Production-grade security headers** and Content Security Policy
- **Audit logging** for all admin actions
- **IP restrictions** (configurable)
- **Session management** with automatic timeouts

## 🚀 Deployment

### Production URL
- **Admin Portal**: https://admin.oceansoulsparkles.com.au
- **Public Website**: https://www.oceansoulsparkles.com.au

### Quick Deploy to Vercel

```bash
# Clone the repository
git clone https://github.com/YOUR_USERNAME/oceansoulsparkles-admin.git
cd oceansoulsparkles-admin

# Install dependencies
npm install

# Deploy to production
vercel --prod
```

### Environment Configuration

Copy `.env.local` and configure the following production variables:

```env
# Admin Portal Configuration
NEXT_PUBLIC_ADMIN_SUBDOMAIN=true
NEXT_PUBLIC_SITE_URL=https://admin.oceansoulsparkles.com.au
NEXT_PUBLIC_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au

# Database (Supabase)
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Authentication & Security
NEXTAUTH_URL=https://admin.oceansoulsparkles.com.au
NEXTAUTH_SECRET=your_32_character_secret
JWT_SECRET=your_32_character_jwt_secret
ENCRYPTION_KEY=your_32_character_encryption_key

# Payment Processing (Square)
NEXT_PUBLIC_SQUARE_APPLICATION_ID=your_square_app_id
NEXT_PUBLIC_SQUARE_LOCATION_ID=your_location_id
SQUARE_ACCESS_TOKEN=your_square_access_token
NEXT_PUBLIC_SQUARE_ENVIRONMENT=production

# Production Settings
NODE_ENV=production
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_ADMIN_ACCESS=true
```

## 🏗️ Architecture

### Tech Stack
- **Framework**: Next.js 14.2.30
- **Language**: TypeScript
- **Database**: Supabase (PostgreSQL)
- **Authentication**: NextAuth.js + Custom JWT
- **Payments**: Square Production API
- **Hosting**: Vercel
- **Styling**: CSS Modules + Custom CSS

### Project Structure
```
oceansoulsparkles-admin/
├── components/          # React components
│   ├── admin/          # Admin-specific components
│   ├── auth/           # Authentication components
│   └── security/       # Security-related components
├── pages/              # Next.js pages
│   ├── admin/          # Admin dashboard pages
│   ├── api/            # API routes
│   └── auth/           # Authentication pages
├── lib/                # Utility libraries
│   ├── auth/           # Authentication logic
│   └── security/       # Security utilities
├── styles/             # CSS modules and styles
├── scripts/            # Deployment and utility scripts
├── types/              # TypeScript type definitions
└── utils/              # Helper utilities
```

## 👥 User Roles & Permissions

### DEV (Development Team)
- Full system access
- Database management
- Security configuration
- User role assignment

### Admin (Business Managers)
- Complete business operations
- Staff management
- Financial reporting
- System configuration

### Artist (Service Providers)
- Service delivery management
- Customer interaction
- Schedule management
- Portfolio updates

### Braider (Hair Specialists)
- Hair braiding services
- Appointment scheduling
- Customer consultation
- Service documentation

### User (Basic Staff)
- Customer service functions
- Basic booking assistance
- Information access

## 📋 Features

### Dashboard
- Real-time business metrics
- Today's schedule overview
- Quick action buttons
- Performance analytics

### Booking Management
- Create and edit bookings
- Artist/braider assignment
- Payment processing
- Customer communication

### Customer Management
- Customer profiles and history
- Communication logs
- Preference tracking
- Service recommendations

### Payment Processing
- Square integration
- Multiple payment methods
- Refund processing
- Financial reporting

### Staff Management
- Role-based access control
- Performance tracking
- Schedule management
- Training documentation

## 🔧 Development

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account
- Square developer account

### Local Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Build for production
npm run build

# Security audit
npm run security-audit

# Environment validation
npm run check-env
```

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run test` - Run test suite
- `npm run security-audit` - Security validation
- `npm run deploy-admin` - Deployment validation
- `npm run check-env` - Environment validation

## 📚 Documentation

- **[Staff Onboarding Guide](./STAFF_ONBOARDING.md)** - Complete staff training guide
- **[Production Deployment](./PRODUCTION_DEPLOYMENT.md)** - Deployment instructions
- **[Deployment Summary](./DEPLOYMENT_SUMMARY.md)** - Deployment status and checklist

## 🔒 Security

### Security Measures
- All sensitive data encrypted at rest and in transit
- Regular security audits and penetration testing
- Compliance with industry security standards
- Automated vulnerability scanning
- Secure coding practices enforced

### Reporting Security Issues
If you discover a security vulnerability, please email: <EMAIL>

**Do not create public GitHub issues for security vulnerabilities.**

## 📞 Support

### Technical Support
- **Email**: <EMAIL>
- **Business Hours**: Monday-Friday, 9 AM - 5 PM AEST

### Emergency Contact
- **After Hours**: [Emergency contact to be provided during onboarding]

## 📄 License

This project is proprietary software owned by Ocean Soul Sparkles. All rights reserved.

## 🚀 Deployment Status

**Current Status**: ✅ Production Ready

- ✅ Security audit passed
- ✅ Environment configured
- ✅ Database verified
- ✅ Build successful
- ✅ Documentation complete

---

**Ocean Soul Sparkles Admin Portal v1.0.0**  
*Secure. Scalable. Professional.*
=======
# oceansoulsparkles-admin
Ocean Soul Sparkles Admin Dashboard
>>>>>>> 2a732bc89768fc592bd12b5efd9e70a41b1be918
