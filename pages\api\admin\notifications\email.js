import { authenticateAdminRequest } from '@/lib/auth/admin-auth'
import emailService from '@/lib/email/email-service'

/**
 * API endpoint for email notifications
 * Handles sending various types of email notifications
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Email notifications API called - ${req.method}`)

  try {
    // Authenticate admin request
    const { user, error: authError } = await authenticateAdminRequest(req)
    if (authError || !user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: authError?.message || 'Authentication failed',
        requestId
      })
    }

    if (req.method === 'POST') {
      const { type, data } = req.body

      if (!type) {
        return res.status(400).json({
          error: 'Email type is required',
          requestId
        })
      }

      let result
      
      switch (type) {
        case 'booking_confirmation':
          result = await emailService.sendBookingConfirmation(data)
          break
          
        case 'booking_reminder':
          result = await emailService.sendBookingReminder(data)
          break
          
        case 'booking_cancellation':
          result = await emailService.sendBookingCancellation(data)
          break
          
        case 'payment_receipt':
          result = await emailService.sendPaymentReceipt(data)
          break
          
        case 'staff_notification':
          result = await emailService.sendStaffNotification(data)
          break
          
        case 'low_inventory_alert':
          result = await emailService.sendLowInventoryAlert(data.items, data.adminEmail)
          break
          
        case 'test_email':
          if (!data.to) {
            return res.status(400).json({
              error: 'Recipient email is required for test email',
              requestId
            })
          }
          result = await emailService.sendTest(data.to)
          break
          
        default:
          return res.status(400).json({
            error: `Unknown email type: ${type}`,
            requestId
          })
      }

      if (result.success) {
        console.log(`[${requestId}] Email sent successfully:`, result.messageId)
        return res.status(200).json({
          success: true,
          messageId: result.messageId,
          message: 'Email sent successfully',
          requestId
        })
      } else {
        console.error(`[${requestId}] Email sending failed:`, result.error)
        return res.status(500).json({
          error: 'Failed to send email',
          message: result.error,
          requestId
        })
      }
    }

    if (req.method === 'GET') {
      // Get email service status
      const status = emailService.getStatus()
      
      return res.status(200).json({
        status,
        requestId
      })
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    })

  } catch (error) {
    console.error(`[${requestId}] Email API error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      requestId
    })
  }
}
