/**
 * SMS Templates for Ocean Soul Sparkles Admin Dashboard
 * 
 * Provides pre-defined SMS message templates with variable substitution.
 */

/**
 * Booking confirmation SMS template
 */
function bookingConfirmationTemplate(booking) {
  return `Hi ${booking.customerName}! Your appointment for ${booking.serviceName} is confirmed for ${booking.date} at ${booking.time}. Location: Ocean Soul Sparkles. Questions? Reply to this message.`;
}

/**
 * Booking reminder SMS template
 */
function bookingReminderTemplate(booking) {
  const timeUntil = booking.reminderHours || 24;
  const timeText = timeUntil === 24 ? 'tomorrow' : `in ${timeUntil} hours`;
  
  return `Reminder: Your appointment for ${booking.serviceName} is ${timeText} at ${booking.time}. See you at Ocean Soul Sparkles! Reply CONFIRM to confirm or CANCEL to reschedule.`;
}

/**
 * Booking cancellation SMS template
 */
function bookingCancellationTemplate(booking) {
  return `Your appointment for ${booking.serviceName} on ${booking.date} at ${booking.time} has been cancelled. To reschedule, please contact Ocean Soul Sparkles. Thank you!`;
}

/**
 * Payment receipt SMS template
 */
function paymentReceiptTemplate(payment) {
  return `Payment received! $${payment.amount} for ${payment.serviceName}. Receipt #${payment.receiptNumber}. Thank you for choosing Ocean Soul Sparkles!`;
}

/**
 * Staff schedule notification SMS template
 */
function staffScheduleTemplate(notification) {
  return `Schedule update: You have ${notification.appointmentCount} appointments on ${notification.date}. Check the admin portal for details. - Ocean Soul Sparkles`;
}

/**
 * Staff shift reminder SMS template
 */
function staffShiftReminderTemplate(shift) {
  return `Shift reminder: You're scheduled to work ${shift.startTime} - ${shift.endTime} on ${shift.date}. See you at Ocean Soul Sparkles!`;
}

/**
 * Low inventory alert SMS template (for managers)
 */
function lowInventoryAlertTemplate(items) {
  const itemCount = items.length;
  const itemNames = items.slice(0, 3).map(item => item.name).join(', ');
  const moreText = itemCount > 3 ? ` and ${itemCount - 3} more` : '';
  
  return `Low inventory alert: ${itemNames}${moreText} need restocking. Check admin portal for details.`;
}

/**
 * Customer welcome SMS template
 */
function customerWelcomeTemplate(customer) {
  return `Welcome to Ocean Soul Sparkles, ${customer.name}! We're excited to help you sparkle. Book your next appointment online or reply to this message.`;
}

/**
 * Promotional SMS template
 */
function promotionalTemplate(promotion) {
  return `${promotion.title} at Ocean Soul Sparkles! ${promotion.description} Valid until ${promotion.expiryDate}. Book now!`;
}

/**
 * Birthday SMS template
 */
function birthdayTemplate(customer) {
  return `Happy Birthday, ${customer.name}! 🎉 Celebrate with us - enjoy 15% off your next service at Ocean Soul Sparkles. Valid for 30 days!`;
}

/**
 * Appointment follow-up SMS template
 */
function appointmentFollowUpTemplate(booking) {
  return `Thank you for visiting Ocean Soul Sparkles! How was your ${booking.serviceName} experience? We'd love your feedback. Reply or visit our website to review.`;
}

/**
 * No-show follow-up SMS template
 */
function noShowFollowUpTemplate(booking) {
  return `We missed you for your ${booking.serviceName} appointment today. Life happens! Please contact us to reschedule. - Ocean Soul Sparkles`;
}

/**
 * Waitlist notification SMS template
 */
function waitlistNotificationTemplate(booking) {
  return `Great news! A spot opened up for ${booking.serviceName} on ${booking.date} at ${booking.time}. Reply YES within 2 hours to confirm this appointment.`;
}

/**
 * Service reminder SMS template (for recurring services)
 */
function serviceReminderTemplate(customer, service) {
  const daysSince = service.daysSinceLastVisit || 30;
  return `Hi ${customer.name}! It's been ${daysSince} days since your last ${service.name}. Ready for your next sparkle session? Book online or reply to schedule.`;
}

/**
 * Emergency closure SMS template
 */
function emergencyClosureTemplate(closure) {
  return `Important: Ocean Soul Sparkles is temporarily closed due to ${closure.reason}. We'll contact you to reschedule your appointment. Thank you for understanding.`;
}

/**
 * Template variable substitution
 */
function substituteVariables(template, variables) {
  let message = template;
  
  Object.keys(variables).forEach(key => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    message = message.replace(regex, variables[key] || '');
  });
  
  return message;
}

/**
 * Get all available SMS templates
 */
function getAllTemplates() {
  return {
    booking_confirmation: {
      name: 'Booking Confirmation',
      description: 'Sent when a booking is confirmed',
      template: 'Hi {{customerName}}! Your appointment for {{serviceName}} is confirmed for {{date}} at {{time}}. Location: Ocean Soul Sparkles. Questions? Reply to this message.',
      variables: ['customerName', 'serviceName', 'date', 'time'],
      category: 'booking'
    },
    booking_reminder: {
      name: 'Booking Reminder',
      description: 'Sent before appointment (24h default)',
      template: 'Reminder: Your appointment for {{serviceName}} is {{timeText}} at {{time}}. See you at Ocean Soul Sparkles! Reply CONFIRM to confirm or CANCEL to reschedule.',
      variables: ['serviceName', 'timeText', 'time'],
      category: 'booking'
    },
    booking_cancellation: {
      name: 'Booking Cancellation',
      description: 'Sent when booking is cancelled',
      template: 'Your appointment for {{serviceName}} on {{date}} at {{time}} has been cancelled. To reschedule, please contact Ocean Soul Sparkles. Thank you!',
      variables: ['serviceName', 'date', 'time'],
      category: 'booking'
    },
    payment_receipt: {
      name: 'Payment Receipt',
      description: 'Sent after successful payment',
      template: 'Payment received! ${{amount}} for {{serviceName}}. Receipt #{{receiptNumber}}. Thank you for choosing Ocean Soul Sparkles!',
      variables: ['amount', 'serviceName', 'receiptNumber'],
      category: 'payment'
    },
    staff_schedule: {
      name: 'Staff Schedule Notification',
      description: 'Sent to staff about schedule updates',
      template: 'Schedule update: You have {{appointmentCount}} appointments on {{date}}. Check the admin portal for details. - Ocean Soul Sparkles',
      variables: ['appointmentCount', 'date'],
      category: 'staff'
    },
    staff_shift_reminder: {
      name: 'Staff Shift Reminder',
      description: 'Sent to remind staff of upcoming shifts',
      template: 'Shift reminder: You\'re scheduled to work {{startTime}} - {{endTime}} on {{date}}. See you at Ocean Soul Sparkles!',
      variables: ['startTime', 'endTime', 'date'],
      category: 'staff'
    },
    low_inventory_alert: {
      name: 'Low Inventory Alert',
      description: 'Sent to managers when inventory is low',
      template: 'Low inventory alert: {{itemNames}} need restocking. Check admin portal for details.',
      variables: ['itemNames'],
      category: 'admin'
    },
    customer_welcome: {
      name: 'Customer Welcome',
      description: 'Sent to new customers',
      template: 'Welcome to Ocean Soul Sparkles, {{name}}! We\'re excited to help you sparkle. Book your next appointment online or reply to this message.',
      variables: ['name'],
      category: 'customer'
    },
    promotional: {
      name: 'Promotional Message',
      description: 'Sent for promotions and special offers',
      template: '{{title}} at Ocean Soul Sparkles! {{description}} Valid until {{expiryDate}}. Book now!',
      variables: ['title', 'description', 'expiryDate'],
      category: 'marketing'
    },
    birthday: {
      name: 'Birthday Message',
      description: 'Sent on customer birthdays',
      template: 'Happy Birthday, {{name}}! 🎉 Celebrate with us - enjoy 15% off your next service at Ocean Soul Sparkles. Valid for 30 days!',
      variables: ['name'],
      category: 'customer'
    },
    appointment_followup: {
      name: 'Appointment Follow-up',
      description: 'Sent after appointment completion',
      template: 'Thank you for visiting Ocean Soul Sparkles! How was your {{serviceName}} experience? We\'d love your feedback. Reply or visit our website to review.',
      variables: ['serviceName'],
      category: 'followup'
    },
    no_show_followup: {
      name: 'No-Show Follow-up',
      description: 'Sent when customer misses appointment',
      template: 'We missed you for your {{serviceName}} appointment today. Life happens! Please contact us to reschedule. - Ocean Soul Sparkles',
      variables: ['serviceName'],
      category: 'followup'
    },
    waitlist_notification: {
      name: 'Waitlist Notification',
      description: 'Sent when waitlist spot becomes available',
      template: 'Great news! A spot opened up for {{serviceName}} on {{date}} at {{time}}. Reply YES within 2 hours to confirm this appointment.',
      variables: ['serviceName', 'date', 'time'],
      category: 'booking'
    },
    service_reminder: {
      name: 'Service Reminder',
      description: 'Sent to remind customers of recurring services',
      template: 'Hi {{name}}! It\'s been {{daysSince}} days since your last {{serviceName}}. Ready for your next sparkle session? Book online or reply to schedule.',
      variables: ['name', 'daysSince', 'serviceName'],
      category: 'customer'
    },
    emergency_closure: {
      name: 'Emergency Closure',
      description: 'Sent during unexpected closures',
      template: 'Important: Ocean Soul Sparkles is temporarily closed due to {{reason}}. We\'ll contact you to reschedule your appointment. Thank you for understanding.',
      variables: ['reason'],
      category: 'admin'
    }
  };
}

module.exports = {
  bookingConfirmationTemplate,
  bookingReminderTemplate,
  bookingCancellationTemplate,
  paymentReceiptTemplate,
  staffScheduleTemplate,
  staffShiftReminderTemplate,
  lowInventoryAlertTemplate,
  customerWelcomeTemplate,
  promotionalTemplate,
  birthdayTemplate,
  appointmentFollowUpTemplate,
  noShowFollowUpTemplate,
  waitlistNotificationTemplate,
  serviceReminderTemplate,
  emergencyClosureTemplate,
  substituteVariables,
  getAllTemplates
};
