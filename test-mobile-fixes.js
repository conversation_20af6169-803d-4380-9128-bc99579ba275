/**
 * Ocean Soul Sparkles Admin - Mobile Navigation Test Script
 * Tests mobile hamburger menu and user dropdown functionality
 */

console.log('🔧 Starting Mobile Navigation Tests...');

// Test 1: Check if mobile navigation elements exist
function testMobileElementsExist() {
  console.log('\n📱 Test 1: Checking mobile navigation elements...');
  
  const hamburgerButton = document.querySelector('[class*="sidebarToggle"]');
  const userButton = document.querySelector('[class*="userButton"]');
  const mobileBottomNav = document.querySelector('[class*="mobileBottomNav"]');
  
  console.log('✅ Hamburger button found:', !!hamburgerButton);
  console.log('✅ User button found:', !!userButton);
  console.log('✅ Mobile bottom nav found:', !!mobileBottomNav);
  
  return {
    hamburgerButton: !!hamburgerButton,
    userButton: !!userButton,
    mobileBottomNav: !!mobileBottomNav
  };
}

// Test 2: Test hamburger menu functionality
function testHamburgerMenu() {
  console.log('\n🍔 Test 2: Testing hamburger menu...');
  
  const hamburgerButton = document.querySelector('[class*="sidebarToggle"]');
  if (!hamburgerButton) {
    console.error('❌ Hamburger button not found');
    return false;
  }
  
  try {
    // Simulate click
    hamburgerButton.click();
    console.log('✅ Hamburger button clicked successfully');
    
    // Check if menu appears
    setTimeout(() => {
      const mobileMenu = document.querySelector('[class*="MobileHamburgerMenu"]');
      const menuOverlay = document.querySelector('[class*="menuOverlay"]');
      
      console.log('✅ Mobile menu overlay found:', !!menuOverlay);
      console.log('✅ Mobile menu container found:', !!mobileMenu);
      
      if (menuOverlay) {
        const isOpen = menuOverlay.classList.contains('open') || 
                      menuOverlay.style.visibility === 'visible' ||
                      menuOverlay.style.opacity === '1';
        console.log('✅ Menu is open:', isOpen);
      }
    }, 100);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing hamburger menu:', error);
    return false;
  }
}

// Test 3: Test user dropdown functionality
function testUserDropdown() {
  console.log('\n👤 Test 3: Testing user dropdown...');
  
  const userButton = document.querySelector('[class*="userButton"]');
  if (!userButton) {
    console.error('❌ User button not found');
    return false;
  }
  
  try {
    // Simulate click
    userButton.click();
    console.log('✅ User button clicked successfully');
    
    // Check if dropdown appears
    setTimeout(() => {
      const userDropdown = document.querySelector('[class*="userDropdown"]');
      console.log('✅ User dropdown found:', !!userDropdown);
      
      if (userDropdown) {
        const isVisible = userDropdown.style.display !== 'none' &&
                         userDropdown.style.visibility !== 'hidden' &&
                         userDropdown.style.opacity !== '0';
        console.log('✅ Dropdown is visible:', isVisible);
        
        // Check z-index
        const zIndex = window.getComputedStyle(userDropdown).zIndex;
        console.log('✅ Dropdown z-index:', zIndex);
        console.log('✅ Z-index is high enough:', parseInt(zIndex) > 10000);
      }
    }, 100);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing user dropdown:', error);
    return false;
  }
}

// Test 4: Test mobile responsiveness
function testMobileResponsiveness() {
  console.log('\n📱 Test 4: Testing mobile responsiveness...');
  
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  };
  
  console.log('✅ Viewport size:', viewport);
  console.log('✅ Is mobile width:', viewport.width <= 768);
  
  // Check if mobile styles are applied
  const body = document.body;
  const isMobileLayout = window.getComputedStyle(body).getPropertyValue('--mobile-layout') === 'true' ||
                        viewport.width <= 768;
  
  console.log('✅ Mobile layout detected:', isMobileLayout);
  
  return isMobileLayout;
}

// Test 5: Test console errors
function testConsoleErrors() {
  console.log('\n🐛 Test 5: Monitoring console errors...');
  
  let errorCount = 0;
  const originalError = console.error;
  
  console.error = function(...args) {
    errorCount++;
    console.log(`❌ Console Error #${errorCount}:`, ...args);
    originalError.apply(console, args);
  };
  
  // Restore after 5 seconds
  setTimeout(() => {
    console.error = originalError;
    console.log(`✅ Error monitoring complete. Total errors: ${errorCount}`);
  }, 5000);
  
  return errorCount;
}

// Test 6: Test z-index stacking
function testZIndexStacking() {
  console.log('\n📚 Test 6: Testing z-index stacking...');
  
  const elements = [
    { name: 'Mobile Bottom Nav', selector: '[class*="mobileBottomNav"]', expectedZIndex: 9997 },
    { name: 'Mobile Overlay', selector: '[class*="mobileOverlay"]', expectedZIndex: 9998 },
    { name: 'Hamburger Menu', selector: '[class*="menuOverlay"]', expectedZIndex: 10000 },
    { name: 'User Dropdown', selector: '[class*="userDropdown"]', expectedZIndex: 10001 },
    { name: 'Notification Dropdown', selector: '[class*="notificationDropdown"]', expectedZIndex: 10001 }
  ];
  
  elements.forEach(element => {
    const el = document.querySelector(element.selector);
    if (el) {
      const zIndex = parseInt(window.getComputedStyle(el).zIndex) || 0;
      const isCorrect = zIndex >= element.expectedZIndex;
      console.log(`${isCorrect ? '✅' : '❌'} ${element.name}: ${zIndex} (expected: ${element.expectedZIndex})`);
    } else {
      console.log(`⚠️ ${element.name}: Element not found`);
    }
  });
}

// Run all tests
function runAllTests() {
  console.log('🚀 Running all mobile navigation tests...');
  
  const results = {
    elementsExist: testMobileElementsExist(),
    hamburgerMenu: false,
    userDropdown: false,
    mobileResponsive: testMobileResponsiveness(),
    consoleErrors: testConsoleErrors()
  };
  
  // Run async tests
  setTimeout(() => {
    results.hamburgerMenu = testHamburgerMenu();
  }, 500);
  
  setTimeout(() => {
    results.userDropdown = testUserDropdown();
  }, 1000);
  
  setTimeout(() => {
    testZIndexStacking();
  }, 1500);
  
  // Summary
  setTimeout(() => {
    console.log('\n📊 Test Summary:');
    console.log('================');
    Object.entries(results).forEach(([test, result]) => {
      console.log(`${result ? '✅' : '❌'} ${test}: ${result}`);
    });
    console.log('\n🔧 Mobile navigation testing complete!');
  }, 2000);
  
  return results;
}

// Auto-run tests when script loads
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
  } else {
    runAllTests();
  }
}

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testMobileElementsExist,
    testHamburgerMenu,
    testUserDropdown,
    testMobileResponsiveness,
    testConsoleErrors,
    testZIndexStacking,
    runAllTests
  };
}
