import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../../hooks/useAuth';
import AdminLayout from '../../../components/admin/AdminLayout';
import styles from '../../../styles/admin/StaffPerformance.module.css';

interface PerformanceMetric {
  id: string;
  metric_date: string;
  total_bookings: number;
  completed_bookings: number;
  cancelled_bookings: number;
  total_revenue: number;
  total_tips: number;
  average_rating: number;
  customer_feedback_count: number;
  hours_worked: number;
  punctuality_score: number;
  created_at: string;
}

interface PerformanceSummary {
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  totalRevenue: number;
  totalTips: number;
  totalHours: number;
  averageRating: number;
  averagePunctuality: number;
  completionRate: number;
  cancellationRate: number;
  totalPeriods: number;
}

export default function StaffPerformancePage() {
  const { user } = useAuth();
  const router = useRouter();
  const { staff_id } = router.query;
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [summary, setSummary] = useState<PerformanceSummary>({
    totalBookings: 0,
    completedBookings: 0,
    cancelledBookings: 0,
    totalRevenue: 0,
    totalTips: 0,
    totalHours: 0,
    averageRating: 0,
    averagePunctuality: 0,
    completionRate: 0,
    cancellationRate: 0,
    totalPeriods: 0
  });
  const [staffInfo, setStaffInfo] = useState<any>(null);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end: new Date().toISOString().split('T')[0] // today
  });

  useEffect(() => {
    if (staff_id) {
      loadPerformanceData();
      loadStaffInfo();
    }
  }, [staff_id, dateRange]);

  const loadPerformanceData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        staff_id: staff_id as string,
        start_date: dateRange.start,
        end_date: dateRange.end
      });

      const response = await fetch(`/api/admin/staff/performance?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMetrics(data.metrics || []);
        setSummary(data.summary || {});
      } else {
        setError('Failed to load performance data');
      }
    } catch (error) {
      console.error('Error loading performance data:', error);
      setError('Failed to load performance data');
    } finally {
      setLoading(false);
    }
  };

  const loadStaffInfo = async () => {
    try {
      const response = await fetch(`/api/admin/staff?id=${staff_id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStaffInfo(data.staff);
      }
    } catch (error) {
      console.error('Error loading staff info:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return '#10b981';
    if (rating >= 4) return '#84cc16';
    if (rating >= 3) return '#f59e0b';
    if (rating >= 2) return '#f97316';
    return '#ef4444';
  };

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 90) return '#10b981';
    if (percentage >= 75) return '#84cc16';
    if (percentage >= 60) return '#f59e0b';
    return '#ef4444';
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading performance data...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Staff Performance | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="View staff performance metrics and analytics" />
      </Head>

      <div className={styles.performanceContainer}>
        <header className={styles.header}>
          <div className={styles.headerLeft}>
            <h1 className={styles.title}>Staff Performance</h1>
            {staffInfo && (
              <p className={styles.subtitle}>
                {staffInfo.firstName} {staffInfo.lastName} - {staffInfo.role}
              </p>
            )}
          </div>
          <div className={styles.headerActions}>
            <button
              onClick={() => router.back()}
              className={styles.backBtn}
            >
              ← Back to Staff
            </button>
          </div>
        </header>

        {error && (
          <div className={styles.errorMessage}>
            {error}
            <button onClick={() => setError(null)} className={styles.closeError}>×</button>
          </div>
        )}

        <div className={styles.dateRangeSection}>
          <div className={styles.dateInputs}>
            <div className={styles.inputGroup}>
              <label htmlFor="startDate">Start Date:</label>
              <input
                type="date"
                id="startDate"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className={styles.dateInput}
              />
            </div>
            <div className={styles.inputGroup}>
              <label htmlFor="endDate">End Date:</label>
              <input
                type="date"
                id="endDate"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className={styles.dateInput}
              />
            </div>
          </div>
        </div>

        <div className={styles.summarySection}>
          <div className={styles.summaryGrid}>
            <div className={styles.summaryCard}>
              <div className={styles.cardIcon}>📅</div>
              <div className={styles.cardContent}>
                <h3>Total Bookings</h3>
                <div className={styles.cardValue}>{summary.totalBookings}</div>
                <div className={styles.cardSubtext}>
                  {summary.completedBookings} completed, {summary.cancelledBookings} cancelled
                </div>
              </div>
            </div>

            <div className={styles.summaryCard}>
              <div className={styles.cardIcon}>💰</div>
              <div className={styles.cardContent}>
                <h3>Total Revenue</h3>
                <div className={styles.cardValue}>{formatCurrency(summary.totalRevenue)}</div>
                <div className={styles.cardSubtext}>
                  + {formatCurrency(summary.totalTips)} in tips
                </div>
              </div>
            </div>

            <div className={styles.summaryCard}>
              <div className={styles.cardIcon}>⭐</div>
              <div className={styles.cardContent}>
                <h3>Average Rating</h3>
                <div 
                  className={styles.cardValue}
                  style={{ color: getRatingColor(summary.averageRating) }}
                >
                  {summary.averageRating.toFixed(1)}
                </div>
                <div className={styles.cardSubtext}>
                  Based on customer feedback
                </div>
              </div>
            </div>

            <div className={styles.summaryCard}>
              <div className={styles.cardIcon}>✅</div>
              <div className={styles.cardContent}>
                <h3>Completion Rate</h3>
                <div 
                  className={styles.cardValue}
                  style={{ color: getPerformanceColor(summary.completionRate) }}
                >
                  {summary.completionRate.toFixed(1)}%
                </div>
                <div className={styles.cardSubtext}>
                  {summary.completedBookings} of {summary.totalBookings} bookings
                </div>
              </div>
            </div>

            <div className={styles.summaryCard}>
              <div className={styles.cardIcon}>⏰</div>
              <div className={styles.cardContent}>
                <h3>Hours Worked</h3>
                <div className={styles.cardValue}>{summary.totalHours.toFixed(1)}</div>
                <div className={styles.cardSubtext}>
                  Punctuality: {summary.averagePunctuality.toFixed(1)}%
                </div>
              </div>
            </div>

            <div className={styles.summaryCard}>
              <div className={styles.cardIcon}>📊</div>
              <div className={styles.cardContent}>
                <h3>Cancellation Rate</h3>
                <div 
                  className={styles.cardValue}
                  style={{ color: summary.cancellationRate > 10 ? '#ef4444' : '#10b981' }}
                >
                  {summary.cancellationRate.toFixed(1)}%
                </div>
                <div className={styles.cardSubtext}>
                  {summary.cancelledBookings} cancelled bookings
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className={styles.metricsSection}>
          <h2 className={styles.sectionTitle}>Daily Performance Metrics</h2>
          
          {metrics.length === 0 ? (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>📈</div>
              <h3>No Performance Data</h3>
              <p>No performance metrics found for the selected date range.</p>
            </div>
          ) : (
            <div className={styles.metricsTable}>
              <div className={styles.tableContainer}>
                <table className={styles.table}>
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>Bookings</th>
                      <th>Completed</th>
                      <th>Revenue</th>
                      <th>Tips</th>
                      <th>Rating</th>
                      <th>Hours</th>
                      <th>Punctuality</th>
                    </tr>
                  </thead>
                  <tbody>
                    {metrics.map((metric) => (
                      <tr key={metric.id} className={styles.tableRow}>
                        <td className={styles.dateCell}>
                          {formatDate(metric.metric_date)}
                        </td>
                        <td className={styles.numberCell}>
                          {metric.total_bookings}
                        </td>
                        <td className={styles.numberCell}>
                          {metric.completed_bookings}
                          {metric.total_bookings > 0 && (
                            <span className={styles.percentage}>
                              ({Math.round((metric.completed_bookings / metric.total_bookings) * 100)}%)
                            </span>
                          )}
                        </td>
                        <td className={styles.currencyCell}>
                          {formatCurrency(metric.total_revenue)}
                        </td>
                        <td className={styles.currencyCell}>
                          {formatCurrency(metric.total_tips)}
                        </td>
                        <td className={styles.ratingCell}>
                          {metric.average_rating ? (
                            <span style={{ color: getRatingColor(metric.average_rating) }}>
                              {metric.average_rating.toFixed(1)}
                            </span>
                          ) : (
                            <span className={styles.noData}>-</span>
                          )}
                        </td>
                        <td className={styles.numberCell}>
                          {metric.hours_worked ? metric.hours_worked.toFixed(1) : '-'}
                        </td>
                        <td className={styles.percentageCell}>
                          {metric.punctuality_score ? (
                            <span style={{ color: getPerformanceColor(metric.punctuality_score) }}>
                              {metric.punctuality_score.toFixed(1)}%
                            </span>
                          ) : (
                            <span className={styles.noData}>-</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
