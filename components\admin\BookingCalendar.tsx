import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import styles from '../../styles/admin/BookingCalendar.module.css';

interface Booking {
  id: string;
  customer_name: string;
  service_name: string;
  artist_name: string;
  start_time: string;
  end_time: string;
  status: string;
  total_amount: number;
}

interface BookingCalendarProps {
  bookings: Booking[];
  onBookingClick?: (booking: Booking) => void;
  onDateClick?: (date: Date) => void;
}

export default function BookingCalendar({ bookings, onBookingClick, onDateClick }: BookingCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [calendarDays, setCalendarDays] = useState<Date[]>([]);
  const [bookingsByDate, setBookingsByDate] = useState<Record<string, Booking[]>>({});

  useEffect(() => {
    generateCalendarDays();
    organizeBookingsByDate();
  }, [currentDate, bookings]);

  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Get first day of the month
    const firstDay = new Date(year, month, 1);
    // Get last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of week for the first day (0 = Sunday, 1 = Monday, etc.)
    const startingDayOfWeek = firstDay.getDay();
    
    // Calculate how many days to show from previous month
    const daysFromPrevMonth = startingDayOfWeek;
    
    // Calculate total days to show (6 weeks = 42 days)
    const totalDays = 42;
    
    const days: Date[] = [];
    
    // Add days from previous month
    for (let i = daysFromPrevMonth - 1; i >= 0; i--) {
      const date = new Date(year, month, -i);
      days.push(date);
    }
    
    // Add days from current month
    for (let day = 1; day <= lastDay.getDate(); day++) {
      const date = new Date(year, month, day);
      days.push(date);
    }
    
    // Add days from next month to fill the grid
    const remainingDays = totalDays - days.length;
    for (let day = 1; day <= remainingDays; day++) {
      const date = new Date(year, month + 1, day);
      days.push(date);
    }
    
    setCalendarDays(days);
  };

  const organizeBookingsByDate = () => {
    const organized: Record<string, Booking[]> = {};
    
    bookings.forEach(booking => {
      const bookingDate = new Date(booking.start_time);
      const dateKey = bookingDate.toISOString().split('T')[0];
      
      if (!organized[dateKey]) {
        organized[dateKey] = [];
      }
      organized[dateKey].push(booking);
    });
    
    setBookingsByDate(organized);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'cancelled': return '#dc3545';
      case 'completed': return '#17a2b8';
      case 'no_show': return '#6c757d';
      default: return '#6c757d';
    }
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className={styles.calendarContainer}>
      <div className={styles.calendarHeader}>
        <div className={styles.monthNavigation}>
          <button onClick={() => navigateMonth('prev')} className={styles.navButton}>
            ←
          </button>
          <h2 className={styles.monthTitle}>
            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
          </h2>
          <button onClick={() => navigateMonth('next')} className={styles.navButton}>
            →
          </button>
        </div>
        <button onClick={goToToday} className={styles.todayButton}>
          Today
        </button>
      </div>

      <div className={styles.calendar}>
        <div className={styles.dayHeaders}>
          {dayNames.map(day => (
            <div key={day} className={styles.dayHeader}>
              {day}
            </div>
          ))}
        </div>

        <div className={styles.calendarGrid}>
          {calendarDays.map((date, index) => {
            const dateKey = date.toISOString().split('T')[0];
            const dayBookings = bookingsByDate[dateKey] || [];
            
            return (
              <div
                key={index}
                className={`${styles.calendarDay} ${
                  isToday(date) ? styles.today : ''
                } ${
                  !isCurrentMonth(date) ? styles.otherMonth : ''
                }`}
                onClick={() => onDateClick?.(date)}
              >
                <div className={styles.dayNumber}>
                  {date.getDate()}
                </div>
                
                <div className={styles.bookingsContainer}>
                  {dayBookings.slice(0, 3).map(booking => (
                    <div
                      key={booking.id}
                      className={styles.bookingItem}
                      style={{ borderLeftColor: getStatusColor(booking.status) }}
                      onClick={(e) => {
                        e.stopPropagation();
                        onBookingClick?.(booking);
                      }}
                      title={`${booking.customer_name} - ${booking.service_name} at ${formatTime(booking.start_time)}`}
                    >
                      <div className={styles.bookingTime}>
                        {formatTime(booking.start_time)}
                      </div>
                      <div className={styles.bookingCustomer}>
                        {booking.customer_name}
                      </div>
                      <div className={styles.bookingService}>
                        {booking.service_name}
                      </div>
                    </div>
                  ))}
                  
                  {dayBookings.length > 3 && (
                    <div className={styles.moreBookings}>
                      +{dayBookings.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
