import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import AdminLayout from '@/components/admin/AdminLayout'
import styles from '@/styles/admin/Notifications.module.css'

/**
 * Email Notifications Management Page
 * Allows admins to test and manage email notifications
 */
export default function NotificationsManagement() {
  const { user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(true)
  const [emailStatus, setEmailStatus] = useState(null)
  const [testEmail, setTestEmail] = useState('')
  const [testResult, setTestResult] = useState(null)
  const [sending, setSending] = useState(false)

  useEffect(() => {
    if (!authLoading && user) {
      fetchEmailStatus()
    }
  }, [authLoading, user])

  const fetchEmailStatus = async () => {
    try {
      const response = await fetch('/api/admin/notifications/email')
      const data = await response.json()
      
      if (response.ok) {
        setEmailStatus(data.status)
      } else {
        console.error('Failed to fetch email status:', data.error)
      }
    } catch (error) {
      console.error('Error fetching email status:', error)
    } finally {
      setLoading(false)
    }
  }

  const sendTestEmail = async () => {
    if (!testEmail) {
      alert('Please enter an email address')
      return
    }

    setSending(true)
    setTestResult(null)

    try {
      const response = await fetch('/api/admin/notifications/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'test_email',
          data: { to: testEmail }
        })
      })

      const data = await response.json()
      
      if (response.ok) {
        setTestResult({ success: true, message: 'Test email sent successfully!' })
      } else {
        setTestResult({ success: false, message: data.message || 'Failed to send test email' })
      }
    } catch (error) {
      setTestResult({ success: false, message: 'Network error occurred' })
    } finally {
      setSending(false)
    }
  }

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading notifications management...</p>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1>📧 Email Notifications</h1>
          <p>Manage and test email notification system</p>
        </div>

        {/* Email System Status */}
        <div className={styles.statusCard}>
          <h2>Email System Status</h2>
          {emailStatus ? (
            <div className={styles.statusGrid}>
              <div className={styles.statusItem}>
                <span className={styles.statusLabel}>Configuration:</span>
                <span className={`${styles.statusValue} ${emailStatus.configured ? styles.success : styles.warning}`}>
                  {emailStatus.configured ? '✅ Configured' : '⚠️ Not Configured'}
                </span>
              </div>
              <div className={styles.statusItem}>
                <span className={styles.statusLabel}>SMTP Host:</span>
                <span className={styles.statusValue}>{emailStatus.smtpHost}</span>
              </div>
              <div className={styles.statusItem}>
                <span className={styles.statusLabel}>SMTP User:</span>
                <span className={styles.statusValue}>{emailStatus.smtpUser}</span>
              </div>
              <div className={styles.statusItem}>
                <span className={styles.statusLabel}>SMTP Port:</span>
                <span className={styles.statusValue}>{emailStatus.smtpPort}</span>
              </div>
            </div>
          ) : (
            <p>Loading status...</p>
          )}
        </div>

        {/* Test Email Section */}
        <div className={styles.testCard}>
          <h2>Send Test Email</h2>
          <p>Send a test email to verify the email system is working correctly.</p>
          
          <div className={styles.testForm}>
            <input
              type="email"
              placeholder="Enter email address"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              className={styles.emailInput}
            />
            <button
              onClick={sendTestEmail}
              disabled={sending || !testEmail}
              className={styles.testButton}
            >
              {sending ? 'Sending...' : 'Send Test Email'}
            </button>
          </div>

          {testResult && (
            <div className={`${styles.testResult} ${testResult.success ? styles.success : styles.error}`}>
              {testResult.message}
            </div>
          )}
        </div>

        {/* Email Templates Info */}
        <div className={styles.templatesCard}>
          <h2>Available Email Templates</h2>
          <div className={styles.templatesList}>
            <div className={styles.templateItem}>
              <h3>📅 Booking Confirmation</h3>
              <p>Sent automatically when a booking is confirmed</p>
            </div>
            <div className={styles.templateItem}>
              <h3>⏰ Booking Reminder</h3>
              <p>Sent 24 hours before appointment</p>
            </div>
            <div className={styles.templateItem}>
              <h3>❌ Booking Cancellation</h3>
              <p>Sent when a booking is cancelled</p>
            </div>
            <div className={styles.templateItem}>
              <h3>💳 Payment Receipt</h3>
              <p>Sent after successful payment</p>
            </div>
            <div className={styles.templateItem}>
              <h3>👥 Staff Notifications</h3>
              <p>Internal notifications for staff</p>
            </div>
            <div className={styles.templateItem}>
              <h3>📦 Low Inventory Alerts</h3>
              <p>Alerts when inventory is running low</p>
            </div>
          </div>
        </div>

        {/* Configuration Help */}
        {emailStatus && !emailStatus.configured && (
          <div className={styles.helpCard}>
            <h2>Email Configuration</h2>
            <p>To enable email notifications, add these environment variables:</p>
            <div className={styles.envVars}>
              <code>SMTP_HOST=smtp.gmail.com</code>
              <code>SMTP_USER=<EMAIL></code>
              <code>SMTP_PASS=your-app-password</code>
              <code>SMTP_PORT=587</code>
            </div>
            <p><strong>Note:</strong> For Gmail, use an App Password instead of your regular password.</p>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
