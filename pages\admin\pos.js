import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import ServiceBookingAvailability from '@/components/admin/pos/ServiceBookingAvailability';
import POSCheckout from '@/components/admin/pos/POSCheckout';
import MobilePOS from '@/components/admin/mobile/MobilePOS';
import styles from '@/styles/admin/POS.module.css';

/**
 * Point of Sale System
 * 
 * This page provides a comprehensive POS interface for processing transactions,
 * managing services, handling customer interactions, and booking appointments.
 */
export default function POSSystem() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [services, setServices] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [isMobile, setIsMobile] = useState(false);
  
  // POS Flow State
  const [currentView, setCurrentView] = useState('services'); // 'services', 'booking', 'checkout'
  const [selectedService, setSelectedService] = useState(null);
  const [bookingSelection, setBookingSelection] = useState(null); // { artist, tier, timeSlot }
  
  // Legacy cart state (for simple add-to-cart flow)
  const [cart, setCart] = useState([]);
  const [customer, setCustomer] = useState(null);
  const [total, setTotal] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [customerSearchTerm, setCustomerSearchTerm] = useState('');

  // Load services and customers
  const loadData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      
      // Load services with artist and pricing data
      const servicesResponse = await fetch('/api/admin/services', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Load customers
      const customersResponse = await fetch('/api/admin/customers', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Load products
      const productsResponse = await fetch('/api/admin/products', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json();
        // Process services to include artist and pricing tier information
        const processedServices = (servicesData.services || []).map(service => ({
          ...service,
          // Mock data - in real implementation, this would come from the API
          availableArtists: [
            {
              id: 'artist1',
              name: 'Emma Wilson',
              specialties: [service.category],
              isAvailableToday: true
            },
            {
              id: 'artist2', 
              name: 'Sarah Johnson',
              specialties: [service.category],
              isAvailableToday: true
            }
          ],
          pricing_tiers: [
            {
              id: 'tier1',
              name: 'Basic',
              duration: 30,
              price: service.price || 50,
              description: 'Basic service package'
            },
            {
              id: 'tier2',
              name: 'Premium',
              duration: 60,
              price: (service.price || 50) * 1.5,
              description: 'Premium service package with extras'
            }
          ]
        }));
        setServices(processedServices);
      }

      if (customersResponse.ok) {
        const customersData = await customersResponse.json();
        setCustomers(customersData.customers || []);
      }

      if (productsResponse.ok) {
        const productsData = await productsResponse.json();
        setProducts(productsData.products || []);
      }
    } catch (error) {
      console.error('Error loading POS data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Check for mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (!authLoading && user) {
      loadData();
    }
  }, [authLoading, user]);

  // Calculate total when cart changes
  useEffect(() => {
    const newTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    setTotal(newTotal);
  }, [cart]);

  // Service selection for booking flow
  const handleServiceSelect = (service) => {
    setSelectedService(service);
    setCurrentView('booking');
  };

  // Handle booking slot selection
  const handleBookingSlotSelect = (artist, tier, timeSlot) => {
    setBookingSelection({ artist, tier, timeSlot });
    setCurrentView('checkout');
  };

  // Handle checkout completion
  const handleCheckoutComplete = (result) => {
    console.log('Checkout completed:', result);
    // Reset to services view
    setCurrentView('services');
    setSelectedService(null);
    setBookingSelection(null);
    alert('Booking completed successfully!');
  };

  // Handle mobile POS transaction completion
  const handleMobileTransactionComplete = (transaction) => {
    console.log('Mobile transaction completed:', transaction);
    alert(`Transaction completed! Total: $${transaction.total.toFixed(2)}`);
    // Could integrate with receipt printing or other post-transaction actions
  };

  // Handle back navigation
  const handleBackToServices = () => {
    setCurrentView('services');
    setSelectedService(null);
    setBookingSelection(null);
  };

  const handleBackToBooking = () => {
    setCurrentView('booking');
    setBookingSelection(null);
  };

  // Legacy cart functions (keeping for backward compatibility)
  const addToCart = (service) => {
    const existingItem = cart.find(item => item.id === service.id);
    
    if (existingItem) {
      setCart(cart.map(item =>
        item.id === service.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, {
        id: service.id,
        name: service.name,
        price: service.price || 0,
        quantity: 1,
        duration: service.duration
      }]);
    }
  };

  const removeFromCart = (itemId) => {
    setCart(cart.filter(item => item.id !== itemId));
  };

  const updateQuantity = (itemId, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setCart(cart.map(item =>
      item.id === itemId
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  const selectCustomer = (selectedCustomer) => {
    setCustomer(selectedCustomer);
    setCustomerSearchTerm('');
  };

  const clearCart = () => {
    setCart([]);
    setCustomer(null);
    setTotal(0);
  };

  const processTransaction = async () => {
    if (cart.length === 0) return;

    setIsProcessing(true);
    try {
      const token = localStorage.getItem('admin-token');
      
      // Create booking record
      const bookingData = {
        customer_id: customer?.id,
        customer_name: customer?.name || 'Walk-in Customer',
        customer_email: customer?.email || '',
        customer_phone: customer?.phone || '',
        services: cart.map(item => ({
          service_id: item.id,
          service_name: item.name,
          quantity: item.quantity,
          price: item.price,
          duration: item.duration
        })),
        total_amount: total,
        payment_method: paymentMethod,
        status: 'completed',
        booking_date: new Date().toISOString(),
        notes: 'POS Transaction'
      };

      const response = await fetch('/api/admin/bookings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bookingData)
      });

      if (response.ok) {
        alert('Transaction completed successfully!');
        clearCart();
      } else {
        throw new Error('Failed to process transaction');
      }
    } catch (error) {
      console.error('Error processing transaction:', error);
      alert('Error processing transaction. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatDuration = (minutes) => {
    if (!minutes) return '';
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
    (customer.phone && customer.phone.includes(customerSearchTerm))
  );

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading POS system...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return null; // Will redirect to login via useAuth
  }

  // Mobile POS Interface
  if (isMobile) {
    return (
      <AdminLayout>
        <Head>
          <title>Mobile POS | Ocean Soul Sparkles Admin</title>
          <meta name="description" content="Mobile point of sale interface" />
        </Head>

        <MobilePOS
          onTransactionComplete={handleMobileTransactionComplete}
          services={services}
          products={products}
          customers={customers}
        />
      </AdminLayout>
    );
  }

  // Desktop POS Interface
  return (
    <AdminLayout>
      <Head>
        <title>Point of Sale | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Process transactions and manage sales" />
      </Head>

      <div className={styles.posContainer}>
        {/* Services Selection View */}
        {currentView === 'services' && (
          <div className={styles.posContent}>
            <div className={styles.servicesSection}>
              <div className={styles.sectionHeader}>
                <h2>Services</h2>
                <p>Select a service to start booking with calendar and payment processing</p>
                <input
                  type="text"
                  placeholder="Search services..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={styles.searchInput}
                />
              </div>

              <div className={styles.servicesGrid}>
                {filteredServices.map((service) => (
                  <div key={service.id} className={styles.serviceCard}>
                    <h3 className={styles.serviceName}>{service.name}</h3>
                    <p className={styles.serviceCategory}>{service.category}</p>
                    <div className={styles.serviceDetails}>
                      <span className={styles.price}>
                        {service.price ? formatCurrency(service.price) : 'From $50'}
                      </span>
                      {service.duration && (
                        <span className={styles.duration}>{formatDuration(service.duration)}</span>
                      )}
                    </div>
                    <div className={styles.serviceActions}>
                      <button
                        onClick={() => handleServiceSelect(service)}
                        className={styles.bookServiceBtn}
                      >
                        📅 Book with Calendar
                      </button>
                      <button
                        onClick={() => addToCart(service)}
                        className={styles.addToCartBtn}
                        disabled={!service.price}
                      >
                        🛒 Quick Add
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Cart Section (Legacy) */}
            {cart.length > 0 && (
              <div className={styles.quickCart}>
                <h3>Quick Cart ({cart.length} items)</h3>
                <div className={styles.cartItems}>
                  {cart.map((item) => (
                    <div key={item.id} className={styles.cartItem}>
                      <div className={styles.itemInfo}>
                        <span className={styles.itemName}>{item.name}</span>
                        <span className={styles.itemPrice}>{formatCurrency(item.price)}</span>
                      </div>
                      <div className={styles.quantityControls}>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className={styles.quantityBtn}
                        >
                          -
                        </button>
                        <span className={styles.quantity}>{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className={styles.quantityBtn}
                        >
                          +
                        </button>
                      </div>
                      <span className={styles.itemTotal}>
                        {formatCurrency(item.price * item.quantity)}
                      </span>
                      <button
                        onClick={() => removeFromCart(item.id)}
                        className={styles.removeBtn}
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
                <div className={styles.cartTotal}>
                  Total: {formatCurrency(total)}
                </div>
                <div className={styles.cartActions}>
                  <button
                    onClick={clearCart}
                    className={styles.clearBtn}
                  >
                    Clear
                  </button>
                  <button
                    onClick={processTransaction}
                    className={styles.quickCheckoutBtn}
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processing...' : 'Quick Checkout'}
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Booking Calendar View */}
        {currentView === 'booking' && selectedService && (
          <ServiceBookingAvailability
            service={selectedService}
            onBookingSlotSelect={handleBookingSlotSelect}
            onBack={handleBackToServices}
          />
        )}

        {/* Checkout View */}
        {currentView === 'checkout' && bookingSelection && (
          <POSCheckout
            service={selectedService}
            artist={bookingSelection.artist}
            tier={bookingSelection.tier}
            timeSlot={bookingSelection.timeSlot}
            onBack={handleBackToBooking}
            onComplete={handleCheckoutComplete}
          />
        )}
      </div>
    </AdminLayout>
  );
}
