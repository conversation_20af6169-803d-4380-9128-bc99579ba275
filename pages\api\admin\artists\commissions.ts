import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface CommissionTransaction {
  artist_id: string;
  booking_id: string;
  payment_id?: string;
  service_amount: number;
  commission_rate: number;
  commission_amount: number;
  tip_amount?: number;
  total_earnings: number;
  status?: 'pending' | 'calculated' | 'paid' | 'disputed';
  payment_method?: 'cash' | 'bank_transfer' | 'payroll';
  notes?: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const requestId = uuidv4();
  
  try {
    // Authentication check
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid authentication token required',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { 
        artist_id, 
        status, 
        start_date, 
        end_date,
        payment_method,
        limit = 50, 
        offset = 0 
      } = req.query;

      let query = supabase
        .from('commission_transactions')
        .select(`
          id,
          artist_id,
          booking_id,
          payment_id,
          service_amount,
          commission_rate,
          commission_amount,
          tip_amount,
          total_earnings,
          status,
          payment_method,
          paid_at,
          paid_by,
          notes,
          created_at,
          updated_at,
          artist_profiles!inner(
            id,
            name,
            email,
            commission_rate as default_commission_rate
          ),
          bookings!inner(
            id,
            start_time,
            end_time,
            status as booking_status,
            customers(
              id,
              first_name,
              last_name,
              email
            ),
            services(
              id,
              name,
              category
            )
          ),
          payments(
            id,
            amount,
            method,
            status as payment_status,
            payment_time
          ),
          payer:admin_users!paid_by(
            id,
            first_name,
            last_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      // Apply filters
      if (artist_id) {
        query = query.eq('artist_id', artist_id);
      }
      if (status) {
        query = query.eq('status', status);
      }
      if (payment_method) {
        query = query.eq('payment_method', payment_method);
      }
      if (start_date) {
        query = query.gte('created_at', start_date);
      }
      if (end_date) {
        query = query.lte('created_at', end_date);
      }

      // Apply pagination
      query = query.range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1);

      const { data: commissions, error } = await query;

      if (error) {
        console.error('Commissions fetch error:', error);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to fetch commission transactions',
          requestId
        });
      }

      // Get total count for pagination
      const { count: totalCount } = await supabase
        .from('commission_transactions')
        .select('*', { count: 'exact', head: true });

      // Calculate summary statistics
      const commissionsData = (commissions || []) as any[];
      const stats = {
        total: commissionsData.length,
        totalEarnings: commissionsData.reduce((sum, c) => sum + (c.total_earnings || 0), 0),
        totalCommissions: commissionsData.reduce((sum, c) => sum + (c.commission_amount || 0), 0),
        totalTips: commissionsData.reduce((sum, c) => sum + (c.tip_amount || 0), 0),
        pending: commissionsData.filter(c => c.status === 'pending').length,
        calculated: commissionsData.filter(c => c.status === 'calculated').length,
        paid: commissionsData.filter(c => c.status === 'paid').length,
        disputed: commissionsData.filter(c => c.status === 'disputed').length,
        averageCommissionRate: commissionsData.length > 0
          ? commissionsData.reduce((sum, c) => sum + (c.commission_rate || 0), 0) / commissionsData.length
          : 0
      };

      return res.status(200).json({
        commissions: commissionsData,
        stats,
        pagination: {
          total: totalCount || 0,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: (parseInt(offset as string) + parseInt(limit as string)) < (totalCount || 0)
        },
        requestId
      });
    }

    if (req.method === 'POST') {
      const commissionData: CommissionTransaction = req.body;

      // Validate required fields
      if (!commissionData.artist_id || !commissionData.booking_id || 
          commissionData.service_amount === undefined || commissionData.commission_rate === undefined) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Missing required fields: artist_id, booking_id, service_amount, commission_rate',
          requestId
        });
      }

      // Verify artist exists
      const { data: artist, error: artistError } = await supabase
        .from('artist_profiles')
        .select('id, name, commission_rate')
        .eq('id', commissionData.artist_id)
        .single();

      if (artistError || !artist) {
        return res.status(404).json({
          error: 'Artist not found',
          message: 'The specified artist does not exist',
          requestId
        });
      }

      // Verify booking exists
      const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .select('id, total_amount, status, artist_id')
        .eq('id', commissionData.booking_id)
        .single();

      if (bookingError || !booking) {
        return res.status(404).json({
          error: 'Booking not found',
          message: 'The specified booking does not exist',
          requestId
        });
      }

      // Verify booking belongs to the artist
      if (booking.artist_id !== commissionData.artist_id) {
        return res.status(400).json({
          error: 'Invalid booking',
          message: 'The booking does not belong to the specified artist',
          requestId
        });
      }

      // Check if commission already exists for this booking
      const { data: existingCommission, error: existingError } = await supabase
        .from('commission_transactions')
        .select('id')
        .eq('booking_id', commissionData.booking_id)
        .single();

      if (existingError && existingError.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Existing commission check error:', existingError);
      } else if (existingCommission) {
        return res.status(409).json({
          error: 'Commission exists',
          message: 'A commission transaction already exists for this booking',
          requestId
        });
      }

      // Calculate commission amount
      const commissionAmount = (commissionData.service_amount * commissionData.commission_rate) / 100;
      const tipAmount = commissionData.tip_amount || 0;
      const totalEarnings = commissionAmount + tipAmount;

      const newCommission = {
        id: uuidv4(),
        artist_id: commissionData.artist_id,
        booking_id: commissionData.booking_id,
        payment_id: commissionData.payment_id || null,
        service_amount: commissionData.service_amount,
        commission_rate: commissionData.commission_rate,
        commission_amount: commissionAmount,
        tip_amount: tipAmount,
        total_earnings: totalEarnings,
        status: commissionData.status || 'calculated',
        payment_method: commissionData.payment_method || null,
        notes: commissionData.notes || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: createdCommission, error: createError } = await supabase
        .from('commission_transactions')
        .insert([newCommission])
        .select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          bookings!inner(
            id,
            start_time,
            customers(first_name, last_name),
            services(name)
          )
        `)
        .single();

      if (createError) {
        console.error('Commission creation error:', createError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to create commission transaction',
          requestId
        });
      }

      return res.status(201).json({
        commission: createdCommission,
        message: 'Commission transaction created successfully',
        requestId
      });
    }

    if (req.method === 'PUT') {
      const { commission_id } = req.query;
      const updateData = req.body;

      if (!commission_id) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Commission ID is required',
          requestId
        });
      }

      // Verify commission exists
      const { data: existingCommission, error: fetchError } = await supabase
        .from('commission_transactions')
        .select('*')
        .eq('id', commission_id)
        .single();

      if (fetchError || !existingCommission) {
        return res.status(404).json({
          error: 'Commission not found',
          message: 'The specified commission transaction does not exist',
          requestId
        });
      }

      // Handle payment status updates
      if (updateData.status === 'paid' && !updateData.paid_at) {
        updateData.paid_at = new Date().toISOString();
        // Note: paid_by should be set from the authenticated user
      }

      // Recalculate totals if amounts change
      if (updateData.service_amount !== undefined || updateData.commission_rate !== undefined || updateData.tip_amount !== undefined) {
        const serviceAmount = updateData.service_amount ?? existingCommission.service_amount;
        const commissionRate = updateData.commission_rate ?? existingCommission.commission_rate;
        const tipAmount = updateData.tip_amount ?? existingCommission.tip_amount;
        
        updateData.commission_amount = (serviceAmount * commissionRate) / 100;
        updateData.total_earnings = updateData.commission_amount + tipAmount;
      }

      const updatedData = {
        ...updateData,
        updated_at: new Date().toISOString()
      };

      const { data: updatedCommission, error: updateError } = await supabase
        .from('commission_transactions')
        .update(updatedData)
        .eq('id', commission_id)
        .select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          bookings!inner(
            id,
            start_time,
            customers(first_name, last_name),
            services(name)
          ),
          payer:admin_users!paid_by(
            id,
            first_name,
            last_name,
            email
          )
        `)
        .single();

      if (updateError) {
        console.error('Commission update error:', updateError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to update commission transaction',
          requestId
        });
      }

      return res.status(200).json({
        commission: updatedCommission,
        message: 'Commission transaction updated successfully',
        requestId
      });
    }

    if (req.method === 'DELETE') {
      const { commission_id } = req.query;

      if (!commission_id) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Commission ID is required',
          requestId
        });
      }

      // Verify commission exists
      const { data: existingCommission, error: fetchError } = await supabase
        .from('commission_transactions')
        .select('id, status, total_earnings')
        .eq('id', commission_id)
        .single();

      if (fetchError || !existingCommission) {
        return res.status(404).json({
          error: 'Commission not found',
          message: 'The specified commission transaction does not exist',
          requestId
        });
      }

      // Only allow deletion of pending/calculated commissions
      if (['paid', 'disputed'].includes(existingCommission.status)) {
        return res.status(400).json({
          error: 'Invalid operation',
          message: 'Cannot delete paid or disputed commission transactions',
          requestId
        });
      }

      const { error: deleteError } = await supabase
        .from('commission_transactions')
        .delete()
        .eq('id', commission_id);

      if (deleteError) {
        console.error('Commission deletion error:', deleteError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to delete commission transaction',
          requestId
        });
      }

      return res.status(200).json({
        message: 'Commission transaction deleted successfully',
        deletedCommission: existingCommission,
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      message: `HTTP method ${req.method} is not supported`,
      requestId
    });

  } catch (error) {
    console.error('Commission API error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      requestId
    });
  }
}
