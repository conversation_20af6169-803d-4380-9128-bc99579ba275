import { useState } from 'react'
import TipManagement from './TipManagement'
import styles from '@/styles/admin/POS.module.css'

/**
 * PaymentMethodSelector component for POS checkout
 * Provides options for different payment methods with pricing breakdown
 */
export default function PaymentMethodSelector({ amount, onMethodSelect, onCancel, customerName = 'Customer' }) {
  const [selectedMethod, setSelectedMethod] = useState(null)
  const [cashAmount, setCashAmount] = useState('')
  const [processing, setProcessing] = useState(false)
  const [showTipManagement, setShowTipManagement] = useState(false)
  const [pendingPaymentMethod, setPendingPaymentMethod] = useState(null)
  const [pendingMethodData, setPendingMethodData] = useState(null)

  // Calculate processing fees and totals
  const baseAmount = parseFloat(amount || 0)
  const cardProcessingFee = baseAmount * 0.029 + 0.30 // 2.9% + 30¢
  const terminalProcessingFee = baseAmount * 0.026 + 0.10 // 2.6% + 10¢
  
  const cardTotal = baseAmount + cardProcessingFee
  const terminalTotal = baseAmount + terminalProcessingFee
  const cashTotal = baseAmount // No processing fee for cash

  const handleMethodSelect = (method) => {
    setSelectedMethod(method)

    if (method === 'cash') {
      // For cash, show cash handling interface
      return
    }

    // For card and terminal payments, show tip management first
    const methodData = {
      originalAmount: baseAmount,
      processingFee: method === 'square_payment' ? cardProcessingFee :
                     method === 'square_terminal' ? terminalProcessingFee : 0,
      totalAmount: method === 'square_payment' ? cardTotal :
                   method === 'square_terminal' ? terminalTotal : cashTotal
    }

    setPendingPaymentMethod(method)
    setPendingMethodData(methodData)
    setShowTipManagement(true)
  }

  const handleCashPayment = () => {
    const cashReceived = parseFloat(cashAmount)

    if (!cashReceived || cashReceived < cashTotal) {
      alert(`Please enter at least $${cashTotal.toFixed(2)}`)
      return
    }

    const changeAmount = cashReceived - cashTotal

    // For cash payments, also show tip management
    const methodData = {
      originalAmount: baseAmount,
      processingFee: 0,
      totalAmount: cashTotal,
      cashReceived: cashReceived,
      changeAmount: changeAmount
    }

    setPendingPaymentMethod('cash')
    setPendingMethodData(methodData)
    setShowTipManagement(true)
  }

  const handleTipCalculated = (tipData) => {
    setShowTipManagement(false)
    setProcessing(true)

    // Combine payment method data with tip data
    const finalMethodData = {
      ...pendingMethodData,
      tipAmount: tipData.tipAmount,
      tipMethod: tipData.tipMethod,
      tipPercentage: tipData.tipPercentage,
      totalAmount: tipData.totalAmount
    }

    onMethodSelect(pendingPaymentMethod, finalMethodData)
  }

  const handleTipCancelled = () => {
    setShowTipManagement(false)
    setPendingPaymentMethod(null)
    setPendingMethodData(null)
    setSelectedMethod(null)
  }

  const formatCurrency = (amount) => {
    return `$${amount.toFixed(2)}`
  }

  if (processing) {
    return (
      <div className={styles.paymentMethodContainer}>
        <div className={styles.processingPayment}>
          <div className={styles.loadingSpinner}></div>
          <p>Processing payment...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className={styles.paymentMethodContainer}>
        <div className={styles.paymentHeader}>
          <h3>Select Payment Method</h3>
          <div className={styles.totalAmount}>
            Service Total: {formatCurrency(baseAmount)}
          </div>
        </div>

      <div className={styles.paymentMethods}>
        {/* Card Payment */}
        <div 
          className={`${styles.paymentMethodCard} ${selectedMethod === 'square_payment' ? styles.selected : ''}`}
          onClick={() => handleMethodSelect('square_payment')}
        >
          <div className={styles.methodIcon}>💳</div>
          <div className={styles.methodInfo}>
            <h4>Card Payment</h4>
            <p>Credit/Debit Card Entry</p>
            <div className={styles.methodPricing}>
              <div className={styles.priceBreakdown}>
                <span>Service: {formatCurrency(baseAmount)}</span>
                <span>Processing: {formatCurrency(cardProcessingFee)}</span>
                <strong>Total: {formatCurrency(cardTotal)}</strong>
              </div>
            </div>
          </div>
          <div className={styles.methodBadge}>
            <span>Online</span>
          </div>
        </div>

        {/* Terminal Payment */}
        <div 
          className={`${styles.paymentMethodCard} ${selectedMethod === 'square_terminal' ? styles.selected : ''}`}
          onClick={() => handleMethodSelect('square_terminal')}
        >
          <div className={styles.methodIcon}>📱</div>
          <div className={styles.methodInfo}>
            <h4>Square Terminal</h4>
            <p>Hardware Card Reader</p>
            <div className={styles.methodPricing}>
              <div className={styles.priceBreakdown}>
                <span>Service: {formatCurrency(baseAmount)}</span>
                <span>Processing: {formatCurrency(terminalProcessingFee)}</span>
                <strong>Total: {formatCurrency(terminalTotal)}</strong>
              </div>
            </div>
          </div>
          <div className={styles.methodBadge}>
            <span>Hardware</span>
          </div>
        </div>

        {/* Cash Payment */}
        <div 
          className={`${styles.paymentMethodCard} ${selectedMethod === 'cash' ? styles.selected : ''}`}
          onClick={() => setSelectedMethod('cash')}
        >
          <div className={styles.methodIcon}>💵</div>
          <div className={styles.methodInfo}>
            <h4>Cash Payment</h4>
            <p>Physical Currency</p>
            <div className={styles.methodPricing}>
              <div className={styles.priceBreakdown}>
                <span>Service: {formatCurrency(baseAmount)}</span>
                <span>Processing: {formatCurrency(0)}</span>
                <strong>Total: {formatCurrency(cashTotal)}</strong>
              </div>
            </div>
          </div>
          <div className={styles.methodBadge}>
            <span>No Fees</span>
          </div>
        </div>
      </div>

      {/* Cash Payment Interface */}
      {selectedMethod === 'cash' && (
        <div className={styles.cashPaymentInterface}>
          <h4>Cash Payment Details</h4>
          <div className={styles.cashInputs}>
            <div className={styles.inputGroup}>
              <label>Amount Due: {formatCurrency(cashTotal)}</label>
            </div>
            <div className={styles.inputGroup}>
              <label>Cash Received:</label>
              <input
                type="number"
                step="0.01"
                min={cashTotal}
                value={cashAmount}
                onChange={(e) => setCashAmount(e.target.value)}
                placeholder={cashTotal.toFixed(2)}
                className={styles.cashInput}
              />
            </div>
            {cashAmount && parseFloat(cashAmount) >= cashTotal && (
              <div className={styles.changeAmount}>
                <strong>Change: {formatCurrency(parseFloat(cashAmount) - cashTotal)}</strong>
              </div>
            )}
          </div>
          <div className={styles.cashActions}>
            <button
              onClick={handleCashPayment}
              disabled={!cashAmount || parseFloat(cashAmount) < cashTotal}
              className={styles.processCashButton}
            >
              Process Cash Payment
            </button>
          </div>
        </div>
      )}

      <div className={styles.paymentActions}>
        <button onClick={onCancel} className={styles.cancelButton}>
          Cancel
        </button>
      </div>

        <div className={styles.paymentNote}>
          <p>💡 Processing fees are automatically calculated and included in the total</p>
        </div>
      </div>

      {/* Tip Management Modal */}
      {showTipManagement && (
        <TipManagement
          baseAmount={pendingMethodData?.totalAmount || baseAmount}
          paymentMethod={pendingPaymentMethod}
          customerName={customerName}
          onTipCalculated={handleTipCalculated}
          onCancel={handleTipCancelled}
        />
      )}
    </>
  )
}
