/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Input Component
 * Touch-friendly input component optimized for mobile devices
 */

import React, { useState, useRef, useEffect } from 'react';
import { ValidationError } from '@/types';
import styles from './MobileInput.module.css';

export interface MobileInputProps {
  id?: string;
  name: string;
  type?: 'text' | 'email' | 'tel' | 'password' | 'number' | 'url' | 'search';
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  autoComplete?: string;
  autoFocus?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  error?: ValidationError | string;
  success?: boolean;
  icon?: React.ReactNode;
  suffix?: React.ReactNode;
  helpText?: string;
  className?: string;
  inputMode?: 'text' | 'decimal' | 'numeric' | 'tel' | 'search' | 'email' | 'url';
  enterKeyHint?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send';
  'data-testid'?: string;
}

export const MobileInput: React.FC<MobileInputProps> = ({
  id,
  name,
  type = 'text',
  value,
  onChange,
  onBlur,
  onFocus,
  placeholder,
  label,
  required = false,
  disabled = false,
  readOnly = false,
  autoComplete,
  autoFocus = false,
  maxLength,
  minLength,
  pattern,
  error,
  success = false,
  icon,
  suffix,
  helpText,
  className = '',
  inputMode,
  enterKeyHint = 'done',
  'data-testid': testId,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!!value);
  const inputRef = useRef<HTMLInputElement>(null);

  const inputId = id || `mobile-input-${name}`;
  const hasError = !!error;
  const errorMessage = typeof error === 'string' ? error : error?.message;

  useEffect(() => {
    setHasValue(!!value);
  }, [value]);

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setHasValue(!!newValue);
    onChange(newValue);
  };

  const handleLabelClick = () => {
    if (!disabled && !readOnly) {
      inputRef.current?.focus();
    }
  };

  const containerClasses = [
    styles.container,
    isFocused && styles.focused,
    hasError && styles.error,
    success && styles.success,
    disabled && styles.disabled,
    readOnly && styles.readOnly,
    hasValue && styles.hasValue,
    className
  ].filter(Boolean).join(' ');

  const inputClasses = [
    styles.input,
    icon && styles.withIcon,
    suffix && styles.withSuffix
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses} data-testid={testId}>
      {label && (
        <label 
          htmlFor={inputId}
          className={styles.label}
          onClick={handleLabelClick}
        >
          {label}
          {required && <span className={styles.required} aria-label="required">*</span>}
        </label>
      )}
      
      <div className={styles.inputWrapper}>
        {icon && (
          <div className={styles.icon} aria-hidden="true">
            {icon}
          </div>
        )}
        
        <input
          ref={inputRef}
          id={inputId}
          name={name}
          type={type}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          readOnly={readOnly}
          autoComplete={autoComplete}
          autoFocus={autoFocus}
          maxLength={maxLength}
          minLength={minLength}
          pattern={pattern}
          inputMode={inputMode}
          enterKeyHint={enterKeyHint}
          className={inputClasses}
          aria-invalid={hasError}
          aria-describedby={
            [
              hasError && `${inputId}-error`,
              helpText && `${inputId}-help`
            ].filter(Boolean).join(' ') || undefined
          }
        />
        
        {suffix && (
          <div className={styles.suffix} aria-hidden="true">
            {suffix}
          </div>
        )}
      </div>
      
      {hasError && (
        <div 
          id={`${inputId}-error`}
          className={styles.errorMessage}
          role="alert"
          aria-live="polite"
        >
          {errorMessage}
        </div>
      )}
      
      {helpText && !hasError && (
        <div 
          id={`${inputId}-help`}
          className={styles.helpText}
        >
          {helpText}
        </div>
      )}
    </div>
  );
};

// Specialized mobile input variants
export const MobileEmailInput: React.FC<Omit<MobileInputProps, 'type' | 'inputMode'>> = (props) => (
  <MobileInput {...props} type="email" inputMode="email" />
);

export const MobilePhoneInput: React.FC<Omit<MobileInputProps, 'type' | 'inputMode'>> = (props) => (
  <MobileInput {...props} type="tel" inputMode="tel" />
);

export const MobileNumberInput: React.FC<Omit<MobileInputProps, 'type' | 'inputMode'>> = (props) => (
  <MobileInput {...props} type="number" inputMode="numeric" />
);

export const MobileSearchInput: React.FC<Omit<MobileInputProps, 'type' | 'inputMode' | 'enterKeyHint'>> = (props) => (
  <MobileInput {...props} type="search" inputMode="search" enterKeyHint="search" />
);

export const MobilePasswordInput: React.FC<Omit<MobileInputProps, 'type'>> = (props) => {
  const [showPassword, setShowPassword] = useState(false);
  
  return (
    <MobileInput
      {...props}
      type={showPassword ? 'text' : 'password'}
      suffix={
        <button
          type="button"
          className={styles.passwordToggle}
          onClick={() => setShowPassword(!showPassword)}
          aria-label={showPassword ? 'Hide password' : 'Show password'}
        >
          {showPassword ? '👁️' : '👁️‍🗨️'}
        </button>
      }
    />
  );
};

export default MobileInput;
