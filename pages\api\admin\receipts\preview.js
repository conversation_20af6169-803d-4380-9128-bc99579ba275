import { authenticateAdminRequest } from '@/lib/auth/admin-auth'
import { generateReceipt } from '@/lib/receipt-generator'

/**
 * API endpoint for generating receipt previews
 * Used by the receipt customizer to show live previews
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate admin user
    const token = req.headers.authorization?.replace('Bearer ', '') ||
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await authenticateAdminRequest(token)
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' })
    }

    const { templateId, bookingData } = req.body
    const requestId = Math.random().toString(36).substring(2, 8)
    
    console.log(`[${requestId}] Receipt preview requested for template: ${templateId}`)

    if (!bookingData) {
      return res.status(400).json({ error: 'Booking data is required' })
    }

    // Generate receipt HTML
    const result = await generateReceipt(bookingData, templateId)
    
    if (!result.success) {
      console.error(`[${requestId}] Error generating receipt preview:`, result.error)
      return res.status(500).json({ error: result.error })
    }

    console.log(`[${requestId}] Receipt preview generated successfully`)
    return res.status(200).json({
      html: result.html,
      template: result.template
    })
  } catch (error) {
    console.error('Receipt preview API error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
