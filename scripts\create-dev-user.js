#!/usr/bin/env node

/**
 * Create Development Admin User Script
 * Creates a test admin user for development environment
 */

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: require('path').join(__dirname, '..', '.env.local') });

async function createDevUser() {
  console.log('🔧 Creating development admin user...\n');

  // Check if we're in development
  if (process.env.NODE_ENV === 'production') {
    console.error('❌ This script is only for development environment');
    process.exit(1);
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase configuration');
    console.error('Please check your .env.local file');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // Create development admin user
    const devUser = {
      email: '<EMAIL>',
      password_hash: await bcrypt.hash('DevPassword123!', 12),
      first_name: 'Development',
      last_name: 'Admin',
      role: 'DEV',
      is_active: true,
      mfa_enabled: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('admin_users')
      .select('id, email')
      .eq('email', devUser.email)
      .single();

    if (existingUser) {
      console.log('✅ Development user already exists');
      console.log(`Email: ${devUser.email}`);
      console.log(`Password: DevPassword123!`);
      console.log(`Role: DEV`);
      return;
    }

    // Insert new user
    const { data, error } = await supabase
      .from('admin_users')
      .insert(devUser)
      .select()
      .single();

    if (error) {
      throw error;
    }

    console.log('✅ Development admin user created successfully!');
    console.log('\n📋 Login Credentials:');
    console.log(`Email: ${devUser.email}`);
    console.log(`Password: DevPassword123!`);
    console.log(`Role: DEV`);
    console.log('\n🌐 Access URLs:');
    console.log(`Admin Dashboard: http://localhost:3002/admin/dashboard`);
    console.log(`Login Page: http://localhost:3002/admin/login`);
    console.log('\n⚠️  This user is for development only!');

  } catch (error) {
    console.error('❌ Failed to create development user:', error.message);
    
    if (error.code === '42P01') {
      console.error('\n💡 The admin_users table does not exist.');
      console.error('Please run the database setup script first.');
    }
    
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  createDevUser();
}

module.exports = { createDevUser };
