import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') ||
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    if (req.method === 'GET') {
      // Get artists from artist_profiles table
      const { data: artists, error } = await supabase
        .from('artist_profiles')
        .select(`
          id,
          artist_name,
          display_name,
          bio,
          profile_image_url,
          specializations,
          skill_level,
          hourly_rate,
          commission_rate,
          is_active,
          is_available_today,
          availability_notes,
          portfolio_urls,
          social_media_links,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('artist_name', { ascending: true });

      if (error && error.message.includes('relation "artist_profiles" does not exist')) {
        // If no artist_profiles table, return structured mock data
        const mockArtists = [
          {
            id: 1,
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            phone: '0412 345 678',
            specializations: ['Box Braids', 'Cornrows', 'Twists'],
            status: 'active',
            availability: {
              monday: { start: '09:00', end: '17:00', available: true },
              tuesday: { start: '09:00', end: '17:00', available: true },
              wednesday: { start: '09:00', end: '17:00', available: true },
              thursday: { start: '09:00', end: '17:00', available: true },
              friday: { start: '09:00', end: '17:00', available: true },
              saturday: { start: '10:00', end: '16:00', available: true },
              sunday: { start: '10:00', end: '16:00', available: false }
            },
            rating: 4.8,
            total_bookings: 127,
            total_revenue: 15240,
            created_at: '2024-01-15T00:00:00Z'
          },
          {
            id: 2,
            name: 'Maya Patel',
            email: '<EMAIL>',
            phone: '0423 456 789',
            specializations: ['Locs', 'Faux Locs', 'Goddess Braids'],
            status: 'active',
            availability: {
              monday: { start: '10:00', end: '18:00', available: true },
              tuesday: { start: '10:00', end: '18:00', available: true },
              wednesday: { start: '10:00', end: '18:00', available: false },
              thursday: { start: '10:00', end: '18:00', available: true },
              friday: { start: '10:00', end: '18:00', available: true },
              saturday: { start: '09:00', end: '17:00', available: true },
              sunday: { start: '11:00', end: '15:00', available: true }
            },
            rating: 4.9,
            total_bookings: 98,
            total_revenue: 12800,
            created_at: '2024-02-01T00:00:00Z'
          },
          {
            id: 3,
            name: 'Keisha Williams',
            email: '<EMAIL>',
            phone: '0434 567 890',
            specializations: ['Senegalese Twists', 'Marley Twists', 'Passion Twists'],
            status: 'active',
            availability: {
              monday: { start: '08:00', end: '16:00', available: true },
              tuesday: { start: '08:00', end: '16:00', available: true },
              wednesday: { start: '08:00', end: '16:00', available: true },
              thursday: { start: '08:00', end: '16:00', available: true },
              friday: { start: '08:00', end: '16:00', available: true },
              saturday: { start: '09:00', end: '15:00', available: true },
              sunday: { start: '09:00', end: '15:00', available: false }
            },
            rating: 4.7,
            total_bookings: 145,
            total_revenue: 18600,
            created_at: '2024-01-20T00:00:00Z'
          }
        ];
        
        return res.status(200).json({ artists: mockArtists, source: 'mock' });
      }

      if (error) {
        throw error;
      }

      // Transform data for consistency with frontend expectations
      const transformedArtists = (artists || []).map(artist => ({
        id: artist.id,
        name: artist.artist_name || artist.display_name,
        artist_name: artist.artist_name,
        display_name: artist.display_name,
        email: artist.social_media_links?.email || '', // Extract from social_media_links if available
        phone: artist.social_media_links?.phone || '', // Extract from social_media_links if available
        specializations: artist.specializations || [],
        bio: artist.bio,
        profile_image_url: artist.profile_image_url,
        skill_level: artist.skill_level,
        portfolio_urls: artist.portfolio_urls || [],
        social_media_links: artist.social_media_links || {},
        is_active: artist.is_active,
        is_available_today: artist.is_available_today,
        availability_notes: artist.availability_notes,
        hourly_rate: artist.hourly_rate,
        commission_rate: artist.commission_rate,
        created_at: artist.created_at,
        updated_at: artist.updated_at
      }));

      return res.status(200).json({
        artists: transformedArtists,
        total: transformedArtists.length,
        source: 'database'
      });
    }

    if (req.method === 'POST') {
      const { name, email, phone, specializations, availability } = req.body;

      const { data, error } = await supabase
        .from('artist_profiles')
        .insert([
          {
            artist_name: name,
            name,
            email,
            phone,
            specializations,
            availability_schedule: availability,
            is_active: true,
            created_at: new Date().toISOString()
          }
        ])
        .select();

      if (error) {
        throw error;
      }

      return res.status(201).json({ artist: data[0] });
    }

    return res.status(405).json({ error: 'Method not allowed' });

  } catch (error) {
    console.error('Artists API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
