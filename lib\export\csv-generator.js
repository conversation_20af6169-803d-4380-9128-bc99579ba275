/**
 * Generate CSV report from report data
 */
export async function generateCSVReport(reportData, dateRange, rangeName) {
  let csvContent = '';

  // Add header
  csvContent += `Ocean Soul Sparkles - Business Report\n`;
  csvContent += `Report Period: ${formatDateRange(rangeName)}\n`;
  csvContent += `Generated: ${new Date().toLocaleDateString('en-AU')}\n\n`;

  // Add overview section
  csvContent += addOverviewSection(reportData.overview);
  
  // Add revenue section
  csvContent += addRevenueSection(reportData.revenue);
  
  // Add bookings section
  csvContent += addBookingsSection(reportData.bookings);
  
  // Add customers section
  csvContent += addCustomersSection(reportData.customers);

  return Buffer.from(csvContent, 'utf8');
}

/**
 * Add overview section to CSV
 */
function addOverviewSection(overview) {
  let content = 'EXECUTIVE SUMMARY\n';
  content += 'Metric,Value,Growth\n';
  content += `Total Revenue,${formatCurrency(overview.totalRevenue)},${overview.revenueGrowth >= 0 ? '+' : ''}${overview.revenueGrowth.toFixed(1)}%\n`;
  content += `Total Bookings,${overview.totalBookings},${overview.bookingGrowth >= 0 ? '+' : ''}${overview.bookingGrowth.toFixed(1)}%\n`;
  content += `Total Customers,${overview.totalCustomers},-\n`;
  content += `Average Booking Value,${formatCurrency(overview.averageBookingValue)},-\n\n`;
  
  return content;
}

/**
 * Add revenue section to CSV
 */
function addRevenueSection(revenue) {
  let content = 'REVENUE ANALYSIS\n\n';
  
  // Revenue by service
  content += 'Revenue by Service\n';
  content += 'Service,Revenue,Percentage\n';
  revenue.byService.forEach(service => {
    content += `"${service.service}",${service.amount.toFixed(2)},${service.percentage.toFixed(1)}%\n`;
  });
  content += '\n';
  
  // Daily revenue
  content += 'Daily Revenue\n';
  content += 'Date,Amount\n';
  revenue.daily.forEach(day => {
    const date = new Date(day.date).toLocaleDateString('en-AU');
    content += `${date},${day.amount.toFixed(2)}\n`;
  });
  content += '\n';
  
  return content;
}

/**
 * Add bookings section to CSV
 */
function addBookingsSection(bookings) {
  let content = 'BOOKING ANALYSIS\n\n';
  
  // Status breakdown
  content += 'Booking Status Breakdown\n';
  content += 'Status,Count,Percentage\n';
  bookings.statusBreakdown.forEach(status => {
    content += `${status.status},${status.count},${status.percentage.toFixed(1)}%\n`;
  });
  content += '\n';
  
  // Summary statistics
  content += 'Summary Statistics\n';
  content += 'Metric,Value\n';
  const totalBookings = bookings.statusBreakdown.reduce((sum, status) => sum + status.count, 0);
  content += `Total Bookings,${totalBookings}\n`;
  content += `Cancellation Rate,${bookings.cancellationRate.toFixed(1)}%\n`;
  content += `Success Rate,${(100 - bookings.cancellationRate).toFixed(1)}%\n\n`;
  
  return content;
}

/**
 * Add customers section to CSV
 */
function addCustomersSection(customers) {
  let content = 'CUSTOMER ANALYSIS\n\n';
  
  // Customer metrics
  content += 'Customer Metrics\n';
  content += 'Metric,Value\n';
  content += `New Customers,${customers.newCustomers}\n`;
  content += `Returning Customers,${customers.returningCustomers}\n`;
  content += `Customer Lifetime Value,${formatCurrency(customers.customerLifetimeValue)}\n`;
  
  const totalCustomers = customers.newCustomers + customers.returningCustomers;
  const retentionRate = totalCustomers > 0 ? (customers.returningCustomers / totalCustomers) * 100 : 0;
  content += `Retention Rate,${retentionRate.toFixed(1)}%\n\n`;
  
  // Customer distribution
  content += 'Customer Distribution\n';
  content += 'Type,Count,Percentage\n';
  const newPercentage = totalCustomers > 0 ? (customers.newCustomers / totalCustomers) * 100 : 0;
  const returningPercentage = totalCustomers > 0 ? (customers.returningCustomers / totalCustomers) * 100 : 0;
  content += `New Customers,${customers.newCustomers},${newPercentage.toFixed(1)}%\n`;
  content += `Returning Customers,${customers.returningCustomers},${returningPercentage.toFixed(1)}%\n\n`;
  
  return content;
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD'
  }).format(amount);
}

/**
 * Format date range for display
 */
function formatDateRange(rangeName) {
  const ranges = {
    'last7days': 'Last 7 Days',
    'last30days': 'Last 30 Days',
    'last90days': 'Last 90 Days',
    'thisyear': 'This Year'
  };
  
  return ranges[rangeName] || rangeName;
}

/**
 * Escape CSV field if it contains special characters
 */
function escapeCsvField(field) {
  if (typeof field !== 'string') {
    return field;
  }
  
  // If field contains comma, quote, or newline, wrap in quotes and escape quotes
  if (field.includes(',') || field.includes('"') || field.includes('\n')) {
    return `"${field.replace(/"/g, '""')}"`;
  }
  
  return field;
}
