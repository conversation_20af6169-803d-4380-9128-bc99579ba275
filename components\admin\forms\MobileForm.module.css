/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Form Styles
 * Complete mobile-optimized form styling
 */

.container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header {
  margin-bottom: 2rem;
  text-align: center;
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.progressContainer {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.progressLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #475569;
  margin-bottom: 0.75rem;
  text-align: center;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.autoSaveStatus {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.autoSaveSaving {
  color: #3b82f6;
  animation: pulse 1.5s infinite;
}

.autoSaveSaved {
  color: #10b981;
}

.autoSaveError {
  color: #ef4444;
}

.form {
  width: 100%;
}

.fields {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.field {
  width: 100%;
}

.section {
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
}

.sectionHeader {
  padding: 1rem 1.25rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  -webkit-tap-highlight-color: transparent;
}

.sectionHeader:hover {
  background: #f3f4f6;
}

.sectionTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  flex: 1;
}

.sectionSubtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0 0 0;
}

.sectionToggle {
  font-size: 0.875rem;
  color: #6b7280;
  margin-left: 1rem;
  transition: transform 0.2s ease;
}

.sectionContent {
  padding: 1.25rem;
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  align-items: center;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.cancelButton,
.resetButton,
.submitButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 12px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
  min-width: 100px;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.cancelButton {
  background: #ffffff;
  color: #6b7280;
  border-color: #d1d5db;
}

.cancelButton:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

.cancelButton:active {
  background: #f3f4f6;
}

.resetButton {
  background: #ffffff;
  color: #ef4444;
  border-color: #fecaca;
}

.resetButton:hover {
  background: #fef2f2;
  border-color: #f87171;
}

.resetButton:active {
  background: #fee2e2;
}

.submitButton {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

.submitButton:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.submitButton:active {
  background: #1d4ed8;
}

.submitButton:disabled {
  background: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading {
  opacity: 0.7;
  pointer-events: none;
}

.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .container {
    padding: 1.25rem;
    border-radius: 20px;
    margin: 0.5rem;
  }
  
  .header {
    margin-bottom: 2.5rem;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .subtitle {
    font-size: 1.125rem;
  }
  
  .progressContainer {
    padding: 1.25rem;
    margin-bottom: 2.5rem;
  }
  
  .progressLabel {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
  
  .progressBar {
    height: 10px;
  }
  
  .fields {
    gap: 2rem;
    margin-bottom: 2.5rem;
  }
  
  .section {
    margin-bottom: 2.5rem;
    border-radius: 16px;
  }
  
  .sectionHeader {
    padding: 1.25rem 1.5rem;
  }
  
  .sectionTitle {
    font-size: 1.25rem;
  }
  
  .sectionSubtitle {
    font-size: 1rem;
  }
  
  .sectionContent {
    padding: 1.5rem;
  }
  
  .actions {
    flex-direction: column;
    gap: 1.25rem;
    padding-top: 2rem;
  }
  
  .cancelButton,
  .resetButton,
  .submitButton {
    width: 100%;
    padding: 1.125rem 1.5rem;
    font-size: 1.125rem;
    min-height: 48px;
    border-radius: 16px;
  }
  
  .autoSaveStatus {
    font-size: 1rem;
    margin-bottom: 1.25rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .container {
    padding: 1.5rem;
    margin: 0.25rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .cancelButton,
  .resetButton,
  .submitButton {
    padding: 1.25rem 1.5rem;
    min-height: 52px;
  }
  
  .actions {
    gap: 1.5rem;
  }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
  .actions {
    flex-direction: row;
    justify-content: space-between;
  }
  
  .cancelButton,
  .resetButton,
  .submitButton {
    width: auto;
    flex: 1;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .container {
    border: 2px solid #000000;
  }
  
  .submitButton {
    background: #000000;
    border-color: #000000;
  }
  
  .cancelButton,
  .resetButton {
    border-width: 3px;
  }
  
  .progressFill {
    background: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .progressFill,
  .cancelButton,
  .resetButton,
  .submitButton,
  .sectionToggle {
    transition: none;
  }
  
  .spinner {
    animation: none;
  }
  
  .autoSaveSaving {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .container {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .title {
    color: #f9fafb;
  }
  
  .subtitle {
    color: #d1d5db;
  }
  
  .progressContainer {
    background: #374151;
    border-color: #4b5563;
  }
  
  .progressLabel {
    color: #d1d5db;
  }
  
  .progressBar {
    background: #4b5563;
  }
  
  .section {
    border-color: #4b5563;
  }
  
  .sectionHeader {
    background: #374151;
    border-color: #4b5563;
  }
  
  .sectionHeader:hover {
    background: #4b5563;
  }
  
  .sectionTitle {
    color: #f9fafb;
  }
  
  .sectionSubtitle {
    color: #d1d5db;
  }
  
  .actions {
    border-color: #4b5563;
  }
  
  .cancelButton {
    background: #374151;
    color: #d1d5db;
    border-color: #4b5563;
  }
  
  .cancelButton:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
  
  .resetButton {
    background: #374151;
    border-color: #ef4444;
  }
  
  .resetButton:hover {
    background: #1f2937;
  }
}
