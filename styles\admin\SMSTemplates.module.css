/* SMS Templates Management Styles */

.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.1rem;
  color: #666;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e1e5e9;
}

.title {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.testBtn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.testBtn:hover {
  background: #138496;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

.createBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.createBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  height: calc(100vh - 200px);
}

/* Template List */
.templateList {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.categoryFilter {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.categoryBtn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background: white;
  border-radius: 20px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.categoryBtn:hover {
  background: #f0f0f0;
}

.categoryBtn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.templates {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.templateItem {
  padding: 1rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.templateItem:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.templateItem.selected {
  border-color: #667eea;
  background: #f8f9ff;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.templateHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.templateHeader h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #2c3e50;
}

.category {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.category.booking { background: #e3f2fd; color: #1976d2; }
.category.payment { background: #e8f5e8; color: #388e3c; }
.category.staff { background: #fff3e0; color: #f57c00; }
.category.customer { background: #fce4ec; color: #c2185b; }
.category.marketing { background: #f3e5f5; color: #7b1fa2; }
.category.admin { background: #ffebee; color: #d32f2f; }
.category.custom { background: #f5f5f5; color: #616161; }

.templateDescription {
  margin: 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.templatePreview {
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #555;
  margin: 0.5rem 0;
  font-family: monospace;
}

.templateMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
}

.status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status.active {
  background: #e8f5e8;
  color: #388e3c;
}

.status.inactive {
  background: #ffebee;
  color: #d32f2f;
}

.defaultBadge {
  background: #fff3e0;
  color: #f57c00;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Template Editor */
.templateEditor {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.editorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.editorHeader h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.editorActions {
  display: flex;
  gap: 0.5rem;
}

.editBtn, .deleteBtn, .cancelBtn, .saveBtn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editBtn {
  background: #667eea;
  color: white;
}

.editBtn:hover {
  background: #5a6fd8;
}

.deleteBtn {
  background: #dc3545;
  color: white;
}

.deleteBtn:hover {
  background: #c82333;
}

.cancelBtn {
  background: #6c757d;
  color: white;
}

.cancelBtn:hover {
  background: #5a6268;
}

.saveBtn {
  background: #28a745;
  color: white;
}

.saveBtn:hover {
  background: #218838;
}

.editBtn:disabled, .deleteBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.noSelection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
  font-size: 1.1rem;
}

/* Edit Form */
.editForm {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.formGroup small {
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-size: 0.875rem;
}

.formGroup input[type="checkbox"] {
  width: auto;
  margin-right: 0.5rem;
}

/* Template View */
.templateView {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
}

.templateDetails {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.templateDetails p {
  margin: 0.5rem 0;
  color: #555;
}

.templateContent {
  margin-bottom: 1.5rem;
}

.templateContent h4 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.templateText {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  font-family: monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Variables */
.variablesList {
  margin-bottom: 1.5rem;
}

.variablesList h4 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.variables {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.variable {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  font-family: monospace;
}

/* Preview */
.preview {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.preview h4 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.previewContent {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #ddd;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
  min-height: 60px;
}

.previewControls h5 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.previewInput {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.previewInput label {
  min-width: 100px;
  font-size: 0.875rem;
  font-weight: 500;
}

.previewInput input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .templateList {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .categoryFilter {
    justify-content: center;
  }
  
  .editorHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .editorActions {
    justify-content: center;
  }
}
