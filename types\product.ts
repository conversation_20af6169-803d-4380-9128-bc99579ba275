/**
 * Ocean Soul Sparkles Admin Dashboard - Product Types
 * Product and inventory management types
 */

import { ID, Timestamp, Status, Money, MediaFile, Category, Tag, SEOMetadata } from './common';

export interface Product {
  id: ID;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  sku: string;
  barcode?: string;
  
  // Categorization
  categoryId?: ID;
  category?: Category;
  tags?: Tag[];
  
  // Pricing
  price: Money;
  salePrice?: Money;
  costPrice?: Money;
  
  // Inventory
  stock: number;
  lowStockThreshold: number;
  reorderPoint: number;
  reorderQuantity: number;
  maxStockLevel?: number;
  
  // Physical properties
  weight?: number; // grams
  dimensions?: {
    length: number; // cm
    width: number; // cm
    height: number; // cm
  };
  
  // Media
  featuredImage?: MediaFile;
  gallery?: MediaFile[];
  
  // Status and visibility
  status: Status;
  isActive: boolean;
  isFeatured: boolean;
  
  // SEO
  seoMetadata?: SEOMetadata;
  
  // Inventory management
  abcClassification?: 'A' | 'B' | 'C';
  seasonalItem: boolean;
  perishable: boolean;
  shelfLifeDays?: number;
  leadTimeDays: number;
  minimumOrderQuantity: number;
  
  // System fields
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy?: ID;
  updatedBy?: ID;
}

export interface CreateProductData {
  name: string;
  description: string;
  sku: string;
  price: Money;
  stock: number;
  lowStockThreshold: number;
  categoryId?: ID;
  tags?: string[];
  isActive?: boolean;
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: ID;
}

export interface ProductSearchParams {
  query?: string;
  categoryId?: ID[];
  status?: Status[];
  isActive?: boolean;
  isFeatured?: boolean;
  priceMin?: number;
  priceMax?: number;
  stockMin?: number;
  stockMax?: number;
  lowStock?: boolean;
  outOfStock?: boolean;
}
