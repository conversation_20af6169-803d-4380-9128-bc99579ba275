/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Dashboard Grid Styles
 * Responsive grid layout styling for mobile dashboard
 */

.container {
  position: relative;
  width: 100%;
  padding: 1rem;
}

.loading {
  pointer-events: none;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(2px);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.grid {
  display: grid;
  width: 100%;
  align-items: start;
}

.widget {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.widget:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.customizable .widget {
  cursor: grab;
}

.customizable .widget:active {
  cursor: grabbing;
}

.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  z-index: 1000;
}

.widgetHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.5rem 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.widgetTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.widgetActions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.actionButton:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #374151;
}

.actionButton:active {
  background: #e5e7eb;
  transform: scale(0.95);
}

.dragHandle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  color: #9ca3af;
  cursor: grab;
  font-size: 1.25rem;
  line-height: 1;
  user-select: none;
  -webkit-user-select: none;
  transition: color 0.2s ease;
}

.dragHandle:hover {
  color: #6b7280;
}

.dragHandle:active {
  cursor: grabbing;
}

.widgetContent {
  padding: 1.5rem;
  background: #ffffff;
  min-height: 200px;
}

.widgetLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
}

.widgetError {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #ef4444;
  text-align: center;
}

.errorIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* Widget sizes */
.size-small .widgetContent {
  min-height: 150px;
}

.size-medium .widgetContent {
  min-height: 200px;
}

.size-large .widgetContent {
  min-height: 300px;
}

.size-full .widgetContent {
  min-height: 400px;
}

/* Standalone widget */
.standaloneWidget {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.standaloneWidget .widgetHeader {
  padding: 1.25rem 1.5rem 1rem;
}

.standaloneWidget .widgetContent {
  padding: 1.5rem;
}

/* Empty state */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #6b7280;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.emptyMessage {
  font-size: 1rem;
  margin: 0;
  max-width: 400px;
  line-height: 1.5;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .container {
    padding: 0.75rem;
  }
  
  .grid {
    gap: 1rem !important;
  }
  
  .widget {
    border-radius: 20px;
  }
  
  .widgetHeader {
    padding: 1rem 1.25rem 0.75rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .widgetTitle {
    font-size: 1.25rem;
    text-align: center;
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
  }
  
  .widgetActions {
    justify-content: center;
    margin-left: 0;
    gap: 1rem;
  }
  
  .actionButton {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    border-radius: 10px;
  }
  
  .dragHandle {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }
  
  .widgetContent {
    padding: 1.25rem;
  }
  
  .size-small .widgetContent {
    min-height: 180px;
  }
  
  .size-medium .widgetContent {
    min-height: 250px;
  }
  
  .size-large .widgetContent {
    min-height: 350px;
  }
  
  .size-full .widgetContent {
    min-height: 450px;
  }
  
  .standaloneWidget {
    border-radius: 20px;
    margin-bottom: 2rem;
  }
  
  .standaloneWidget .widgetHeader {
    padding: 1rem 1.25rem 0.75rem;
  }
  
  .standaloneWidget .widgetContent {
    padding: 1.25rem;
  }
  
  .emptyState {
    padding: 3rem 1.5rem;
  }
  
  .emptyIcon {
    font-size: 5rem;
  }
  
  .emptyTitle {
    font-size: 1.75rem;
  }
  
  .emptyMessage {
    font-size: 1.125rem;
  }
  
  .spinner {
    width: 48px;
    height: 48px;
    border-width: 4px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }
  
  .widgetHeader {
    padding: 0.75rem 1rem 0.5rem;
  }
  
  .widgetContent {
    padding: 1rem;
  }
  
  .standaloneWidget .widgetHeader {
    padding: 0.75rem 1rem 0.5rem;
  }
  
  .standaloneWidget .widgetContent {
    padding: 1rem;
  }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
  .widgetHeader {
    flex-direction: row;
    align-items: center;
  }
  
  .widgetTitle {
    text-align: left;
  }
  
  .widgetActions {
    margin-left: 1rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .widget,
  .standaloneWidget {
    border: 2px solid #000000;
  }
  
  .actionButton {
    border-color: #000000;
    border-width: 2px;
  }
  
  .actionButton:hover {
    background: #000000;
    color: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .widget,
  .actionButton {
    transition: none;
  }
  
  .widget:hover {
    transform: none;
  }
  
  .spinner {
    animation: none;
  }
  
  .actionButton:active {
    transform: none;
  }
  
  .dragging {
    transform: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .widget,
  .standaloneWidget {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .widgetHeader {
    background: #374151;
    border-color: #4b5563;
  }
  
  .widgetTitle {
    color: #f9fafb;
  }
  
  .actionButton {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }
  
  .actionButton:hover {
    background: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
  }
  
  .dragHandle {
    color: #6b7280;
  }
  
  .dragHandle:hover {
    color: #d1d5db;
  }
  
  .widgetContent {
    background: #1f2937;
  }
  
  .widgetLoading {
    color: #d1d5db;
  }
  
  .widgetError {
    color: #fca5a5;
  }
  
  .emptyTitle {
    color: #e5e7eb;
  }
  
  .emptyState {
    color: #9ca3af;
  }
  
  .loadingOverlay {
    background: rgba(31, 41, 55, 0.8);
    color: #f9fafb;
  }
  
  .spinner {
    border-color: #4b5563;
    border-top-color: #3b82f6;
  }
}

/* Print styles */
@media print {
  .widget,
  .standaloneWidget {
    background: #ffffff !important;
    box-shadow: none !important;
    border: 1px solid #000000 !important;
    break-inside: avoid;
    margin-bottom: 1rem !important;
  }
  
  .widgetActions {
    display: none !important;
  }
  
  .loadingOverlay {
    display: none !important;
  }
  
  .emptyState {
    display: none !important;
  }
}
