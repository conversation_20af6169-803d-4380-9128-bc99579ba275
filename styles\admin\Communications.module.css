/* Customer Communications Page Styles */

.communicationsContainer {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.headerLeft {
  flex: 1;
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.headerStats {
  display: flex;
  gap: 16px;
}

.statCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  min-width: 120px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.statNumber {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.statLabel {
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
}

.errorMessage {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.closeError {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filters {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-bottom: 32px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filterGroup label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
}

.communicationsTable {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tableContainer {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: #f9fafb;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-bottom: 1px solid #e5e7eb;
}

.tableRow {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.tableRow:hover {
  background: #f9fafb;
}

.table td {
  padding: 16px;
  vertical-align: top;
  font-size: 14px;
}

.typeCell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typeIcon {
  font-size: 16px;
}

.typeText {
  font-weight: 500;
  color: #374151;
}

.customerCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customerName {
  font-weight: 500;
  color: #1f2937;
}

.bookingInfo {
  font-size: 12px;
  color: #6b7280;
}

.recipientCell {
  color: #374151;
  font-family: monospace;
  font-size: 13px;
}

.subjectCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: 250px;
}

.subject {
  font-weight: 500;
  color: #1f2937;
  word-break: break-word;
}

.templateInfo {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.statusBadge {
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
}

.errorInfo {
  margin-top: 4px;
  font-size: 12px;
  color: #dc2626;
  cursor: help;
}

.dateCell {
  color: #6b7280;
  font-size: 13px;
  white-space: nowrap;
}

.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.emptyState h3 {
  font-size: 20px;
  color: #374151;
  margin: 0 0 8px 0;
}

.emptyState p {
  margin: 0;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 32px;
  padding: 20px;
}

.paginationBtn {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.paginationBtn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.paginationBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.paginationInfo {
  font-size: 14px;
  color: #6b7280;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .headerStats {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .communicationsContainer {
    padding: 16px;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .table th,
  .table td {
    padding: 12px 8px;
    font-size: 13px;
  }

  .subjectCell {
    max-width: 150px;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
  }

  .paginationBtn {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 640px) {
  .table {
    font-size: 12px;
  }

  .table th,
  .table td {
    padding: 8px 4px;
  }

  .typeCell {
    flex-direction: column;
    gap: 2px;
    align-items: flex-start;
  }

  .customerCell,
  .subjectCell {
    max-width: 120px;
  }

  .statusBadge {
    font-size: 10px;
    padding: 2px 6px;
  }
}
