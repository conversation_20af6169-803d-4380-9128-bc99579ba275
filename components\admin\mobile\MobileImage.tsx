/**
 * Ocean Soul Sparkles Admin - Mobile Image Component
 * Responsive images optimized for mobile screens with lazy loading
 */

import React, { useState, useEffect, useRef } from 'react';
import { useIntersectionObserver } from '../../../lib/mobile/lazy-loading';
import styles from '../../../styles/admin/mobile/MobileImage.module.css';

interface MobileImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  sizes?: string;
  priority?: boolean;
  placeholder?: 'blur' | 'empty' | string;
  blurDataURL?: string;
  className?: string;
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
  quality?: number;
  format?: 'webp' | 'avif' | 'auto';
}

export default function MobileImage({
  src,
  alt,
  width,
  height,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  priority = false,
  placeholder = 'blur',
  blurDataURL,
  className = '',
  style = {},
  onLoad,
  onError,
  quality = 75,
  format = 'auto'
}: MobileImageProps) {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);
  const [elementRef, isIntersecting] = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px'
  });

  // Generate responsive image URLs
  const generateImageSrc = (originalSrc: string, targetWidth?: number): string => {
    if (!originalSrc) return '';
    
    // If it's already a data URL or external URL, return as-is
    if (originalSrc.startsWith('data:') || originalSrc.startsWith('http')) {
      return originalSrc;
    }

    // Generate optimized URL with query parameters
    const url = new URL(originalSrc, window.location.origin);
    
    if (targetWidth) {
      url.searchParams.set('w', targetWidth.toString());
    }
    
    url.searchParams.set('q', quality.toString());
    
    if (format !== 'auto') {
      url.searchParams.set('f', format);
    }

    return url.toString();
  };

  // Generate srcSet for responsive images
  const generateSrcSet = (originalSrc: string): string => {
    if (!originalSrc || originalSrc.startsWith('data:') || originalSrc.startsWith('http')) {
      return '';
    }

    const breakpoints = [320, 480, 768, 1024, 1280, 1920];
    const srcSetEntries = breakpoints.map(bp => 
      `${generateImageSrc(originalSrc, bp)} ${bp}w`
    );

    return srcSetEntries.join(', ');
  };

  // Get placeholder image
  const getPlaceholderSrc = (): string => {
    if (placeholder === 'empty') return '';
    if (typeof placeholder === 'string' && placeholder !== 'blur') return placeholder;
    if (blurDataURL) return blurDataURL;
    
    // Generate a simple blur placeholder
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="${width || 400}" height="${height || 300}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f0f0f0;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#e0e0e0;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)" />
        <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
              font-family="system-ui, sans-serif" font-size="14" fill="#999">
          Loading...
        </text>
      </svg>
    `)}`;
  };

  // Load image when in viewport or priority is true
  useEffect(() => {
    if ((priority || isIntersecting) && !isLoaded && !isError) {
      const img = new Image();
      
      img.onload = () => {
        setCurrentSrc(generateImageSrc(src, width));
        setIsLoaded(true);
        onLoad?.();
      };
      
      img.onerror = () => {
        setIsError(true);
        onError?.();
      };
      
      // Set srcset for responsive loading
      const srcSet = generateSrcSet(src);
      if (srcSet) {
        img.srcset = srcSet;
        img.sizes = sizes;
      }
      
      img.src = generateImageSrc(src, width);
    }
  }, [priority, isIntersecting, isLoaded, isError, src, width, sizes, onLoad, onError]);

  // Set initial placeholder
  useEffect(() => {
    if (!isLoaded && !isError) {
      setImageSrc(getPlaceholderSrc());
    }
  }, [isLoaded, isError, placeholder, blurDataURL, width, height]);

  // Update image source when loaded
  useEffect(() => {
    if (isLoaded && currentSrc) {
      setImageSrc(currentSrc);
    }
  }, [isLoaded, currentSrc]);

  const handleImageLoad = () => {
    if (imgRef.current) {
      imgRef.current.classList.add(styles.loaded);
    }
  };

  const handleImageError = () => {
    setIsError(true);
    setImageSrc(getErrorPlaceholder());
  };

  const getErrorPlaceholder = (): string => {
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="${width || 400}" height="${height || 300}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f5f5f5" stroke="#ddd" stroke-width="2"/>
        <text x="50%" y="45%" dominant-baseline="middle" text-anchor="middle" 
              font-family="system-ui, sans-serif" font-size="16" fill="#999">
          ⚠️
        </text>
        <text x="50%" y="60%" dominant-baseline="middle" text-anchor="middle" 
              font-family="system-ui, sans-serif" font-size="12" fill="#999">
          Failed to load
        </text>
      </svg>
    `)}`;
  };

  const imageProps = {
    ref: (el: HTMLImageElement | null) => {
      if (imgRef) {
        (imgRef as React.MutableRefObject<HTMLImageElement | null>).current = el;
      }
      if (elementRef) {
        (elementRef as React.MutableRefObject<HTMLImageElement | null>).current = el;
      }
    },
    src: imageSrc,
    alt,
    className: `${styles.mobileImage} ${className} ${isLoaded ? styles.loaded : ''} ${isError ? styles.error : ''}`,
    style: {
      width: width ? `${width}px` : undefined,
      height: height ? `${height}px` : undefined,
      ...style
    },
    onLoad: handleImageLoad,
    onError: handleImageError,
    loading: priority ? 'eager' as const : 'lazy' as const,
    decoding: 'async' as const
  };

  // Add responsive attributes if image is loaded
  if (isLoaded && !isError) {
    const srcSet = generateSrcSet(src);
    if (srcSet) {
      (imageProps as any).srcSet = srcSet;
      (imageProps as any).sizes = sizes;
    }
  }

  return <img {...imageProps} />;
}

/**
 * Mobile Image Gallery Component
 */
interface MobileImageGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    caption?: string;
  }>;
  columns?: number;
  gap?: number;
  className?: string;
  onImageClick?: (index: number) => void;
}

export function MobileImageGallery({
  images,
  columns = 2,
  gap = 8,
  className = '',
  onImageClick
}: MobileImageGalleryProps) {
  return (
    <div 
      className={`${styles.imageGallery} ${className}`}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: `${gap}px`
      }}
    >
      {images.map((image, index) => (
        <div 
          key={index}
          className={styles.galleryItem}
          onClick={() => onImageClick?.(index)}
        >
          <MobileImage
            src={image.src}
            alt={image.alt}
            className={styles.galleryImage}
            sizes={`(max-width: 768px) ${100 / columns}vw, ${100 / columns}vw`}
          />
          {image.caption && (
            <div className={styles.imageCaption}>
              {image.caption}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

/**
 * Mobile Avatar Component
 */
interface MobileAvatarProps {
  src?: string;
  alt: string;
  size?: 'small' | 'medium' | 'large';
  fallback?: string;
  className?: string;
  onClick?: () => void;
}

export function MobileAvatar({
  src,
  alt,
  size = 'medium',
  fallback,
  className = '',
  onClick
}: MobileAvatarProps) {
  const [hasError, setHasError] = useState(false);

  const sizeMap = {
    small: 32,
    medium: 48,
    large: 64
  };

  const avatarSize = sizeMap[size];

  const getFallbackContent = (): string => {
    if (fallback) return fallback;
    
    // Generate initials from alt text
    const initials = alt
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);

    return initials || '?';
  };

  const getFallbackSrc = (): string => {
    const initials = getFallbackContent();
    const colors = ['#16213e', '#4CAF50', '#2196F3', '#FF9800', '#9C27B0'];
    const colorIndex = initials.charCodeAt(0) % colors.length;
    const bgColor = colors[colorIndex];

    return `data:image/svg+xml;base64,${btoa(`
      <svg width="${avatarSize}" height="${avatarSize}" xmlns="http://www.w3.org/2000/svg">
        <circle cx="${avatarSize/2}" cy="${avatarSize/2}" r="${avatarSize/2}" fill="${bgColor}"/>
        <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
              font-family="system-ui, sans-serif" font-size="${avatarSize * 0.4}" 
              font-weight="500" fill="white">
          ${initials}
        </text>
      </svg>
    `)}`;
  };

  return (
    <div 
      className={`${styles.avatar} ${styles[size]} ${className} ${onClick ? styles.clickable : ''}`}
      onClick={onClick}
    >
      <MobileImage
        src={hasError || !src ? getFallbackSrc() : src}
        alt={alt}
        width={avatarSize}
        height={avatarSize}
        className={styles.avatarImage}
        onError={() => setHasError(true)}
        priority={true}
      />
    </div>
  );
}
