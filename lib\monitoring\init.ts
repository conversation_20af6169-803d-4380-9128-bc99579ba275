/**
 * Ocean Soul Sparkles Admin Dashboard - Performance Monitoring Initialization
 * Initialize performance monitoring and uptime checks on server startup
 */

import { startPerformanceMonitoring } from './performance-monitor';
import { startUptimeMonitoring } from './uptime-monitor';

let monitoringInitialized = false;

/**
 * Initialize all monitoring systems
 */
export function initializeMonitoring(): void {
  if (monitoringInitialized) {
    console.log('[MONITORING] Already initialized, skipping...');
    return;
  }

  try {
    console.log('[MONITORING] Initializing performance monitoring systems...');

    // Start performance monitoring
    startPerformanceMonitoring();

    // Start uptime monitoring
    startUptimeMonitoring();

    // Mark as initialized
    monitoringInitialized = true;

    console.log('[MONITORING] ✅ All monitoring systems initialized successfully');

  } catch (error) {
    console.error('[MONITORING] ❌ Failed to initialize monitoring systems:', error);
  }
}

/**
 * Check if monitoring is initialized
 */
export function isMonitoringInitialized(): boolean {
  return monitoringInitialized;
}

// Auto-initialize in server environment
if (typeof window === 'undefined') {
  // Small delay to ensure environment is ready
  setTimeout(initializeMonitoring, 1000);
}
