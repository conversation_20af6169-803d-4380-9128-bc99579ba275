/**
 * Ocean Soul Sparkles Admin Dashboard - Performance Monitoring Dashboard
 * Real-time performance monitoring and alerts dashboard
 */

import React, { useState, useEffect } from 'react';
import styles from '../../styles/admin/PerformanceDashboard.module.css';

interface PerformanceMetric {
  id: string;
  metric_type: string;
  endpoint?: string;
  method?: string;
  duration: number;
  status_code?: number;
  error_message?: string;
  created_at: string;
}

interface PerformanceAlert {
  id: string;
  alert_type: 'warning' | 'critical';
  metric_type: string;
  endpoint?: string;
  threshold_value: number;
  actual_value: number;
  message: string;
  resolved: boolean;
  created_at: string;
}

interface PerformanceSummary {
  avgResponseTime: number;
  totalRequests: number;
  errorRate: number;
  slowRequests: number;
  activeAlerts: number;
}

interface PerformanceData {
  metrics: PerformanceMetric[];
  alerts: PerformanceAlert[];
  summary: PerformanceSummary;
}

const PerformanceDashboard: React.FC = () => {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('24h');
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    fetchPerformanceData();
    
    if (autoRefresh) {
      const interval = setInterval(fetchPerformanceData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [timeRange, autoRefresh]);

  const fetchPerformanceData = async () => {
    try {
      const startDate = getStartDate(timeRange);
      const response = await fetch(`/api/admin/monitoring/performance?startDate=${startDate}&endDate=${new Date().toISOString()}`);
      
      if (response.ok) {
        const result = await response.json();
        setData(result.data);
      } else {
        console.error('Failed to fetch performance data');
      }
    } catch (error) {
      console.error('Error fetching performance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStartDate = (range: string): string => {
    const now = new Date();
    switch (range) {
      case '1h':
        return new Date(now.getTime() - 60 * 60 * 1000).toISOString();
      case '6h':
        return new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString();
      case '24h':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch('/api/admin/monitoring/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'resolve_alert',
          alertId
        })
      });

      if (response.ok) {
        fetchPerformanceData(); // Refresh data
      }
    } catch (error) {
      console.error('Error resolving alert:', error);
    }
  };

  const getPerformanceStatus = (avgResponseTime: number): string => {
    if (avgResponseTime < 200) return 'excellent';
    if (avgResponseTime < 500) return 'good';
    if (avgResponseTime < 1000) return 'warning';
    return 'critical';
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatTimestamp = (timestamp: string): string => {
    return new Date(timestamp).toLocaleString();
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>Loading performance data...</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>Failed to load performance data</div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Performance Monitoring</h2>
        <div className={styles.controls}>
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value)}
            className={styles.timeRangeSelect}
          >
            <option value="1h">Last Hour</option>
            <option value="6h">Last 6 Hours</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
          </select>
          <label className={styles.autoRefreshLabel}>
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
            />
            Auto Refresh
          </label>
          <button onClick={fetchPerformanceData} className={styles.refreshButton}>
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className={styles.summaryGrid}>
        <div className={`${styles.summaryCard} ${styles[getPerformanceStatus(data.summary.avgResponseTime)]}`}>
          <h3>Avg Response Time</h3>
          <div className={styles.summaryValue}>{formatDuration(data.summary.avgResponseTime)}</div>
        </div>
        <div className={styles.summaryCard}>
          <h3>Total Requests</h3>
          <div className={styles.summaryValue}>{data.summary.totalRequests.toLocaleString()}</div>
        </div>
        <div className={`${styles.summaryCard} ${data.summary.errorRate > 5 ? styles.warning : ''}`}>
          <h3>Error Rate</h3>
          <div className={styles.summaryValue}>{data.summary.errorRate}%</div>
        </div>
        <div className={`${styles.summaryCard} ${data.summary.slowRequests > 0 ? styles.warning : ''}`}>
          <h3>Slow Requests</h3>
          <div className={styles.summaryValue}>{data.summary.slowRequests}</div>
        </div>
        <div className={`${styles.summaryCard} ${data.summary.activeAlerts > 0 ? styles.critical : ''}`}>
          <h3>Active Alerts</h3>
          <div className={styles.summaryValue}>{data.summary.activeAlerts}</div>
        </div>
      </div>

      {/* Active Alerts */}
      {data.alerts.length > 0 && (
        <div className={styles.section}>
          <h3>Active Performance Alerts</h3>
          <div className={styles.alertsList}>
            {data.alerts.map((alert) => (
              <div key={alert.id} className={`${styles.alert} ${styles[alert.alert_type]}`}>
                <div className={styles.alertContent}>
                  <div className={styles.alertMessage}>{alert.message}</div>
                  <div className={styles.alertDetails}>
                    <span>Type: {alert.metric_type}</span>
                    {alert.endpoint && <span>Endpoint: {alert.endpoint}</span>}
                    <span>Threshold: {alert.threshold_value}ms</span>
                    <span>Actual: {alert.actual_value}ms</span>
                    <span>Created: {formatTimestamp(alert.created_at)}</span>
                  </div>
                </div>
                <button 
                  onClick={() => resolveAlert(alert.id)}
                  className={styles.resolveButton}
                >
                  Resolve
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Metrics */}
      <div className={styles.section}>
        <h3>Recent Performance Metrics</h3>
        <div className={styles.metricsTable}>
          <div className={styles.tableHeader}>
            <div>Timestamp</div>
            <div>Type</div>
            <div>Endpoint</div>
            <div>Method</div>
            <div>Duration</div>
            <div>Status</div>
          </div>
          <div className={styles.tableBody}>
            {data.metrics.slice(0, 20).map((metric) => (
              <div key={metric.id} className={styles.tableRow}>
                <div>{formatTimestamp(metric.created_at)}</div>
                <div>{metric.metric_type}</div>
                <div>{metric.endpoint || '-'}</div>
                <div>{metric.method || '-'}</div>
                <div className={metric.duration > 500 ? styles.slowDuration : ''}>
                  {formatDuration(metric.duration)}
                </div>
                <div className={metric.status_code && metric.status_code >= 400 ? styles.errorStatus : ''}>
                  {metric.status_code || '-'}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Tips */}
      <div className={styles.section}>
        <h3>Performance Optimization Tips</h3>
        <div className={styles.tips}>
          <div className={styles.tip}>
            <strong>API Response Times:</strong> Keep response times under 500ms for optimal user experience
          </div>
          <div className={styles.tip}>
            <strong>Database Queries:</strong> Monitor slow queries and add appropriate indexes
          </div>
          <div className={styles.tip}>
            <strong>Memory Usage:</strong> Keep memory usage below 80% to prevent performance degradation
          </div>
          <div className={styles.tip}>
            <strong>Error Monitoring:</strong> Investigate error rates above 1% immediately
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;

// CSS Module styles would be in styles/admin/PerformanceDashboard.module.css
