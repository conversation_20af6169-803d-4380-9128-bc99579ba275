import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuth } from '../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { id } = req.query;
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Email template ${id} API called - ${req.method}`);

  if (!id || typeof id !== 'string') {
    return res.status(400).json({
      error: 'Template ID is required',
      requestId
    });
  }

  try {
    // Authenticate admin request
    const authResult = await verifyAdminAuth(req);
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Authentication required',
        message: authResult.message || 'Authentication failed',
        requestId
      });
    }

    const { user } = authResult;

    if (!user) {
      return res.status(401).json({
        error: 'User not found',
        requestId
      });
    }

    // Only Admin and DEV can manage email templates
    if (user.role !== 'Admin' && user.role !== 'DEV') {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        message: 'Only admins can manage email templates',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { data: template, error } = await supabase
        .from('email_templates')
        .select(`
          id,
          name,
          type,
          subject,
          html_content,
          text_content,
          variables,
          is_active,
          is_default,
          created_at,
          updated_at
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error(`[${requestId}] Database error:`, error);
        return res.status(500).json({
          error: 'Failed to fetch email template',
          message: error.message,
          requestId
        });
      }

      if (!template) {
        return res.status(404).json({
          error: 'Template not found',
          requestId
        });
      }

      return res.status(200).json({
        template,
        requestId
      });
    }

    if (req.method === 'PUT') {
      const {
        name,
        type,
        subject,
        html_content,
        text_content,
        variables,
        is_active,
        is_default
      } = req.body;

      // Validate required fields
      if (!name || !type || !subject || !html_content) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Name, type, subject, and HTML content are required',
          requestId
        });
      }

      // Check if name already exists (excluding current template)
      const { data: existingTemplate } = await supabase
        .from('email_templates')
        .select('id')
        .eq('name', name)
        .neq('id', id)
        .single();

      if (existingTemplate) {
        return res.status(409).json({
          error: 'Template name already exists',
          message: 'A template with this name already exists',
          requestId
        });
      }

      // If setting as default, unset other defaults for this type
      if (is_default) {
        await supabase
          .from('email_templates')
          .update({ is_default: false })
          .eq('type', type)
          .neq('id', id);
      }

      const { data: template, error } = await supabase
        .from('email_templates')
        .update({
          name,
          type,
          subject,
          html_content,
          text_content,
          variables: variables || [],
          is_active,
          is_default,
          updated_by: user.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error(`[${requestId}] Error updating template:`, error);
        return res.status(500).json({
          error: 'Failed to update email template',
          message: error.message,
          requestId
        });
      }

      return res.status(200).json({
        template,
        message: 'Email template updated successfully',
        requestId
      });
    }

    if (req.method === 'DELETE') {
      // Check if template is being used in communications
      const { count: usageCount } = await supabase
        .from('customer_communications')
        .select('*', { count: 'exact', head: true })
        .eq('template_id', id);

      if (usageCount && usageCount > 0) {
        return res.status(400).json({
          error: 'Cannot delete template',
          message: `This template has been used in ${usageCount} communication(s). Deactivate it instead.`,
          requestId
        });
      }

      const { error } = await supabase
        .from('email_templates')
        .delete()
        .eq('id', id);

      if (error) {
        console.error(`[${requestId}] Error deleting template:`, error);
        return res.status(500).json({
          error: 'Failed to delete email template',
          message: error.message,
          requestId
        });
      }

      return res.status(200).json({
        message: 'Email template deleted successfully',
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
}
