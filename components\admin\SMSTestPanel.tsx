/**
 * SMS Test Panel Component
 * 
 * Provides interface for testing SMS functionality with different templates
 * and settings configurations.
 */

import { useState } from 'react'
import styles from '../../styles/admin/SMSTestPanel.module.css'

interface SMSTestPanelProps {
  onClose: () => void
}

export default function SMSTestPanel({ onClose }: SMSTestPanelProps) {
  const [testData, setTestData] = useState({
    type: 'test_sms',
    to: '',
    message: 'Test message from Ocean Soul Sparkles admin dashboard. This is a test to verify SMS functionality is working correctly.'
  })
  const [sending, setSending] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [smsStatus, setSmsStatus] = useState<any>(null)

  const handleSendTest = async () => {
    try {
      setSending(true)
      setResult(null)
      
      const response = await fetch('/api/admin/notifications/sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
      })
      
      const data = await response.json()
      setResult(data)
      
      if (data.success) {
        alert('Test SMS sent successfully!')
      } else {
        alert(`SMS test failed: ${data.message}`)
      }
    } catch (error) {
      console.error('Error sending test SMS:', error)
      alert('Error sending test SMS. Please try again.')
    } finally {
      setSending(false)
    }
  }

  const handleCheckStatus = async () => {
    try {
      const response = await fetch('/api/admin/notifications/sms')
      const data = await response.json()
      setSmsStatus(data)
    } catch (error) {
      console.error('Error checking SMS status:', error)
    }
  }

  const testTemplates = [
    {
      type: 'booking_confirmation',
      name: 'Booking Confirmation',
      data: {
        customerName: 'Sarah Johnson',
        serviceName: 'Hair Braiding',
        date: 'March 15, 2025',
        time: '2:30 PM',
        customerPhone: testData.to
      }
    },
    {
      type: 'booking_reminder',
      name: 'Booking Reminder',
      data: {
        customerName: 'Sarah Johnson',
        serviceName: 'Hair Braiding',
        time: '2:30 PM',
        customerPhone: testData.to
      }
    },
    {
      type: 'booking_cancellation',
      name: 'Booking Cancellation',
      data: {
        customerName: 'Sarah Johnson',
        serviceName: 'Hair Braiding',
        date: 'March 15, 2025',
        time: '2:30 PM',
        customerPhone: testData.to
      }
    }
  ]

  const handleTestTemplate = async (template: any) => {
    if (!testData.to) {
      alert('Please enter a phone number first')
      return
    }

    try {
      setSending(true)
      setResult(null)
      
      const response = await fetch('/api/admin/notifications/sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: template.type,
          data: template.data
        })
      })
      
      const data = await response.json()
      setResult(data)
      
      if (data.success) {
        alert(`${template.name} SMS sent successfully!`)
      } else {
        alert(`${template.name} SMS failed: ${data.message}`)
      }
    } catch (error) {
      console.error('Error sending template SMS:', error)
      alert('Error sending template SMS. Please try again.')
    } finally {
      setSending(false)
    }
  }

  return (
    <div className={styles.overlay}>
      <div className={styles.panel}>
        <div className={styles.header}>
          <h2>SMS Test Panel</h2>
          <button onClick={onClose} className={styles.closeBtn}>×</button>
        </div>

        <div className={styles.content}>
          {/* SMS Status */}
          <div className={styles.section}>
            <h3>SMS Service Status</h3>
            <button onClick={handleCheckStatus} className={styles.statusBtn}>
              Check Status
            </button>
            {smsStatus && (
              <div className={styles.statusInfo}>
                <p><strong>Configured:</strong> {smsStatus.status.configured ? 'Yes' : 'No'}</p>
                <p><strong>Provider:</strong> {smsStatus.status.provider}</p>
                <p><strong>Environment:</strong> {smsStatus.status.environment}</p>
                {smsStatus.verification && (
                  <div>
                    <p><strong>Verification:</strong> {smsStatus.verification.configured ? 'Valid' : 'Invalid'}</p>
                    {smsStatus.verification.error && (
                      <p className={styles.error}><strong>Error:</strong> {smsStatus.verification.error}</p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Custom SMS Test */}
          <div className={styles.section}>
            <h3>Custom SMS Test</h3>
            <div className={styles.formGroup}>
              <label>Phone Number:</label>
              <input
                type="tel"
                value={testData.to}
                onChange={(e) => setTestData(prev => ({ ...prev, to: e.target.value }))}
                placeholder="+61412345678"
              />
              <small>Enter phone number in international format</small>
            </div>
            <div className={styles.formGroup}>
              <label>Message:</label>
              <textarea
                value={testData.message}
                onChange={(e) => setTestData(prev => ({ ...prev, message: e.target.value }))}
                rows={3}
                placeholder="Enter test message"
              />
            </div>
            <button 
              onClick={handleSendTest}
              disabled={sending || !testData.to || !testData.message}
              className={styles.sendBtn}
            >
              {sending ? 'Sending...' : 'Send Test SMS'}
            </button>
          </div>

          {/* Template Tests */}
          <div className={styles.section}>
            <h3>Template Tests</h3>
            <p>Test SMS templates with sample data:</p>
            <div className={styles.templateTests}>
              {testTemplates.map(template => (
                <button
                  key={template.type}
                  onClick={() => handleTestTemplate(template)}
                  disabled={sending || !testData.to}
                  className={styles.templateBtn}
                >
                  Test {template.name}
                </button>
              ))}
            </div>
          </div>

          {/* Result Display */}
          {result && (
            <div className={styles.section}>
              <h3>Last Result</h3>
              <div className={`${styles.result} ${result.success ? styles.success : styles.error}`}>
                <p><strong>Status:</strong> {result.success ? 'Success' : 'Failed'}</p>
                {result.messageId && <p><strong>Message ID:</strong> {result.messageId}</p>}
                {result.message && <p><strong>Message:</strong> {result.message}</p>}
                {result.skipped && <p><strong>Skipped:</strong> {result.reason}</p>}
                {result.fallback && <p><strong>Fallback:</strong> Used console logging</p>}
                {result.error && <p><strong>Error:</strong> {result.error}</p>}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className={styles.section}>
            <h3>Instructions</h3>
            <ul className={styles.instructions}>
              <li>Enter a valid phone number in international format (e.g., +61412345678)</li>
              <li>If Twilio is not configured, messages will be logged to console</li>
              <li>Check SMS settings in Admin Settings to enable/disable SMS types</li>
              <li>Template tests use sample data - actual bookings would use real data</li>
              <li>Monitor the browser console for detailed SMS service logs</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
