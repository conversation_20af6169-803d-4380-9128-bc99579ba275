/**
 * Ocean Soul Sparkles Admin - Data Export Utilities
 * Provides reusable export functionality for all admin data types
 */

export interface ExportColumn {
  key: string;
  label: string;
  formatter?: (value: any, row: any) => string;
}

export interface ExportOptions {
  filename?: string;
  columns?: ExportColumn[];
  includeTimestamp?: boolean;
  dateFormat?: 'short' | 'long' | 'iso';
}

/**
 * Export data to CSV format
 */
export function exportToCSV(data: any[], options: ExportOptions = {}) {
  const {
    filename = 'export',
    columns,
    includeTimestamp = true,
    dateFormat = 'short'
  } = options;

  if (!data || data.length === 0) {
    throw new Error('No data to export');
  }

  // Auto-generate columns if not provided
  const exportColumns = columns || generateColumnsFromData(data[0]);
  
  // Create CSV content
  const headers = exportColumns.map(col => col.label);
  const rows = data.map(row => 
    exportColumns.map(col => {
      const value = getNestedValue(row, col.key);
      return col.formatter ? col.formatter(value, row) : formatValue(value, dateFormat);
    })
  );

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${String(cell || '').replace(/"/g, '""')}"`).join(','))
    .join('\n');

  // Generate filename with timestamp
  const timestamp = includeTimestamp ? `-${new Date().toISOString().split('T')[0]}` : '';
  const finalFilename = `${filename}${timestamp}.csv`;

  // Download file
  downloadFile(csvContent, finalFilename, 'text/csv');
}

/**
 * Export bookings data with proper formatting
 */
export function exportBookings(bookings: any[], options: ExportOptions = {}) {
  const columns: ExportColumn[] = [
    { key: 'customer_name', label: 'Customer Name' },
    { key: 'customer_email', label: 'Customer Email' },
    { key: 'customer_phone', label: 'Customer Phone' },
    { key: 'service_name', label: 'Service' },
    { key: 'artist_name', label: 'Artist' },
    { key: 'booking_date', label: 'Date', formatter: (value) => formatDate(value) },
    { key: 'booking_time', label: 'Time' },
    { key: 'status', label: 'Status', formatter: (value) => value?.toUpperCase() || 'UNKNOWN' },
    { key: 'total_amount', label: 'Amount', formatter: (value) => formatCurrency(value) },
    { key: 'notes', label: 'Notes' },
    { key: 'created_at', label: 'Created', formatter: (value) => formatDateTime(value) }
  ];

  exportToCSV(bookings, {
    filename: 'bookings',
    columns,
    ...options
  });
}

/**
 * Export services data with proper formatting
 */
export function exportServices(services: any[], options: ExportOptions = {}) {
  const columns: ExportColumn[] = [
    { key: 'name', label: 'Service Name' },
    { key: 'category', label: 'Category' },
    { key: 'description', label: 'Description' },
    { key: 'base_price', label: 'Base Price', formatter: (value) => formatCurrency(value) },
    { key: 'duration', label: 'Duration (min)' },
    { key: 'is_active', label: 'Status', formatter: (value) => value ? 'ACTIVE' : 'INACTIVE' },
    { key: 'total_bookings', label: 'Total Bookings' },
    { key: 'created_at', label: 'Created', formatter: (value) => formatDateTime(value) }
  ];

  exportToCSV(services, {
    filename: 'services',
    columns,
    ...options
  });
}

/**
 * Export customers data with proper formatting
 */
export function exportCustomers(customers: any[], options: ExportOptions = {}) {
  const columns: ExportColumn[] = [
    { key: 'first_name', label: 'First Name' },
    { key: 'last_name', label: 'Last Name' },
    { key: 'email', label: 'Email' },
    { key: 'phone', label: 'Phone' },
    { key: 'total_bookings', label: 'Total Bookings' },
    { key: 'notes', label: 'Notes' },
    { key: 'created_at', label: 'Created', formatter: (value) => formatDateTime(value) }
  ];

  exportToCSV(customers, {
    filename: 'customers',
    columns,
    ...options
  });
}

/**
 * Export products data with proper formatting
 */
export function exportProducts(products: any[], options: ExportOptions = {}) {
  const columns: ExportColumn[] = [
    { key: 'name', label: 'Product Name' },
    { key: 'category', label: 'Category' },
    { key: 'description', label: 'Description' },
    { key: 'price', label: 'Price', formatter: (value) => formatCurrency(value) },
    { key: 'stock_quantity', label: 'Stock' },
    { key: 'is_active', label: 'Status', formatter: (value) => value ? 'ACTIVE' : 'INACTIVE' },
    { key: 'created_at', label: 'Created', formatter: (value) => formatDateTime(value) }
  ];

  exportToCSV(products, {
    filename: 'products',
    columns,
    ...options
  });
}

/**
 * Export inventory data with proper formatting
 */
export function exportInventory(inventory: any[], options: ExportOptions = {}) {
  const columns: ExportColumn[] = [
    { key: 'name', label: 'Item Name' },
    { key: 'category', label: 'Category' },
    { key: 'current_stock', label: 'Current Stock' },
    { key: 'minimum_stock', label: 'Minimum Stock' },
    { key: 'unit_cost', label: 'Unit Cost', formatter: (value) => formatCurrency(value) },
    { key: 'supplier_name', label: 'Supplier' },
    { key: 'last_restocked', label: 'Last Restocked', formatter: (value) => formatDate(value) },
    { key: 'created_at', label: 'Created', formatter: (value) => formatDateTime(value) }
  ];

  exportToCSV(inventory, {
    filename: 'inventory',
    columns,
    ...options
  });
}

// Helper functions
function generateColumnsFromData(sampleRow: any): ExportColumn[] {
  return Object.keys(sampleRow).map(key => ({
    key,
    label: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }));
}

function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

function formatValue(value: any, dateFormat: string): string {
  if (value === null || value === undefined) return '';
  if (typeof value === 'boolean') return value ? 'Yes' : 'No';
  if (value instanceof Date) return formatDateTime(value);
  if (typeof value === 'string' && isDateString(value)) {
    return formatDateTime(value);
  }
  return String(value);
}

function formatDate(value: any): string {
  if (!value) return '';
  const date = new Date(value);
  return date.toLocaleDateString('en-AU');
}

function formatDateTime(value: any): string {
  if (!value) return '';
  const date = new Date(value);
  return date.toLocaleDateString('en-AU') + ' ' + date.toLocaleTimeString('en-AU', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
}

function formatCurrency(value: any): string {
  if (!value || isNaN(value)) return '$0.00';
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD'
  }).format(Number(value));
}

function isDateString(value: string): boolean {
  return /^\d{4}-\d{2}-\d{2}/.test(value) && !isNaN(Date.parse(value));
}

function downloadFile(content: string, filename: string, mimeType: string) {
  const blob = new Blob([content], { type: mimeType });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}
