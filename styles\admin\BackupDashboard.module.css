/**
 * Ocean Soul Sparkles Admin Dashboard - Backup Dashboard Styles
 */

.container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.header h2 {
  color: #2c3e50;
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.headerActions {
  display: flex;
  gap: 10px;
}

.verifyButton, .refreshButton {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.verifyButton {
  background: #3498db;
  color: white;
}

.verifyButton:hover:not(:disabled) {
  background: #2980b9;
}

.verifyButton:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.refreshButton {
  background: #95a5a6;
  color: white;
}

.refreshButton:hover {
  background: #7f8c8d;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
}

.error {
  color: #e74c3c;
}

/* Alerts Section */
.alertsSection {
  margin-bottom: 30px;
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alertsSection h3 {
  margin: 0 0 15px 0;
  color: #e74c3c;
  font-size: 20px;
}

.alertsList {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.alert {
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert.error {
  background: #fdf2f2;
  border-left-color: #e74c3c;
}

.alert.warning {
  background: #fefbf3;
  border-left-color: #f39c12;
}

.alert.info {
  background: #f0f8ff;
  border-left-color: #3498db;
}

.alertMessage {
  font-weight: 500;
  flex: 1;
}

.alertTime {
  font-size: 12px;
  color: #666;
}

/* Status Grid */
.statusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.statusCard {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3498db;
}

.statusCard h3 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusValue {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.enabled {
  color: #27ae60;
}

.disabled {
  color: #e74c3c;
}

.backupDetails, .healthDetails, .integrityDetails {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.noBackup {
  color: #e74c3c;
  font-style: italic;
}

.successRate {
  font-size: 18px;
  font-weight: bold;
}

.integrityStatus {
  font-size: 18px;
  font-weight: bold;
}

/* Sections */
.section {
  background: white;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

/* Storage Information */
.storageInfo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.storageItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.storageLabel {
  font-weight: 500;
  color: #666;
}

.storageValue {
  font-weight: 600;
  color: #2c3e50;
}

/* Verification Results */
.verificationHeader {
  margin-bottom: 20px;
}

.verificationInfo {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  font-size: 14px;
  color: #666;
}

.verificationStatus {
  font-weight: 600;
}

.verificationSummary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* Table Details */
.tableDetails h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.tableList {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.tableItem {
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.tableName {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  margin-bottom: 8px;
}

.tableStatus {
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
}

.tableInfo {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.tableIssues {
  margin-top: 10px;
}

.issue {
  font-size: 13px;
  color: #e67e22;
  margin-bottom: 5px;
}

/* Recommendations */
.recommendations h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.recommendationsList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendation {
  padding: 10px 15px;
  margin-bottom: 8px;
  background: #e8f4fd;
  border-left: 4px solid #3498db;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
}

/* Quick Actions */
.quickActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.actionButton {
  padding: 12px 20px;
  border: 1px solid #3498db;
  background: white;
  color: #3498db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.actionButton:hover:not(:disabled) {
  background: #3498db;
  color: white;
}

.actionButton:disabled {
  border-color: #bdc3c7;
  color: #bdc3c7;
  cursor: not-allowed;
}

/* Implementation Status */
.implementationStatus {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.statusItem {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 14px;
}

.statusIcon {
  font-size: 18px;
  min-width: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .headerActions {
    justify-content: center;
  }

  .statusGrid {
    grid-template-columns: 1fr;
  }

  .storageInfo {
    grid-template-columns: 1fr;
  }

  .verificationInfo {
    flex-direction: column;
    gap: 10px;
  }

  .verificationSummary {
    grid-template-columns: 1fr;
  }

  .tableInfo {
    flex-direction: column;
    gap: 5px;
  }

  .quickActions {
    grid-template-columns: 1fr;
  }
}

/* Animation for loading states */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}
