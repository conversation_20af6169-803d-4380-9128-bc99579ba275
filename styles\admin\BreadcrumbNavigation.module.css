/**
 * Ocean Soul Sparkles Admin - Breadcrumb Navigation Styles
 * Dynamic breadcrumb navigation with responsive design
 */

.breadcrumbNavigation {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;
}

.breadcrumbList {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  min-width: 0;
  flex-wrap: nowrap;
  overflow: hidden;
}

.breadcrumbItem {
  display: flex;
  align-items: center;
  min-width: 0;
  flex-shrink: 0;
}

.breadcrumbLink {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border-radius: var(--admin-radius-sm, 4px);
  text-decoration: none;
  color: var(--admin-text-secondary, #666666);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--admin-transition-normal, 0.2s ease);
  min-width: 0;
  max-width: 200px;
}

.breadcrumbLink:hover {
  color: var(--admin-primary, #3788d8);
  background: rgba(55, 136, 216, 0.1);
}

.breadcrumbLink:focus {
  outline: 2px solid var(--admin-primary, #3788d8);
  outline-offset: 2px;
}

.breadcrumbCurrent {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  color: var(--admin-text-primary, #1a1a1a);
  font-size: 0.875rem;
  font-weight: 600;
  min-width: 0;
  max-width: 200px;
}

.breadcrumbCurrent.active {
  color: var(--admin-primary, #3788d8);
}

.breadcrumbIcon {
  font-size: 1rem;
  flex-shrink: 0;
  line-height: 1;
}

.breadcrumbLabel {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.breadcrumbSeparator {
  color: var(--admin-text-secondary, #666666);
  font-size: 0.875rem;
  margin: 0 8px;
  flex-shrink: 0;
  opacity: 0.6;
}

/* Hover effects */
.breadcrumbItem:hover .breadcrumbSeparator {
  opacity: 1;
}

/* Focus states for accessibility */
.breadcrumbLink:focus-visible {
  outline: 2px solid var(--admin-primary, #3788d8);
  outline-offset: 2px;
  border-radius: var(--admin-radius-sm, 4px);
}

/* Ellipsis item styling */
.breadcrumbItem:has(.breadcrumbLabel:contains('...')) .breadcrumbLink {
  color: var(--admin-text-secondary, #666666);
  cursor: default;
  pointer-events: none;
}

.breadcrumbItem:has(.breadcrumbLabel:contains('...')) .breadcrumbLink:hover {
  background: transparent;
  color: var(--admin-text-secondary, #666666);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .breadcrumbNavigation {
    max-width: 100%;
  }

  .breadcrumbList {
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-bottom: 2px;
  }

  .breadcrumbList::-webkit-scrollbar {
    display: none;
  }

  .breadcrumbItem {
    flex-shrink: 0;
  }

  .breadcrumbLink,
  .breadcrumbCurrent {
    padding: 8px 12px;
    font-size: 0.9rem;
    max-width: 150px;
  }

  .breadcrumbIcon {
    font-size: 1.1rem;
  }

  .breadcrumbSeparator {
    margin: 0 6px;
    font-size: 0.9rem;
  }

  /* Hide icons on very small screens to save space */
  @media (max-width: 480px) {
    .breadcrumbIcon {
      display: none;
    }

    .breadcrumbLink,
    .breadcrumbCurrent {
      gap: 0;
      padding: 8px 10px;
      max-width: 120px;
    }

    .breadcrumbSeparator {
      margin: 0 4px;
    }
  }
}

/* Tablet responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .breadcrumbLink,
  .breadcrumbCurrent {
    max-width: 180px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .breadcrumbLink {
    border: 1px solid transparent;
  }

  .breadcrumbLink:hover {
    border-color: var(--admin-primary, #3788d8);
  }

  .breadcrumbLink:focus {
    border-color: var(--admin-primary, #3788d8);
    outline-width: 3px;
  }

  .breadcrumbCurrent.active {
    border: 1px solid var(--admin-primary, #3788d8);
    border-radius: var(--admin-radius-sm, 4px);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .breadcrumbLink {
    color: var(--admin-text-secondary-dark, #cccccc);
  }

  .breadcrumbLink:hover {
    color: var(--admin-primary-dark, #5ba3f5);
    background: rgba(91, 163, 245, 0.1);
  }

  .breadcrumbCurrent {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .breadcrumbCurrent.active {
    color: var(--admin-primary-dark, #5ba3f5);
  }

  .breadcrumbSeparator {
    color: var(--admin-text-secondary-dark, #cccccc);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .breadcrumbLink {
    transition: none;
  }
}

/* Print styles */
@media print {
  .breadcrumbNavigation {
    display: none;
  }
}

/* RTL support */
[dir="rtl"] .breadcrumbList {
  direction: rtl;
}

[dir="rtl"] .breadcrumbSeparator {
  transform: scaleX(-1);
}

/* Animation for breadcrumb updates */
@keyframes breadcrumbFadeIn {
  from {
    opacity: 0;
    transform: translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.breadcrumbItem {
  animation: breadcrumbFadeIn 0.2s ease-out;
}

/* Truncation for very long breadcrumb chains */
.breadcrumbNavigation.truncated .breadcrumbList {
  position: relative;
}

.breadcrumbNavigation.truncated .breadcrumbList::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 20px;
  background: linear-gradient(to right, transparent, var(--admin-bg-primary, #ffffff));
  pointer-events: none;
}

/* Accessibility improvements */
.breadcrumbNavigation[aria-label] {
  position: relative;
}

/* Screen reader only text for better accessibility */
.srOnly {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus management for keyboard navigation */
.breadcrumbLink:focus + .breadcrumbSeparator {
  opacity: 1;
}

/* Hover state for the entire breadcrumb item */
.breadcrumbItem:hover .breadcrumbSeparator {
  color: var(--admin-primary, #3788d8);
  opacity: 0.8;
}

/* Loading state (for future use) */
.breadcrumbNavigation.loading {
  opacity: 0.6;
  pointer-events: none;
}

.breadcrumbNavigation.loading .breadcrumbItem {
  animation: none;
}

/* Error state (for future use) */
.breadcrumbNavigation.error .breadcrumbLink {
  color: var(--admin-danger, #dc3545);
}

.breadcrumbNavigation.error .breadcrumbCurrent {
  color: var(--admin-danger, #dc3545);
}
