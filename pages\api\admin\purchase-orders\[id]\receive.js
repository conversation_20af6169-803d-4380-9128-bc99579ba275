import { verifyAdminToken } from '../../../../../lib/auth/admin-auth';
import { supabaseAdmin } from '../../../../../lib/supabase-admin';

/**
 * Purchase Order Receiving API Endpoint
 * 
 * Handles receiving purchase order items and updating inventory
 * Supports partial and full receiving workflows
 */
export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `po-receive-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    const { id } = req.query;

    console.log(`[${requestId}] Purchase order receiving API request:`, {
      method: req.method,
      purchaseOrderId: id,
      userAgent: req.headers['user-agent']
    });

    // Only allow POST method for receiving
    if (req.method !== 'POST') {
      return res.status(405).json({ 
        error: 'Method not allowed',
        requestId 
      });
    }

    // Validate purchase order ID
    if (!id || typeof id !== 'string') {
      return res.status(400).json({ 
        error: 'Invalid purchase order ID',
        requestId 
      });
    }

    // Verify admin authentication
    const authResult = await verifyAdminToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ 
        error: 'Unauthorized',
        requestId 
      });
    }

    // Check admin permissions
    if (!['DEV', 'Admin'].includes(authResult.user.role)) {
      console.log(`[${requestId}] Insufficient permissions:`, authResult.user.role);
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        requestId 
      });
    }

    return await handleReceivePurchaseOrder(req, res, requestId, id, authResult.user);

  } catch (error) {
    console.error(`[${requestId}] Purchase order receiving API error:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message,
      requestId 
    });
  }
}

/**
 * Handle POST request - Receive purchase order items
 */
async function handleReceivePurchaseOrder(req, res, requestId, purchaseOrderId, user) {
  try {
    const { receivedItems, actualDeliveryDate, notes } = req.body;

    // Validate required fields
    if (!receivedItems || !Array.isArray(receivedItems) || receivedItems.length === 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Received items array is required',
        requestId
      });
    }

    // Check if purchase order exists and is in correct status
    const { data: purchaseOrder, error: poError } = await supabaseAdmin
      .from('purchase_orders')
      .select(`
        id,
        po_number,
        status,
        supplier_id,
        suppliers (name)
      `)
      .eq('id', purchaseOrderId)
      .single();

    if (poError) {
      if (poError.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Purchase order not found',
          requestId
        });
      }
      throw poError;
    }

    // Validate purchase order status
    if (!['confirmed', 'sent'].includes(purchaseOrder.status)) {
      return res.status(400).json({
        error: 'Invalid purchase order status',
        message: 'Purchase order must be confirmed or sent to receive items',
        requestId
      });
    }

    // Get existing purchase order items
    const { data: existingItems, error: itemsError } = await supabaseAdmin
      .from('purchase_order_items')
      .select('*')
      .eq('purchase_order_id', purchaseOrderId);

    if (itemsError) {
      throw itemsError;
    }

    // Validate received items
    const itemsMap = new Map(existingItems.map(item => [item.id, item]));
    const updatedItems = [];
    let totalReceivedValue = 0;

    for (const receivedItem of receivedItems) {
      const { itemId, receivedQuantity } = receivedItem;

      if (!itemId || receivedQuantity === undefined || receivedQuantity < 0) {
        return res.status(400).json({
          error: 'Validation failed',
          message: 'Each received item must have itemId and non-negative receivedQuantity',
          requestId
        });
      }

      const existingItem = itemsMap.get(itemId);
      if (!existingItem) {
        return res.status(404).json({
          error: 'Purchase order item not found',
          message: `Item ${itemId} not found in purchase order`,
          requestId
        });
      }

      const newReceivedQuantity = existingItem.received_quantity + parseInt(receivedQuantity);
      if (newReceivedQuantity > existingItem.quantity) {
        return res.status(400).json({
          error: 'Validation failed',
          message: `Cannot receive more than ordered quantity for item ${itemId}`,
          requestId
        });
      }

      updatedItems.push({
        ...existingItem,
        receivedQuantity: parseInt(receivedQuantity),
        newReceivedQuantity
      });

      totalReceivedValue += parseInt(receivedQuantity) * existingItem.unit_cost;
    }

    // Start transaction-like operations
    const results = {
      updatedItems: [],
      updatedInventory: [],
      updatedPurchaseOrder: null
    };

    try {
      // Update purchase order items received quantities
      for (const item of updatedItems) {
        if (item.receivedQuantity > 0) {
          const { data: updatedItem, error: updateError } = await supabaseAdmin
            .from('purchase_order_items')
            .update({
              received_quantity: item.newReceivedQuantity
            })
            .eq('id', item.id)
            .select()
            .single();

          if (updateError) {
            throw updateError;
          }

          results.updatedItems.push(updatedItem);

          // Update inventory if item has inventory_id
          if (item.inventory_id && item.receivedQuantity > 0) {
            const { data: updatedInventory, error: inventoryError } = await supabaseAdmin
              .from('inventory')
              .update({
                quantity_on_hand: supabaseAdmin.sql`quantity_on_hand + ${item.receivedQuantity}`,
                updated_at: new Date().toISOString()
              })
              .eq('id', item.inventory_id)
              .select()
              .single();

            if (inventoryError) {
              console.warn(`[${requestId}] Error updating inventory for item ${item.id}:`, inventoryError);
            } else {
              results.updatedInventory.push(updatedInventory);
            }
          }
        }
      }

      // Check if all items are fully received
      const { data: allItems, error: checkError } = await supabaseAdmin
        .from('purchase_order_items')
        .select('quantity, received_quantity')
        .eq('purchase_order_id', purchaseOrderId);

      if (checkError) {
        throw checkError;
      }

      const fullyReceived = allItems.every(item => item.received_quantity >= item.quantity);
      const newStatus = fullyReceived ? 'received' : 'confirmed';

      // Update purchase order status and delivery date
      const updateData = {
        status: newStatus,
        updated_at: new Date().toISOString()
      };

      if (actualDeliveryDate) {
        updateData.actual_delivery_date = actualDeliveryDate;
      }

      if (notes) {
        updateData.notes = notes.trim();
      }

      const { data: updatedPO, error: poUpdateError } = await supabaseAdmin
        .from('purchase_orders')
        .update(updateData)
        .eq('id', purchaseOrderId)
        .select()
        .single();

      if (poUpdateError) {
        throw poUpdateError;
      }

      results.updatedPurchaseOrder = updatedPO;

      console.log(`[${requestId}] Purchase order receiving completed successfully:`, {
        id: purchaseOrder.id,
        poNumber: purchaseOrder.po_number,
        supplier: purchaseOrder.suppliers?.name,
        itemsReceived: results.updatedItems.length,
        inventoryUpdated: results.updatedInventory.length,
        totalValue: totalReceivedValue.toFixed(2),
        newStatus,
        fullyReceived,
        receivedBy: user.email
      });

      return res.status(200).json({
        message: fullyReceived ? 'Purchase order fully received' : 'Items received successfully',
        purchaseOrder: results.updatedPurchaseOrder,
        receivedItems: results.updatedItems,
        updatedInventory: results.updatedInventory,
        summary: {
          itemsReceived: results.updatedItems.length,
          inventoryUpdated: results.updatedInventory.length,
          totalValue: totalReceivedValue,
          fullyReceived
        },
        requestId
      });

    } catch (transactionError) {
      console.error(`[${requestId}] Error during receiving transaction:`, transactionError);
      throw transactionError;
    }

  } catch (error) {
    console.error(`[${requestId}] Error receiving purchase order:`, error);
    throw error;
  }
}
