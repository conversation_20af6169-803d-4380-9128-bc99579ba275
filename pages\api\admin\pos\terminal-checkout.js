import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth/admin-auth'

/**
 * API endpoint for Square Terminal checkout operations
 * Handles terminal checkout creation and status polling
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Square Terminal checkout API called`)

  try {
    // Authenticate admin request
    const { user, error: authError } = await authenticateAdminRequest(req)
    if (authError || !user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: authError?.message || 'Authentication failed',
        requestId
      })
    }

    // Check Square configuration
    const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN
    const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID
    const squareEnvironment = process.env.SQUARE_ENVIRONMENT || 'sandbox'

    if (!squareAccessToken || !squareLocationId) {
      return res.status(500).json({
        error: 'Square configuration missing',
        message: 'Square Terminal integration is not properly configured',
        requestId
      })
    }

    // Initialize Square Client
    const { Client } = require('square')
    const squareClient = new Client({
      accessToken: squareAccessToken,
      environment: squareEnvironment === 'production' ? 'production' : 'sandbox'
    })

    if (req.method === 'POST') {
      // Create terminal checkout
      const { deviceId, amountMoney, orderDetails, paymentOptions } = req.body

      if (!deviceId || !amountMoney) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'deviceId and amountMoney are required',
          requestId
        })
      }

      try {
        const terminalApi = squareClient.terminalApi

        const checkoutRequest = {
          idempotencyKey: `terminal_${requestId}_${Date.now()}`,
          checkout: {
            amountMoney: {
              amount: amountMoney.amount,
              currency: amountMoney.currency || 'AUD'
            },
            deviceOptions: {
              deviceId: deviceId
            },
            paymentType: 'CARD_PRESENT',
            note: orderDetails?.service || 'POS Terminal Payment',
            paymentOptions: {
              autocomplete: paymentOptions?.autocomplete || true,
              collectSignature: paymentOptions?.collectSignature || true,
              allowTipping: paymentOptions?.allowTipping || false
            }
          }
        }

        console.log(`[${requestId}] Creating terminal checkout:`, {
          deviceId,
          amount: amountMoney.amount,
          currency: amountMoney.currency
        })

        const { result, statusCode } = await terminalApi.createTerminalCheckout(checkoutRequest)

        console.log(`[${requestId}] Terminal checkout response:`, statusCode)

        if (statusCode === 200 && result.checkout) {
          return res.status(200).json({
            success: true,
            checkout: {
              id: result.checkout.id,
              status: result.checkout.status,
              amountMoney: result.checkout.amountMoney,
              deviceId: result.checkout.deviceOptions?.deviceId,
              createdAt: result.checkout.createdAt,
              updatedAt: result.checkout.updatedAt
            },
            requestId
          })
        } else {
          console.error(`[${requestId}] Failed to create checkout:`, result)
          return res.status(400).json({
            error: 'Failed to create terminal checkout',
            message: result.errors?.[0]?.detail || 'Unknown error',
            requestId
          })
        }
      } catch (error) {
        console.error(`[${requestId}] Error creating terminal checkout:`, error)
        return res.status(500).json({
          error: 'Failed to create terminal checkout',
          message: error.message,
          requestId
        })
      }

    } else if (req.method === 'GET') {
      // Get checkout status
      const { checkoutId } = req.query

      if (!checkoutId) {
        return res.status(400).json({
          error: 'Missing checkoutId',
          message: 'checkoutId is required for status updates',
          requestId
        })
      }

      try {
        const terminalApi = squareClient.terminalApi

        const { result, statusCode } = await terminalApi.getTerminalCheckout(checkoutId)

        console.log(`[${requestId}] Terminal checkout status:`, statusCode)

        if (statusCode === 200 && result.checkout) {
          return res.status(200).json({
            success: true,
            checkout: {
              id: result.checkout.id,
              status: result.checkout.status,
              amountMoney: result.checkout.amountMoney,
              paymentId: result.checkout.paymentId,
              deviceId: result.checkout.deviceOptions?.deviceId,
              createdAt: result.checkout.createdAt,
              updatedAt: result.checkout.updatedAt
            },
            requestId
          })
        } else {
          console.error(`[${requestId}] Failed to get checkout status:`, result)
          return res.status(404).json({
            error: 'Checkout not found',
            message: result.errors?.[0]?.detail || 'Unknown error',
            requestId
          })
        }
      } catch (error) {
        console.error(`[${requestId}] Error getting checkout status:`, error)
        return res.status(500).json({
          error: 'Failed to get checkout status',
          message: error.message,
          requestId
        })
      }

    } else {
      return res.status(405).json({ 
        error: 'Method not allowed',
        message: 'Only GET and POST methods are supported',
        requestId
      })
    }

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      requestId
    })
  }
}
