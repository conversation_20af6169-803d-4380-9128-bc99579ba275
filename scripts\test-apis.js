const { default: fetch } = await import('node-fetch');

async function testUpdatedAPIs() {
  console.log('🧪 Testing updated API endpoints...\n');
  
  try {
    // Step 1: Login first
    console.log('1. Logging in...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    });

    const loginData = await loginResponse.json();
    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginData.error);
      return;
    }
    console.log('✅ Login successful');

    // Step 2: Test Bookings API
    console.log('\n2. Testing Bookings API...');
    const bookingsResponse = await fetch('http://localhost:3001/api/admin/bookings', {
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Cookie': `admin-token=${loginData.token}`
      }
    });

    console.log(`   Status: ${bookingsResponse.status}`);
    
    if (bookingsResponse.ok) {
      const bookingsData = await bookingsResponse.json();
      console.log(`   ✅ Bookings loaded: ${bookingsData.bookings.length} items`);
      
      if (bookingsData.bookings.length > 0) {
        const booking = bookingsData.bookings[0];
        console.log(`   📋 Sample booking: ${booking.customer_name} - ${booking.service_name} (${booking.status})`);
      }
    } else {
      const error = await bookingsResponse.text();
      console.log(`   ❌ Bookings API failed: ${error}`);
    }

    // Step 3: Test Customers API
    console.log('\n3. Testing Customers API...');
    const customersResponse = await fetch('http://localhost:3001/api/admin/customers', {
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Cookie': `admin-token=${loginData.token}`
      }
    });

    console.log(`   Status: ${customersResponse.status}`);
    
    if (customersResponse.ok) {
      const customersData = await customersResponse.json();
      console.log(`   ✅ Customers loaded: ${customersData.customers.length} items`);
      
      if (customersData.customers.length > 0) {
        const customer = customersData.customers[0];
        console.log(`   👥 Sample customer: ${customer.name} (${customer.total_bookings} bookings)`);
      }
    } else {
      const error = await customersResponse.text();
      console.log(`   ❌ Customers API failed: ${error}`);
    }

    // Step 4: Test Dashboard with real data
    console.log('\n4. Testing Dashboard API (real data)...');
    const dashboardResponse = await fetch('http://localhost:3001/api/admin/dashboard', {
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Cookie': `admin-token=${loginData.token}`
      }
    });

    console.log(`   Status: ${dashboardResponse.status}`);
    
    if (dashboardResponse.ok) {
      const dashboardData = await dashboardResponse.json();
      console.log(`   ✅ Dashboard data loaded`);
      console.log(`   📊 Total bookings: ${dashboardData.stats.totalBookings}`);
      console.log(`   💰 Total revenue: $${dashboardData.stats.totalRevenue}`);
      console.log(`   👥 Active customers: ${dashboardData.stats.activeCustomers}`);
      console.log(`   📝 Recent bookings: ${dashboardData.recentBookings.length}`);
    } else {
      const error = await dashboardResponse.text();
      console.log(`   ❌ Dashboard API failed: ${error}`);
    }

    console.log('\n🎉 API testing complete!');
    console.log('\n📝 Summary:');
    console.log('   • Login: Working ✅');
    console.log('   • Bookings API: ' + (bookingsResponse.ok ? 'Working ✅' : 'Failed ❌'));
    console.log('   • Customers API: ' + (customersResponse.ok ? 'Working ✅' : 'Failed ❌'));
    console.log('   • Dashboard API: ' + (dashboardResponse.ok ? 'Working ✅' : 'Failed ❌'));
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testUpdatedAPIs();
