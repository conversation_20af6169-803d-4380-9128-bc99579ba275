import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Pie } from 'react-chartjs-2';
import styles from '../../../styles/admin/Charts.module.css';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface BookingData {
  statusBreakdown: Array<{ status: string; count: number; percentage: number }>;
  cancellationRate: number;
  daily?: Array<{ date: string; bookings: number }>;
  byArtist?: Array<{ artist: string; bookings: number }>;
}

interface BookingChartProps {
  data: BookingData;
  dateRange: string;
}

export default function BookingChart({ data, dateRange }: BookingChartProps) {
  // Prepare booking status pie chart data
  const statusChartData = {
    labels: data.statusBreakdown.map(item => item.status),
    datasets: [
      {
        label: 'Booking Status',
        data: data.statusBreakdown.map(item => item.count),
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',   // Completed - Green
          'rgba(59, 130, 246, 0.8)',  // Confirmed - Blue
          'rgba(239, 68, 68, 0.8)',   // Cancelled - Red
          'rgba(245, 158, 11, 0.8)',  // Pending - Orange
          'rgba(168, 85, 247, 0.8)',  // Rescheduled - Purple
        ],
        borderColor: [
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
          'rgb(239, 68, 68)',
          'rgb(245, 158, 11)',
          'rgb(168, 85, 247)',
        ],
        borderWidth: 2,
        hoverOffset: 10,
      },
    ],
  };

  // Prepare daily bookings bar chart data (if available)
  const dailyBookingsData = data.daily ? {
    labels: data.daily.map(item => {
      const date = new Date(item.date);
      return date.toLocaleDateString('en-AU', { 
        month: 'short', 
        day: 'numeric' 
      });
    }),
    datasets: [
      {
        label: 'Daily Bookings',
        data: data.daily.map(item => item.bookings),
        backgroundColor: 'rgba(102, 126, 234, 0.8)',
        borderColor: 'rgb(102, 126, 234)',
        borderWidth: 2,
        borderRadius: 6,
        borderSkipped: false,
      },
    ],
  } : null;

  // Chart options
  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          },
          color: '#374151',
          padding: 15,
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      title: {
        display: true,
        text: `Booking Status Distribution - ${dateRange}`,
        font: {
          family: 'Inter, sans-serif',
          size: 16,
          weight: 'bold' as const,
        },
        color: '#1f2937',
        padding: 20,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(102, 126, 234, 0.8)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            const percentage = ((context.parsed / context.dataset.data.reduce((a: number, b: number) => a + b, 0)) * 100).toFixed(1);
            return `${context.label}: ${context.parsed} bookings (${percentage}%)`;
          },
        },
      },
    },
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          },
          color: '#374151',
        },
      },
      title: {
        display: true,
        text: `Daily Booking Trends - ${dateRange}`,
        font: {
          family: 'Inter, sans-serif',
          size: 16,
          weight: 'bold' as const,
        },
        color: '#1f2937',
        padding: 20,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(102, 126, 234, 0.8)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context: any) {
            return `Bookings: ${context.parsed.y}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
          color: '#6b7280',
        },
      },
      y: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
          color: '#6b7280',
          stepSize: 1,
        },
      },
    },
  };

  return (
    <div className={styles.chartsContainer}>
      {/* Booking Status Pie Chart */}
      <div className={styles.chartCard}>
        <div className={styles.chartWrapper}>
          <Pie data={statusChartData} options={pieChartOptions} />
        </div>
      </div>

      {/* Daily Bookings Bar Chart (if data available) */}
      {dailyBookingsData && (
        <div className={styles.chartCard}>
          <div className={styles.chartWrapper}>
            <Bar data={dailyBookingsData} options={barChartOptions} />
          </div>
        </div>
      )}

      {/* Booking Statistics Summary */}
      <div className={styles.summaryTable}>
        <h3>Booking Statistics</h3>
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>
              {data.statusBreakdown.reduce((sum, item) => sum + item.count, 0)}
            </div>
            <div className={styles.statLabel}>Total Bookings</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>
              {data.statusBreakdown.find(item => item.status === 'Completed')?.count || 0}
            </div>
            <div className={styles.statLabel}>Completed</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>
              {data.cancellationRate.toFixed(1)}%
            </div>
            <div className={styles.statLabel}>Cancellation Rate</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>
              {(100 - data.cancellationRate).toFixed(1)}%
            </div>
            <div className={styles.statLabel}>Success Rate</div>
          </div>
        </div>

        {/* Status Breakdown Table */}
        <div className={styles.tableWrapper}>
          <table>
            <thead>
              <tr>
                <th>Status</th>
                <th>Count</th>
                <th>Percentage</th>
              </tr>
            </thead>
            <tbody>
              {data.statusBreakdown.map((item, index) => (
                <tr key={index}>
                  <td>
                    <span className={`${styles.statusDot} ${styles[item.status.toLowerCase()]}`}></span>
                    {item.status}
                  </td>
                  <td>{item.count}</td>
                  <td>{item.percentage.toFixed(1)}%</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
