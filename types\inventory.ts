/**
 * Ocean Soul Sparkles Admin Dashboard - Inventory Types
 * Inventory management and tracking types
 */

import { ID, Timestamp, Money } from './common';

export interface InventoryItem {
  id: ID;
  productId: ID;
  currentStock: number;
  reservedStock: number;
  availableStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  lastRestocked?: Timestamp;
  lastSold?: Timestamp;
  location?: string;
  updatedAt: Timestamp;
}

export interface StockMovement {
  id: ID;
  productId: ID;
  type: 'in' | 'out' | 'adjustment' | 'transfer';
  quantity: number;
  reason: string;
  reference?: string;
  cost?: Money;
  userId: ID;
  createdAt: Timestamp;
}

export interface PurchaseOrder {
  id: ID;
  orderNumber: string;
  supplierId?: ID;
  status: 'draft' | 'sent' | 'confirmed' | 'received' | 'cancelled';
  items: Array<{
    productId: ID;
    quantity: number;
    unitCost: Money;
    totalCost: Money;
  }>;
  totalAmount: Money;
  orderDate: Timestamp;
  expectedDate?: Timestamp;
  receivedDate?: Timestamp;
  createdBy: ID;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface InventoryAlert {
  id: ID;
  type: 'low_stock' | 'out_of_stock' | 'overstock' | 'expiring';
  productId: ID;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  isResolved: boolean;
  resolvedAt?: Timestamp;
  resolvedBy?: ID;
  createdAt: Timestamp;
}
