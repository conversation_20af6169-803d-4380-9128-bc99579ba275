import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import AdminLayout from '../../components/admin/AdminLayout';
import BookingCalendar from '../../components/admin/BookingCalendar';
import BookingAnalytics from '../../components/admin/BookingAnalytics';
import { QuickExportButton } from '../../components/admin/ExportButton';
import { useAuth } from '../../hooks/useAuth';
import { toast } from 'react-toastify';
import styles from '../../styles/admin/Bookings.module.css';

/**
 * Bookings Management Page
 * 
 * This page provides a comprehensive interface for managing customer bookings,
 * including calendar view, booking details, and status management.
 */
export default function BookingsManagement() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [bookings, setBookings] = useState([]);
  const [filteredBookings, setFilteredBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'calendar'

  // Load bookings from database
  const loadBookings = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      
      const response = await fetch('/api/admin/bookings', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load bookings');
      }

      const data = await response.json();
      setBookings(data.bookings || []);
      setFilteredBookings(data.bookings || []);
    } catch (error) {
      console.error('Error loading bookings:', error);
      // Set empty array instead of mock data
      setBookings([]);
      setFilteredBookings([]);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (!authLoading && user) {
      loadBookings();
    }
  }, [user, authLoading]);

  // Filter bookings based on search, status, and date
  useEffect(() => {
    let filtered = bookings;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(booking =>
        (booking.customer_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (booking.service_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (booking.customer_email || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(booking => booking.status === statusFilter);
    }

    // Filter by date if in list view
    if (viewMode === 'list' && selectedDate) {
      filtered = filtered.filter(booking => {
        const bookingDate = new Date(booking.start_time || booking.booking_date).toISOString().split('T')[0];
        return bookingDate === selectedDate;
      });
    }

    setFilteredBookings(filtered);
  }, [bookings, searchTerm, statusFilter, selectedDate, viewMode]);

  // Update booking status
  const updateBookingStatus = async (bookingId, newStatus) => {
    try {
      const token = localStorage.getItem('admin-token');
      
      const response = await fetch(`/api/admin/bookings/${bookingId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error('Failed to update booking status');
      }

      // Update local state
      setBookings(prevBookings =>
        prevBookings.map(booking =>
          booking.id === bookingId
            ? { ...booking, status: newStatus }
            : booking
        )
      );
    } catch (error) {
      console.error('Error updating booking status:', error);
    }
  };

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'confirmed': return styles.statusConfirmed;
      case 'pending': return styles.statusPending;
      case 'completed': return styles.statusCompleted;
      case 'cancelled': return styles.statusCancelled;
      default: return styles.statusDefault;
    }
  };

  // Format date and time
  const formatDateTime = (dateTimeString) => {
    const date = new Date(dateTimeString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };
  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading bookings...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Bookings Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage customer bookings and appointments" />
      </Head>

      <div className={styles.bookingsContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Bookings Management</h1>
          <div className={styles.headerActions}>
            <QuickExportButton
              data={filteredBookings}
              type="bookings"
              className={styles.exportBtn}
            />
            <Link href="/admin/bookings/new" className={styles.newBookingBtn}>
              + New Booking
            </Link>
            <button
              className={styles.backButton}
              onClick={() => router.push('/')}
            >
              ← Back to Dashboard
            </button>
          </div>
        </header>

        {/* Analytics Section */}
        <BookingAnalytics bookings={bookings} />

        <div className={styles.controlsPanel}>
          <div className={styles.viewToggle}>
            <button
              className={`${styles.viewBtn} ${viewMode === 'list' ? styles.active : ''}`}
              onClick={() => setViewMode('list')}
            >
              List View
            </button>
            <button
              className={`${styles.viewBtn} ${viewMode === 'calendar' ? styles.active : ''}`}
              onClick={() => setViewMode('calendar')}
            >
              Calendar View
            </button>
          </div>

          <div className={styles.filters}>
            <input
              type="text"
              placeholder="Search bookings..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={styles.statusFilter}
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>

            {viewMode === 'list' && (
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className={styles.dateFilter}
              />
            )}
          </div>
        </div>

        <div className={styles.bookingsContent}>
          {viewMode === 'list' ? (
            <div className={styles.bookingsList}>
              {filteredBookings.length === 0 ? (
                <div className={styles.emptyState}>
                  <p>No bookings found for the selected criteria.</p>
                </div>
              ) : (
                filteredBookings.map(booking => {
                  const { date, time } = formatDateTime(booking.start_time);
                  const endTime = formatDateTime(booking.end_time).time;
                  
                  return (
                    <div key={booking.id} className={styles.bookingCard}>
                      <div className={styles.bookingHeader}>
                        <div className={styles.customerInfo}>
                          <h3>{booking.customer_name}</h3>
                          <p>{booking.customer_email}</p>
                        </div>
                        <div className={`${styles.statusBadge} ${getStatusBadgeClass(booking.status)}`}>
                          {booking.status}
                        </div>
                      </div>

                      <div className={styles.bookingDetails}>
                        <div className={styles.serviceInfo}>
                          <strong>{booking.service_name}</strong>
                          <p>Artist: {booking.artist}</p>
                          <p>Price: ${booking.price}</p>
                        </div>

                        <div className={styles.timeInfo}>
                          <p><strong>{date}</strong></p>
                          <p>{time} - {endTime}</p>
                        </div>
                      </div>

                      {booking.notes && (
                        <div className={styles.bookingNotes}>
                          <strong>Notes:</strong> {booking.notes}
                        </div>
                      )}

                      <div className={styles.bookingActions}>
                        <Link href={`/admin/bookings/${booking.id}`} className={styles.viewBtn}>
                          View Details
                        </Link>
                        <select
                          value={booking.status}
                          onChange={(e) => updateBookingStatus(booking.id, e.target.value)}
                          className={styles.statusSelect}
                        >
                          <option value="pending">Pending</option>
                          <option value="confirmed">Confirmed</option>
                          <option value="completed">Completed</option>
                          <option value="cancelled">Cancelled</option>
                        </select>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          ) : (
            <div className={styles.calendarView}>
              <BookingCalendar
                bookings={bookings}
                onBookingClick={(booking) => {
                  router.push(`/admin/bookings/${booking.id}`);
                }}
                onDateClick={(date) => {
                  const dateString = date.toISOString().split('T')[0];
                  setSelectedDate(dateString);
                  setViewMode('list');
                  toast.info(`Switched to list view for ${date.toLocaleDateString()}`);
                }}
              />
            </div>
          )}        </div>
      </div>
    </AdminLayout>
  );
}
