/* Ocean Soul Sparkles Admin Portal - Global Styles */

/* CSS Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: #2c3e50;
  line-height: 1.6;
}

/* Admin Portal Color Scheme */
:root {
  /* Primary Colors */
  --admin-primary: #3788d8;
  --admin-primary-dark: #2c6cb7;
  --admin-primary-light: #5ba0e3;
  
  /* Secondary Colors */
  --admin-secondary: #6c757d;
  --admin-accent: #4ECDC4;
  
  /* Status Colors */
  --admin-success: #28a745;
  --admin-warning: #ffc107;
  --admin-danger: #dc3545;
  --admin-info: #17a2b8;
  
  /* Neutral Colors */
  --admin-white: #ffffff;
  --admin-light: #f8f9fa;
  --admin-lighter: #e9ecef;
  --admin-gray: #6c757d;
  --admin-dark: #343a40;
  --admin-darker: #2c3e50;
  
  /* Background Colors */
  --admin-bg-primary: #ffffff;
  --admin-bg-secondary: #f8f9fa;
  --admin-bg-tertiary: #e9ecef;
  
  /* Border Colors */
  --admin-border-light: #e9ecef;
  --admin-border-medium: #dee2e6;
  --admin-border-dark: #adb5bd;
  
  /* Shadow Colors */
  --admin-shadow-light: rgba(0, 0, 0, 0.1);
  --admin-shadow-medium: rgba(0, 0, 0, 0.15);
  --admin-shadow-dark: rgba(0, 0, 0, 0.25);
  
  /* Spacing */
  --admin-spacing-xs: 4px;
  --admin-spacing-sm: 8px;
  --admin-spacing-md: 16px;
  --admin-spacing-lg: 24px;
  --admin-spacing-xl: 32px;
  --admin-spacing-xxl: 48px;
  
  /* Border Radius */
  --admin-radius-sm: 4px;
  --admin-radius-md: 8px;
  --admin-radius-lg: 12px;
  --admin-radius-xl: 16px;
  
  /* Transitions */
  --admin-transition-fast: 0.15s ease;
  --admin-transition-normal: 0.2s ease;
  --admin-transition-slow: 0.3s ease;
  
  /* Z-Index Scale */
  --admin-z-dropdown: 1000;
  --admin-z-sticky: 1020;
  --admin-z-fixed: 1030;
  --admin-z-modal-backdrop: 1040;
  --admin-z-modal: 1050;
  --admin-z-popover: 1060;
  --admin-z-tooltip: 1070;
  --admin-z-toast: 1080;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
  color: var(--admin-darker);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

a {
  color: var(--admin-primary);
  text-decoration: none;
  transition: color var(--admin-transition-normal);
}

a:hover {
  color: var(--admin-primary-dark);
  text-decoration: underline;
}

/* Form Elements */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  font: inherit;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

.text-primary { color: var(--admin-primary); }
.text-secondary { color: var(--admin-secondary); }
.text-success { color: var(--admin-success); }
.text-warning { color: var(--admin-warning); }
.text-danger { color: var(--admin-danger); }
.text-info { color: var(--admin-info); }

.bg-primary { background-color: var(--admin-primary); }
.bg-secondary { background-color: var(--admin-bg-secondary); }
.bg-white { background-color: var(--admin-white); }

.border { border: 1px solid var(--admin-border-light); }
.border-top { border-top: 1px solid var(--admin-border-light); }
.border-bottom { border-bottom: 1px solid var(--admin-border-light); }

.rounded { border-radius: var(--admin-radius-md); }
.rounded-lg { border-radius: var(--admin-radius-lg); }
.rounded-xl { border-radius: var(--admin-radius-xl); }

.shadow { box-shadow: 0 2px 4px var(--admin-shadow-light); }
.shadow-md { box-shadow: 0 4px 8px var(--admin-shadow-light); }
.shadow-lg { box-shadow: 0 8px 16px var(--admin-shadow-medium); }

.transition { transition: all var(--admin-transition-normal); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.flex-1 { flex: 1; }
.flex-wrap { flex-wrap: wrap; }

/* Grid Utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.gap-1 { gap: var(--admin-spacing-xs); }
.gap-2 { gap: var(--admin-spacing-sm); }
.gap-4 { gap: var(--admin-spacing-md); }
.gap-6 { gap: var(--admin-spacing-lg); }
.gap-8 { gap: var(--admin-spacing-xl); }

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--admin-spacing-xs); }
.m-2 { margin: var(--admin-spacing-sm); }
.m-4 { margin: var(--admin-spacing-md); }
.m-6 { margin: var(--admin-spacing-lg); }
.m-8 { margin: var(--admin-spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--admin-spacing-xs); }
.p-2 { padding: var(--admin-spacing-sm); }
.p-4 { padding: var(--admin-spacing-md); }
.p-6 { padding: var(--admin-spacing-lg); }
.p-8 { padding: var(--admin-spacing-xl); }

/* Width and Height Utilities */
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* Position Utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Overflow Utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* Admin-specific Components */
.admin-card {
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-lg);
  box-shadow: 0 2px 4px var(--admin-shadow-light);
  transition: box-shadow var(--admin-transition-normal);
}

.admin-card:hover {
  box-shadow: 0 4px 8px var(--admin-shadow-medium);
}

.admin-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-spacing-sm);
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  background: var(--admin-primary);
  color: var(--admin-white);
  border: none;
  border-radius: var(--admin-radius-md);
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  text-decoration: none;
}

.admin-button:hover:not(:disabled) {
  background: var(--admin-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(55, 136, 216, 0.3);
}

.admin-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.admin-button.secondary {
  background: var(--admin-secondary);
  color: var(--admin-white);
}

.admin-button.secondary:hover:not(:disabled) {
  background: var(--admin-dark);
}

.admin-button.outline {
  background: transparent;
  color: var(--admin-primary);
  border: 2px solid var(--admin-primary);
}

.admin-button.outline:hover:not(:disabled) {
  background: var(--admin-primary);
  color: var(--admin-white);
}

/* Loading Spinner */
.admin-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
  padding: var(--admin-spacing-xs) var(--admin-spacing-sm);
  border-radius: var(--admin-radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator.success {
  background: rgba(40, 167, 69, 0.1);
  color: var(--admin-success);
}

.status-indicator.warning {
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
}

.status-indicator.danger {
  background: rgba(220, 53, 69, 0.1);
  color: var(--admin-danger);
}

.status-indicator.info {
  background: rgba(23, 162, 184, 0.1);
  color: var(--admin-info);
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    --admin-spacing-xs: 2px;
    --admin-spacing-sm: 4px;
    --admin-spacing-md: 8px;
    --admin-spacing-lg: 16px;
    --admin-spacing-xl: 24px;
    --admin-spacing-xxl: 32px;
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }
  h5 { font-size: 1.1rem; }
  h6 { font-size: 1rem; }

  .admin-card {
    padding: var(--admin-spacing-md);
  }
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  .admin-button,
  .admin-spinner,
  .status-indicator {
    display: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --admin-border-light: #000000;
    --admin-border-medium: #000000;
    --admin-shadow-light: rgba(0, 0, 0, 0.5);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
