/* Charts Component Styles */

.chartsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.chartCard {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.chartCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chartWrapper {
  position: relative;
  height: 400px;
  width: 100%;
}

.summaryTable {
  grid-column: 1 / -1;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.summaryTable h3 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  font-family: 'Inter', sans-serif;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
  font-family: 'Inter', sans-serif;
}

.statLabel {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tableWrapper {
  overflow-x: auto;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.tableWrapper table {
  width: 100%;
  border-collapse: collapse;
  font-family: 'Inter', sans-serif;
}

.tableWrapper th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tableWrapper th:first-child {
  border-top-left-radius: 12px;
}

.tableWrapper th:last-child {
  border-top-right-radius: 12px;
}

.tableWrapper td {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  color: #374151;
  font-size: 0.875rem;
}

.tableWrapper tr:last-child td {
  border-bottom: none;
}

.tableWrapper tr:nth-child(even) {
  background-color: #f8fafc;
}

.tableWrapper tr:hover {
  background-color: #f1f5f9;
}

.statusDot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.statusDot.completed {
  background-color: #22c55e;
}

.statusDot.confirmed {
  background-color: #3b82f6;
}

.statusDot.cancelled {
  background-color: #ef4444;
}

.statusDot.pending {
  background-color: #f59e0b;
}

.statusDot.rescheduled {
  background-color: #a855f7;
}

/* Export Controls */
.exportControls {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: flex-end;
}

.exportBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.exportBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.exportBtn:active {
  transform: translateY(0);
}

.exportBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading States */
.chartLoading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #64748b;
  font-size: 1rem;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.chartError {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #ef4444;
  font-size: 1rem;
  text-align: center;
  flex-direction: column;
  gap: 1rem;
}

.errorIcon {
  font-size: 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .chartsContainer {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .chartWrapper {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .chartsContainer {
    gap: 1rem;
  }
  
  .chartCard {
    padding: 1rem;
  }
  
  .chartWrapper {
    height: 300px;
  }
  
  .summaryTable {
    padding: 1.5rem;
  }
  
  .statsGrid {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }
  
  .statCard {
    padding: 1rem;
  }
  
  .statValue {
    font-size: 1.5rem;
  }
  
  .exportControls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .exportBtn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .tableWrapper {
    font-size: 0.75rem;
  }
  
  .tableWrapper th,
  .tableWrapper td {
    padding: 0.75rem 0.5rem;
  }
}
