/**
 * Ocean Soul Sparkles Admin Dashboard - Staff Types
 * Staff and artist management types
 */

import { ID, Timestamp, UserRole, Status, Money, MediaFile, ContactInfo, BusinessHours } from './common';

export interface StaffMember {
  id: ID;
  userId?: ID; // Link to user account
  employeeId: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  email: string;
  phone?: string;
  mobile?: string;
  
  // Role and permissions
  role: UserRole;
  department: string;
  position: string;
  permissions: string[];
  
  // Employment details
  employmentType: 'full_time' | 'part_time' | 'contractor' | 'casual';
  startDate: string;
  endDate?: string;
  status: Status;
  
  // Contact and personal
  contactInfo: ContactInfo;
  avatar?: MediaFile;
  dateOfBirth?: string;
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
  
  // Professional details
  skills: string[];
  certifications: Array<{
    name: string;
    issuer: string;
    issueDate: string;
    expiryDate?: string;
    certificateUrl?: string;
  }>;
  experienceLevel: 'junior' | 'senior' | 'master';
  specialties: string[];
  
  // Availability and scheduling
  availability: BusinessHours;
  maxBookingsPerDay?: number;
  maxBookingsPerWeek?: number;
  
  // Financial
  hourlyRate?: Money;
  commissionRate?: number; // Percentage
  salary?: Money;
  
  // Performance metrics
  totalBookings: number;
  totalRevenue: Money;
  averageRating?: number;
  customerRetentionRate?: number;
  
  // System fields
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy?: ID;
  updatedBy?: ID;
}

export interface ArtistProfile extends StaffMember {
  // Artist-specific fields
  artistName: string;
  bio?: string;
  portfolio?: MediaFile[];
  socialLinks?: {
    instagram?: string;
    facebook?: string;
    website?: string;
  };
  
  // Booking preferences
  bookingBuffer: number; // Minutes between bookings
  acceptsWalkIns: boolean;
  acceptsMobileBookings: boolean;
  
  // Performance tracking
  portfolioViews: number;
  profileViews: number;
  bookingConversionRate: number;
}

export interface CreateStaffData {
  firstName: string;
  lastName: string;
  email: string;
  role: UserRole;
  department: string;
  position: string;
  employmentType: StaffMember['employmentType'];
  startDate: string;
  skills?: string[];
  hourlyRate?: Money;
  commissionRate?: number;
}

export interface UpdateStaffData extends Partial<CreateStaffData> {
  id: ID;
  status?: Status;
}

export interface StaffSearchParams {
  query?: string;
  role?: UserRole[];
  department?: string[];
  status?: Status[];
  employmentType?: StaffMember['employmentType'][];
  skills?: string[];
  experienceLevel?: StaffMember['experienceLevel'][];
}
