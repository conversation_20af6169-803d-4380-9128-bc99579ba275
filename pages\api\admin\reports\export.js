import { verifyAdminToken } from '../../../../lib/auth/admin-auth';
import { generatePDFReport } from '../../../../lib/export/pdf-generator';
import { generateExcelReport } from '../../../../lib/export/excel-generator';
import { generateCSVReport } from '../../../../lib/export/csv-generator';
import { supabaseAdmin } from '../../../../lib/supabase-admin';

/**
 * Reports Export API Endpoint
 * 
 * Handles exporting reports in various formats (PDF, Excel, CSV)
 * Supports different date ranges and report types
 */
export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `export-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    console.log(`[${requestId}] Export request started:`, {
      method: req.method,
      query: req.query,
      userAgent: req.headers['user-agent']
    });

    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ 
        error: 'Method not allowed',
        requestId 
      });
    }

    // Verify admin authentication
    const authResult = await verifyAdminToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ 
        error: 'Unauthorized',
        requestId 
      });
    }

    // Check admin permissions
    if (!['DEV', 'Admin'].includes(authResult.user.role)) {
      console.log(`[${requestId}] Insufficient permissions:`, authResult.user.role);
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        requestId 
      });
    }

    // Extract query parameters
    const { format = 'pdf', range = 'last30days', type = 'all' } = req.query;

    // Validate format
    if (!['pdf', 'excel', 'csv'].includes(format)) {
      return res.status(400).json({ 
        error: 'Invalid format. Supported formats: pdf, excel, csv',
        requestId 
      });
    }

    // Calculate date range
    const dateRange = getDateRange(range);
    
    // Fetch report data
    const reportData = await fetchReportData(dateRange, type, requestId);
    
    // Generate export based on format
    let exportData;
    let contentType;
    let fileExtension;

    switch (format) {
      case 'pdf':
        exportData = await generatePDFReport(reportData, dateRange, range);
        contentType = 'application/pdf';
        fileExtension = 'pdf';
        break;
      case 'excel':
        exportData = await generateExcelReport(reportData, dateRange, range);
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        fileExtension = 'xlsx';
        break;
      case 'csv':
        exportData = await generateCSVReport(reportData, dateRange, range);
        contentType = 'text/csv';
        fileExtension = 'csv';
        break;
    }

    // Set response headers
    const filename = `ocean-soul-sparkles-report-${range}-${Date.now()}.${fileExtension}`;
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    console.log(`[${requestId}] Export completed successfully:`, {
      format,
      range,
      filename,
      dataSize: exportData.length
    });

    // Send the file
    return res.send(exportData);

  } catch (error) {
    console.error(`[${requestId}] Export error:`, error);
    return res.status(500).json({ 
      error: 'Export failed',
      message: error.message,
      requestId 
    });
  }
}

/**
 * Calculate date range based on range parameter
 */
function getDateRange(range) {
  const now = new Date();
  let start, end = now;

  switch (range) {
    case 'last7days':
      start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'last30days':
      start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case 'last90days':
      start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case 'thisyear':
      start = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  return {
    start: start.toISOString(),
    end: end.toISOString(),
    label: range
  };
}

/**
 * Fetch comprehensive report data for export
 */
async function fetchReportData(dateRange, type, requestId) {
  try {
    const reportData = {
      overview: {},
      revenue: {},
      bookings: {},
      customers: {},
      metadata: {
        generatedAt: new Date().toISOString(),
        dateRange: dateRange,
        requestId: requestId
      }
    };

    // Fetch bookings data
    const { data: bookings, error: bookingsError } = await supabaseAdmin
      .from('bookings')
      .select(`
        id,
        total_amount,
        status,
        created_at,
        customer_id,
        services (name),
        artists (first_name, last_name)
      `)
      .gte('created_at', dateRange.start)
      .lte('created_at', dateRange.end);

    if (bookingsError) {
      console.warn(`[${requestId}] Bookings query error:`, bookingsError);
    }

    // Fetch customers data
    const { data: customers, error: customersError } = await supabaseAdmin
      .from('customers')
      .select('id, first_name, last_name, email, created_at')
      .gte('created_at', dateRange.start)
      .lte('created_at', dateRange.end);

    if (customersError) {
      console.warn(`[${requestId}] Customers query error:`, customersError);
    }

    // Process overview data
    const totalBookings = bookings?.length || 0;
    const totalRevenue = bookings?.reduce((sum, booking) => sum + (booking.total_amount || 0), 0) || 0;
    const totalCustomers = customers?.length || 0;
    const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0;

    reportData.overview = {
      totalRevenue,
      totalBookings,
      totalCustomers,
      averageBookingValue,
      revenueGrowth: 12.5, // Would need previous period data
      bookingGrowth: 8.3   // Would need previous period data
    };

    // Process revenue data
    const revenueByService = {};
    const dailyRevenue = {};

    bookings?.forEach(booking => {
      if (booking.status === 'completed') {
        const amount = booking.total_amount || 0;
        const service = booking.services?.name || 'Unknown Service';
        const date = booking.created_at.split('T')[0];

        // Service revenue
        if (revenueByService[service]) {
          revenueByService[service] += amount;
        } else {
          revenueByService[service] = amount;
        }

        // Daily revenue
        if (dailyRevenue[date]) {
          dailyRevenue[date] += amount;
        } else {
          dailyRevenue[date] = amount;
        }
      }
    });

    const totalServiceRevenue = Object.values(revenueByService).reduce((sum, amount) => sum + amount, 0);
    
    reportData.revenue = {
      daily: Object.entries(dailyRevenue).map(([date, amount]) => ({ date, amount })),
      byService: Object.entries(revenueByService).map(([service, amount]) => ({
        service,
        amount,
        percentage: totalServiceRevenue > 0 ? (amount / totalServiceRevenue) * 100 : 0
      }))
    };

    // Process bookings data
    const statusBreakdown = {};
    bookings?.forEach(booking => {
      const status = booking.status || 'Unknown';
      if (statusBreakdown[status]) {
        statusBreakdown[status]++;
      } else {
        statusBreakdown[status] = 1;
      }
    });

    const cancelledCount = statusBreakdown['cancelled'] || 0;
    const cancellationRate = totalBookings > 0 ? (cancelledCount / totalBookings) * 100 : 0;

    reportData.bookings = {
      statusBreakdown: Object.entries(statusBreakdown).map(([status, count]) => ({
        status,
        count,
        percentage: totalBookings > 0 ? (count / totalBookings) * 100 : 0
      })),
      cancellationRate
    };

    // Process customers data
    reportData.customers = {
      newCustomers: totalCustomers,
      returningCustomers: Math.floor(totalCustomers * 0.6), // Simplified calculation
      customerLifetimeValue: averageBookingValue * 1.5 // Simplified calculation
    };

    return reportData;

  } catch (error) {
    console.error(`[${requestId}] Error fetching report data:`, error);
    throw error;
  }
}
