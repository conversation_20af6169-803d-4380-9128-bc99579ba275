/**
 * Ocean Soul Sparkles Admin Dashboard - Mock Server
 * MSW (Mock Service Worker) setup for API testing
 */

import { setupServer } from 'msw/node';
import { rest } from 'msw';
import { 
  createMockCustomer, 
  createMockBooking, 
  createMockService, 
  createMockStaffMember,
  createMockApiResponse,
  createMockPaginatedResponse
} from '../utils/test-helpers';

// Mock data
const mockCustomers = [
  createMockCustomer({ id: 'cust_1', firstName: '<PERSON>', lastName: 'Doe' }),
  createMockCustomer({ id: 'cust_2', firstName: '<PERSON>', lastName: '<PERSON>' }),
  createMockCustomer({ id: 'cust_3', firstName: 'Bob', lastName: '<PERSON>' }),
];

const mockBookings = [
  createMockBooking({ id: 'book_1', customerId: 'cust_1' }),
  createMockBooking({ id: 'book_2', customerId: 'cust_2' }),
  createMockBooking({ id: 'book_3', customerId: 'cust_1' }),
];

const mockServices = [
  createMockService({ id: 'serv_1', name: 'Box Braids' }),
  createMockService({ id: 'serv_2', name: 'Cornrows' }),
  createMockService({ id: 'serv_3', name: 'Twists' }),
];

const mockStaff = [
  createMockStaffMember({ id: 'staff_1', firstName: 'Alice', lastName: 'Wilson' }),
  createMockStaffMember({ id: 'staff_2', firstName: 'Bob', lastName: 'Brown' }),
];

// Request handlers
export const handlers = [
  // Authentication endpoints
  rest.post('/api/auth/login', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        user: {
          id: 'user_123',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'User',
          role: 'Admin',
          permissions: ['admin:all'],
          lastLoginAt: '2024-06-16T10:00:00Z'
        },
        token: 'mock-jwt-token',
        expiresAt: '2024-06-17T10:00:00Z'
      }))
    );
  }),

  rest.post('/api/auth/logout', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({ success: true }))
    );
  }),

  // Customer endpoints
  rest.get('/api/admin/customers', (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1;
    const limit = Number(req.url.searchParams.get('limit')) || 10;
    const search = req.url.searchParams.get('search');
    
    let filteredCustomers = mockCustomers;
    
    if (search) {
      filteredCustomers = mockCustomers.filter(customer =>
        customer.firstName.toLowerCase().includes(search.toLowerCase()) ||
        customer.lastName.toLowerCase().includes(search.toLowerCase()) ||
        customer.email.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCustomers = filteredCustomers.slice(startIndex, endIndex);
    
    return res(
      ctx.status(200),
      ctx.json(createMockPaginatedResponse(paginatedCustomers, page, limit, filteredCustomers.length))
    );
  }),

  rest.get('/api/admin/customers/:id', (req, res, ctx) => {
    const { id } = req.params;
    const customer = mockCustomers.find(c => c.id === id);
    
    if (!customer) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Customer not found'
          }
        })
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        customer,
        bookings: mockBookings.filter(b => b.customerId === id),
        totalSpent: customer.totalSpent.amount,
        lastVisit: '2024-06-15T14:30:00Z'
      }))
    );
  }),

  rest.post('/api/admin/customers', (req, res, ctx) => {
    const newCustomer = createMockCustomer({
      id: `cust_${Date.now()}`,
      ...(req.body as any)
    });
    
    return res(
      ctx.status(201),
      ctx.json(createMockApiResponse({ customer: newCustomer }))
    );
  }),

  rest.put('/api/admin/customers/:id', (req, res, ctx) => {
    const { id } = req.params;
    const customer = mockCustomers.find(c => c.id === id);
    
    if (!customer) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Customer not found'
          }
        })
      );
    }
    
    const updatedCustomer = { ...customer, ...(req.body as any), updatedAt: new Date().toISOString() };
    
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({ customer: updatedCustomer }))
    );
  }),

  // Booking endpoints
  rest.get('/api/admin/bookings', (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1;
    const limit = Number(req.url.searchParams.get('limit')) || 10;
    const status = req.url.searchParams.get('status');
    
    let filteredBookings = mockBookings;
    
    if (status && status !== 'all') {
      filteredBookings = mockBookings.filter(booking => booking.status === status);
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedBookings = filteredBookings.slice(startIndex, endIndex);
    
    return res(
      ctx.status(200),
      ctx.json(createMockPaginatedResponse(paginatedBookings, page, limit, filteredBookings.length))
    );
  }),

  rest.get('/api/admin/bookings/:id', (req, res, ctx) => {
    const { id } = req.params;
    const booking = mockBookings.find(b => b.id === id);
    
    if (!booking) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Booking not found'
          }
        })
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        booking,
        customer: mockCustomers.find(c => c.id === booking.customerId),
        service: mockServices.find(s => s.id === booking.serviceId),
        artist: mockStaff.find(s => s.id === booking.artistId)
      }))
    );
  }),

  rest.post('/api/admin/bookings', (req, res, ctx) => {
    const newBooking = createMockBooking({
      id: `book_${Date.now()}`,
      ...(req.body as any)
    });
    
    return res(
      ctx.status(201),
      ctx.json(createMockApiResponse({ booking: newBooking }))
    );
  }),

  // Service endpoints
  rest.get('/api/admin/services', (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1;
    const limit = Number(req.url.searchParams.get('limit')) || 10;
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedServices = mockServices.slice(startIndex, endIndex);
    
    return res(
      ctx.status(200),
      ctx.json(createMockPaginatedResponse(paginatedServices, page, limit, mockServices.length))
    );
  }),

  rest.get('/api/admin/services/:id', (req, res, ctx) => {
    const { id } = req.params;
    const service = mockServices.find(s => s.id === id);
    
    if (!service) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Service not found'
          }
        })
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        service,
        tiers: [],
        addons: [],
        reviews: []
      }))
    );
  }),

  // Staff endpoints
  rest.get('/api/admin/staff', (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1;
    const limit = Number(req.url.searchParams.get('limit')) || 10;
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedStaff = mockStaff.slice(startIndex, endIndex);
    
    return res(
      ctx.status(200),
      ctx.json(createMockPaginatedResponse(paginatedStaff, page, limit, mockStaff.length))
    );
  }),

  // Dashboard endpoint
  rest.get('/api/admin/dashboard', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        stats: {
          totalBookings: 150,
          totalRevenue: 18750,
          activeCustomers: 45,
          pendingBookings: 12,
          completedBookings: 120,
          cancelledBookings: 18,
          averageBookingValue: 125,
          monthlyGrowth: 15.5
        },
        recentBookings: mockBookings.slice(0, 5),
        recentActivity: [
          {
            id: 'act_1',
            type: 'booking_created',
            description: 'New booking created by John Doe',
            timestamp: '2024-06-16T09:30:00Z',
            userId: 'user_123',
            userName: 'Admin User'
          }
        ]
      }))
    );
  }),

  // Search endpoint
  rest.get('/api/admin/search', (req, res, ctx) => {
    const query = req.url.searchParams.get('q') || '';
    const types = req.url.searchParams.get('types')?.split(',') || ['customer', 'booking', 'service'];
    
    const results = [];
    
    if (types.includes('customer')) {
      const matchingCustomers = mockCustomers.filter(customer =>
        customer.firstName.toLowerCase().includes(query.toLowerCase()) ||
        customer.lastName.toLowerCase().includes(query.toLowerCase()) ||
        customer.email.toLowerCase().includes(query.toLowerCase())
      );
      
      results.push(...matchingCustomers.map(customer => ({
        id: customer.id,
        type: 'customer' as const,
        title: `${customer.firstName} ${customer.lastName}`,
        subtitle: customer.email,
        url: `/admin/customers/${customer.id}`
      })));
    }
    
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        results,
        total: results.length,
        query
      }))
    );
  }),

  // Error simulation endpoints
  rest.get('/api/admin/error/500', (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      })
    );
  }),

  rest.get('/api/admin/error/401', (req, res, ctx) => {
    return res(
      ctx.status(401),
      ctx.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
    );
  }),
];

// Create and export the server
export const server = setupServer(...handlers);
