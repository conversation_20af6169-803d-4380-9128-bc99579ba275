import { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import styles from '@/styles/admin/ServiceForm.module.css';

export default function NewService() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    duration: '',
    price: '',
    category: 'Hair Braiding',
    status: 'active',
    visible_on_public: true,
    visible_on_pos: true,
    visible_on_events: true
  });
  const [errors, setErrors] = useState({});

  const categories = [
    'Hair Braiding',
    'Protective Styles',
    'Hair Care',
    'Styling',
    'Consultation',
    'Special Events',
    'Maintenance'
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (formData.duration && (isNaN(formData.duration) || parseInt(formData.duration) <= 0)) {
      newErrors.duration = 'Duration must be a positive number';
    }

    if (formData.price && (isNaN(formData.price) || parseFloat(formData.price) < 0)) {
      newErrors.price = 'Price must be a valid number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      
      const response = await fetch('/api/admin/services', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create service');
      }

      const data = await response.json();
      router.push(`/admin/services/${data.service.id}`);
    } catch (error) {
      console.error('Error creating service:', error);
      setErrors({ submit: error.message });
    } finally {
      setLoading(false);
    }
  };

  if (authLoading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <AdminLayout>
      <Head>
        <title>Add New Service | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Create a new service offering" />
      </Head>

      <div className={styles.serviceFormContainer}>
        <header className={styles.header}>
          <div className={styles.breadcrumb}>
            <Link href="/admin/services">Services</Link>
            <span>/</span>
            <span>New Service</span>
          </div>
          
          <Link href="/admin/services" className={styles.backButton}>
            ← Back to Services
          </Link>
        </header>

        <div className={styles.formContent}>
          <h1>Add New Service</h1>
          
          <form onSubmit={handleSubmit} className={styles.form}>
            {errors.submit && (
              <div className={styles.errorAlert}>
                {errors.submit}
              </div>
            )}

            <div className={styles.formGrid}>
              <div className={styles.formGroup}>
                <label htmlFor="name">Service Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={errors.name ? styles.inputError : ''}
                  placeholder="Enter service name"
                  required
                />
                {errors.name && <span className={styles.errorText}>{errors.name}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="category">Category *</label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className={errors.category ? styles.inputError : ''}
                  required
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
                {errors.category && <span className={styles.errorText}>{errors.category}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="duration">Duration (minutes)</label>
                <input
                  type="number"
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className={errors.duration ? styles.inputError : ''}
                  placeholder="e.g., 120"
                  min="1"
                />
                {errors.duration && <span className={styles.errorText}>{errors.duration}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="price">Price (AUD)</label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className={errors.price ? styles.inputError : ''}
                  placeholder="e.g., 150.00"
                  min="0"
                  step="0.01"
                />
                {errors.price && <span className={styles.errorText}>{errors.price}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="status">Status</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="draft">Draft</option>
                </select>
              </div>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows="4"
                placeholder="Describe the service, what's included, any special requirements..."
              />
            </div>

            <div className={styles.visibilitySection}>
              <h3>Visibility Options</h3>
              <div className={styles.checkboxGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="visible_on_public"
                    checked={formData.visible_on_public}
                    onChange={handleInputChange}
                  />
                  <span>Show on public website</span>
                </label>

                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="visible_on_pos"
                    checked={formData.visible_on_pos}
                    onChange={handleInputChange}
                  />
                  <span>Available in POS system</span>
                </label>

                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="visible_on_events"
                    checked={formData.visible_on_events}
                    onChange={handleInputChange}
                  />
                  <span>Available for events</span>
                </label>
              </div>
            </div>

            <div className={styles.formActions}>
              <button
                type="submit"
                className={styles.submitButton}
                disabled={loading}
              >
                {loading ? 'Creating...' : 'Create Service'}
              </button>
              
              <Link href="/admin/services" className={styles.cancelButton}>
                Cancel
              </Link>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
