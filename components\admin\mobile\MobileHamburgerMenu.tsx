/**
 * Ocean Soul Sparkles Admin - Mobile Hamburger Menu Component
 * Slide-out mobile menu for comprehensive navigation on mobile devices
 */

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { HapticFeedback } from '../../../lib/gestures/swipe-handler';
import styles from '../../../styles/admin/mobile/MobileHamburgerMenu.module.css';

interface MobileHamburgerMenuProps {
  isOpen: boolean;
  onClose: () => void;
  userRole: string;
  userName: string;
}

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  href: string;
  roles: string[];
  badge?: string;
}

interface MenuSection {
  title: string;
  items: MenuItem[];
}

export default function MobileHamburgerMenu({ 
  isOpen, 
  onClose, 
  userRole, 
  userName 
}: MobileHamburgerMenuProps) {
  const router = useRouter();

  // Close menu on route change
  useEffect(() => {
    const handleRouteChange = () => {
      onClose();
    };

    router.events.on('routeChangeStart', handleRouteChange);
    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [router.events, onClose]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  const menuSections: MenuSection[] = [
    {
      title: 'Main',
      items: [
        {
          id: 'dashboard',
          label: 'Dashboard',
          icon: '📊',
          href: '/admin/dashboard',
          roles: ['DEV', 'Admin', 'Artist', 'Braider']
        },
        {
          id: 'bookings',
          label: 'Bookings',
          icon: '📅',
          href: '/admin/bookings',
          roles: ['DEV', 'Admin', 'Artist', 'Braider']
        },
        {
          id: 'customers',
          label: 'Customers',
          icon: '👥',
          href: '/admin/customers',
          roles: ['DEV', 'Admin', 'Artist', 'Braider']
        }
      ]
    },
    {
      title: 'Business',
      items: [
        {
          id: 'services',
          label: 'Services',
          icon: '✨',
          href: '/admin/services',
          roles: ['DEV', 'Admin']
        },
        {
          id: 'products',
          label: 'Products',
          icon: '🛍️',
          href: '/admin/products',
          roles: ['DEV', 'Admin']
        },
        {
          id: 'inventory',
          label: 'Inventory',
          icon: '📦',
          href: '/admin/inventory',
          roles: ['DEV', 'Admin']
        },
        {
          id: 'pos',
          label: 'POS Terminal',
          icon: '💳',
          href: '/admin/pos',
          roles: ['DEV', 'Admin', 'Artist', 'Braider']
        }
      ]
    },
    {
      title: 'Staff',
      items: [
        {
          id: 'staff',
          label: 'Staff Management',
          icon: '👨‍💼',
          href: '/admin/staff',
          roles: ['DEV', 'Admin']
        },
        {
          id: 'portfolio',
          label: 'Portfolio',
          icon: '🎨',
          href: '/admin/portfolio',
          roles: ['DEV', 'Admin', 'Artist', 'Braider']
        },
        {
          id: 'schedule',
          label: 'Schedule',
          icon: '🗓️',
          href: '/admin/schedule',
          roles: ['DEV', 'Admin', 'Artist', 'Braider']
        },
        {
          id: 'commissions',
          label: 'Commissions',
          icon: '💰',
          href: '/admin/commissions',
          roles: ['DEV', 'Admin', 'Artist', 'Braider']
        }
      ]
    },
    {
      title: 'System',
      items: [
        {
          id: 'communications',
          label: 'Communications',
          icon: '📱',
          href: '/admin/communications',
          roles: ['DEV', 'Admin']
        },
        {
          id: 'analytics',
          label: 'Analytics',
          icon: '📈',
          href: '/admin/analytics',
          roles: ['DEV', 'Admin']
        },
        {
          id: 'settings',
          label: 'Settings',
          icon: '⚙️',
          href: '/admin/settings',
          roles: ['DEV', 'Admin']
        }
      ]
    }
  ];

  const handleItemClick = (item: MenuItem) => {
    HapticFeedback.light();
    
    // Don't navigate if already on the page
    if (router.pathname === item.href) {
      onClose();
      return;
    }
  };

  const isActive = (href: string): boolean => {
    if (href === '/admin/dashboard') {
      return router.pathname === '/admin/dashboard' || router.pathname === '/admin';
    }
    return router.pathname.startsWith(href);
  };

  const filteredSections = menuSections.map(section => ({
    ...section,
    items: section.items.filter(item => item.roles.includes(userRole))
  })).filter(section => section.items.length > 0);

  if (!isOpen) return null;

  return (
    <div className={`${styles.menuOverlay} ${isOpen ? styles.open : ''}`} onClick={onClose}>
      <div className={styles.menuContainer} onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className={styles.menuHeader}>
          <div className={styles.userInfo}>
            <div className={styles.userAvatar}>
              {userName.split(' ').map(n => n.charAt(0)).join('').slice(0, 2)}
            </div>
            <div className={styles.userDetails}>
              <div className={styles.userName}>{userName}</div>
              <div className={styles.userRole}>{userRole}</div>
            </div>
          </div>
          <button 
            className={styles.closeButton}
            onClick={onClose}
            aria-label="Close menu"
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div className={styles.menuContent}>
          {filteredSections.map((section) => (
            <div key={section.title} className={styles.menuSection}>
              <h3 className={styles.sectionTitle}>{section.title}</h3>
              <div className={styles.sectionItems}>
                {section.items.map((item) => (
                  <Link
                    key={item.id}
                    href={item.href}
                    className={`${styles.menuItem} ${isActive(item.href) ? styles.active : ''}`}
                    onClick={() => handleItemClick(item)}
                  >
                    <span className={styles.menuItemIcon}>{item.icon}</span>
                    <span className={styles.menuItemLabel}>{item.label}</span>
                    {item.badge && (
                      <span className={styles.menuItemBadge}>{item.badge}</span>
                    )}
                    {isActive(item.href) && (
                      <span className={styles.activeIndicator}></span>
                    )}
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className={styles.menuFooter}>
          <div className={styles.footerActions}>
            <Link href="/admin/help" className={styles.footerLink}>
              <span>❓</span>
              Help & Support
            </Link>
            <Link href="/admin/settings" className={styles.footerLink}>
              <span>⚙️</span>
              Settings
            </Link>
          </div>
          <div className={styles.version}>
            Ocean Soul Sparkles v1.0.0
          </div>
        </div>
      </div>
    </div>
  );
}
