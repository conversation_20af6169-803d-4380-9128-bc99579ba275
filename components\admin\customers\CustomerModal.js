import { useState, useEffect } from 'react';
import styles from '../../../styles/admin/CustomerModal.module.css';

export default function CustomerModal({ customer, onClose, onUpdate, onDelete }) {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [bookings, setBookings] = useState([]);
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    phone_secondary: '',
    date_of_birth: '',
    address: '',
    notes: ''
  });

  useEffect(() => {
    if (customer) {
      setFormData({
        first_name: customer.first_name || '',
        last_name: customer.last_name || '',
        email: customer.email || '',
        phone: customer.phone || '',
        phone_secondary: customer.phone_secondary || '',
        date_of_birth: customer.date_of_birth || '',
        address: customer.address || '',
        notes: customer.notes || ''
      });
      loadCustomerBookings();
    }
  }, [customer]);

  const loadCustomerBookings = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/customers/${customer.id}/bookings`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setBookings(data.bookings || []);
      }
    } catch (error) {
      console.error('Error loading customer bookings:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      
      const response = await fetch(`/api/admin/customers/${customer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error('Failed to update customer');
      }

      const data = await response.json();
      onUpdate(data.customer);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating customer:', error);
      alert('Failed to update customer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${customer.first_name} ${customer.last_name}? This action cannot be undone.`)) {
      return;
    }

    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      
      const response = await fetch(`/api/admin/customers/${customer.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete customer');
      }

      onDelete(customer.id);
    } catch (error) {
      console.error('Error deleting customer:', error);
      alert(error.message || 'Failed to delete customer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!customer) return null;

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h2>{customer.first_name} {customer.last_name}</h2>
          <div className={styles.modalActions}>
            {!isEditing ? (
              <>
                <button 
                  onClick={() => setIsEditing(true)}
                  className={styles.editButton}
                >
                  ✏️ Edit
                </button>
                <button 
                  onClick={handleDelete}
                  className={styles.deleteButton}
                  disabled={loading}
                >
                  🗑️ Delete
                </button>
              </>
            ) : (
              <>
                <button 
                  onClick={handleSave}
                  className={styles.saveButton}
                  disabled={loading}
                >
                  {loading ? 'Saving...' : '💾 Save'}
                </button>
                <button 
                  onClick={() => setIsEditing(false)}
                  className={styles.cancelButton}
                >
                  ❌ Cancel
                </button>
              </>
            )}
            <button onClick={onClose} className={styles.closeButton}>
              ✕
            </button>
          </div>
        </div>

        <div className={styles.modalContent}>
          <div className={styles.customerInfo}>
            <h3>Customer Information</h3>
            
            <div className={styles.infoGrid}>
              <div className={styles.infoGroup}>
                <label>First Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.first_name}
                    onChange={(e) => handleInputChange('first_name', e.target.value)}
                  />
                ) : (
                  <span>{customer.first_name}</span>
                )}
              </div>

              <div className={styles.infoGroup}>
                <label>Last Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.last_name}
                    onChange={(e) => handleInputChange('last_name', e.target.value)}
                  />
                ) : (
                  <span>{customer.last_name}</span>
                )}
              </div>

              <div className={styles.infoGroup}>
                <label>Email</label>
                {isEditing ? (
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                  />
                ) : (
                  <span>{customer.email}</span>
                )}
              </div>

              <div className={styles.infoGroup}>
                <label>Phone</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                  />
                ) : (
                  <span>{customer.phone}</span>
                )}
              </div>

              <div className={styles.infoGroup}>
                <label>Date of Birth</label>
                {isEditing ? (
                  <input
                    type="date"
                    value={formData.date_of_birth}
                    onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                  />
                ) : (
                  <span>{customer.date_of_birth ? new Date(customer.date_of_birth).toLocaleDateString() : 'Not provided'}</span>
                )}
              </div>

              <div className={styles.infoGroup}>
                <label>Address</label>
                {isEditing ? (
                  <textarea
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    rows={3}
                  />
                ) : (
                  <span>{customer.address || 'Not provided'}</span>
                )}
              </div>
            </div>

            <div className={styles.infoGroup}>
              <label>Notes</label>
              {isEditing ? (
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={4}
                  placeholder="Add notes about this customer..."
                />
              ) : (
                <span>{customer.notes || 'No notes'}</span>
              )}
            </div>
          </div>

          <div className={styles.customerStats}>
            <h3>Customer Statistics</h3>
            <div className={styles.statsGrid}>
              <div className={styles.statItem}>
                <span className={styles.statValue}>{customer.total_bookings || 0}</span>
                <span className={styles.statLabel}>Total Bookings</span>
              </div>
              <div className={styles.statItem}>
                <span className={styles.statValue}>
                  {new Date(customer.created_at).toLocaleDateString()}
                </span>
                <span className={styles.statLabel}>Customer Since</span>
              </div>
            </div>
          </div>

          {bookings.length > 0 && (
            <div className={styles.bookingHistory}>
              <h3>Recent Bookings</h3>
              <div className={styles.bookingsList}>
                {bookings.slice(0, 5).map(booking => (
                  <div key={booking.id} className={styles.bookingItem}>
                    <div className={styles.bookingDate}>
                      {new Date(booking.start_time).toLocaleDateString()}
                    </div>
                    <div className={styles.bookingService}>
                      {booking.service_name || 'Service'}
                    </div>
                    <div className={styles.bookingStatus}>
                      {booking.status}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
