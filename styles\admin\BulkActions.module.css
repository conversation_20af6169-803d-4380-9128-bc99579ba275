/**
 * Ocean Soul Sparkles Admin - Bulk Actions Styles
 * Responsive bulk actions interface with selection controls
 */

.bulkActions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--admin-bg-secondary, #f8f9fa);
  border: 1px solid var(--admin-border-light, #e0e0e0);
  border-radius: var(--admin-radius-md, 8px);
  margin-bottom: 16px;
  transition: all var(--admin-transition-normal, 0.2s ease);
  min-height: 56px;
}

.bulkActions.hasSelection {
  background: var(--admin-primary-light, #e3f2fd);
  border-color: var(--admin-primary, #3788d8);
  box-shadow: 0 2px 8px rgba(55, 136, 216, 0.1);
}

.selectionControls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selectAllLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
  font-weight: 500;
  color: var(--admin-text-primary, #1a1a1a);
}

.selectAllCheckbox {
  display: none;
}

.checkboxCustom {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 2px solid var(--admin-border-medium, #cccccc);
  border-radius: var(--admin-radius-sm, 4px);
  background: var(--admin-bg-primary, #ffffff);
  transition: all var(--admin-transition-normal, 0.2s ease);
  position: relative;
}

.selectAllCheckbox:checked + .checkboxCustom {
  background: var(--admin-primary, #3788d8);
  border-color: var(--admin-primary, #3788d8);
  color: white;
}

.selectAllCheckbox:indeterminate + .checkboxCustom {
  background: var(--admin-warning, #ffc107);
  border-color: var(--admin-warning, #ffc107);
  color: var(--admin-text-primary, #1a1a1a);
}

.checked,
.indeterminate {
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
}

.selectionText {
  font-size: 0.9rem;
  color: var(--admin-text-primary, #1a1a1a);
}

.totalCount {
  font-size: 0.8rem;
  color: var(--admin-text-secondary, #666666);
  font-style: italic;
}

.actionControls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.exportBtn {
  /* Inherit styles from ExportButton component */
}

.actionsDropdown {
  position: relative;
  display: inline-block;
}

.actionsBtn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--admin-primary, #3788d8);
  color: white;
  border: none;
  border-radius: var(--admin-radius-md, 8px);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--admin-transition-normal, 0.2s ease);
  white-space: nowrap;
}

.actionsBtn:hover:not(:disabled) {
  background: var(--admin-primary-dark, #2c6bb8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.3);
}

.actionsBtn:disabled {
  background: var(--admin-text-secondary, #666666);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.actionsBtn.processing {
  background: var(--admin-warning, #ffc107);
  color: var(--admin-text-primary, #1a1a1a);
  cursor: wait;
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dropdownArrow {
  font-size: 0.7rem;
  transition: transform var(--admin-transition-normal, 0.2s ease);
}

.actionsDropdown:hover .dropdownArrow {
  transform: translateY(1px);
}

.actionsMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--admin-bg-primary, #ffffff);
  border: 1px solid var(--admin-border-light, #e0e0e0);
  border-radius: var(--admin-radius-lg, 12px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 10001;
  margin-top: 4px;
  min-width: 180px;
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menuHeader {
  padding: 12px 16px 8px;
  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);
  background: var(--admin-bg-secondary, #f8f9fa);
}

.menuHeader span {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--admin-text-secondary, #666666);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.actionItem {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background var(--admin-transition-normal, 0.2s ease);
  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);
}

.actionItem:last-child {
  border-bottom: none;
}

.actionItem:hover:not(:disabled) {
  background: var(--admin-bg-secondary, #f8f9fa);
}

.actionItem:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.actionItem.primary:hover:not(:disabled) {
  background: rgba(55, 136, 216, 0.1);
}

.actionItem.secondary:hover:not(:disabled) {
  background: rgba(108, 117, 125, 0.1);
}

.actionItem.warning:hover:not(:disabled) {
  background: rgba(255, 193, 7, 0.1);
}

.actionItem.danger:hover:not(:disabled) {
  background: rgba(220, 53, 69, 0.1);
  color: var(--admin-danger, #dc3545);
}

.actionIcon {
  font-size: 1rem;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.actionLabel {
  flex: 1;
  font-weight: 500;
  color: var(--admin-text-primary, #1a1a1a);
  font-size: 0.9rem;
}

.confirmationIndicator {
  font-size: 0.8rem;
  opacity: 0.7;
}

.clearBtn {
  padding: 8px 12px;
  background: transparent;
  color: var(--admin-text-secondary, #666666);
  border: 1px solid var(--admin-border-medium, #cccccc);
  border-radius: var(--admin-radius-md, 8px);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--admin-transition-normal, 0.2s ease);
}

.clearBtn:hover:not(:disabled) {
  background: var(--admin-bg-secondary, #f8f9fa);
  border-color: var(--admin-border-dark, #999999);
  color: var(--admin-text-primary, #1a1a1a);
}

.clearBtn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .bulkActions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 16px;
    min-height: auto;
  }

  .selectionControls {
    justify-content: center;
  }

  .actionControls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .actionsMenu {
    right: -10px;
    left: -10px;
    min-width: auto;
  }

  .actionItem {
    padding: 16px;
  }

  .actionIcon {
    font-size: 1.2rem;
    width: 24px;
  }

  .actionLabel {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .bulkActions {
    padding: 12px;
  }

  .actionControls {
    width: 100%;
  }

  .actionsBtn,
  .clearBtn {
    flex: 1;
    justify-content: center;
  }

  .actionsMenu {
    right: 0;
    left: 0;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .bulkActions {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
  }

  .bulkActions.hasSelection {
    background: rgba(55, 136, 216, 0.2);
  }

  .checkboxCustom {
    background: var(--admin-bg-primary-dark, #1a1a1a);
    border-color: var(--admin-border-dark, #404040);
  }

  .actionsMenu {
    background: var(--admin-bg-primary-dark, #1a1a1a);
    border-color: var(--admin-border-dark, #404040);
  }

  .menuHeader {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
  }

  .actionItem:hover:not(:disabled) {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
  }

  .clearBtn:hover:not(:disabled) {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .bulkActions {
    border-width: 2px;
  }

  .checkboxCustom {
    border-width: 2px;
  }

  .actionsBtn,
  .clearBtn {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .actionsMenu {
    animation: none;
  }

  .spinner {
    animation: none;
  }

  .bulkActions,
  .actionsBtn,
  .actionItem,
  .clearBtn,
  .checkboxCustom {
    transition: none;
  }
}

/* Focus styles for accessibility */
.selectAllLabel:focus-within .checkboxCustom {
  outline: 2px solid var(--admin-primary, #3788d8);
  outline-offset: 2px;
}

.actionsBtn:focus-visible,
.clearBtn:focus-visible {
  outline: 2px solid var(--admin-primary, #3788d8);
  outline-offset: 2px;
}

.actionItem:focus-visible {
  outline: 2px solid var(--admin-primary, #3788d8);
  outline-offset: -2px;
}
