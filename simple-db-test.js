require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

console.log('Environment check:')
console.log('URL exists:', !!supabaseUrl)
console.log('Service key exists:', !!supabaseServiceKey)

if (supabaseUrl && supabaseServiceKey) {
  const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)
  
  async function checkDatabase() {
    try {
      console.log('\n🔍 Checking database structure...')
      
      // Test connection with a simple query
      const { data, error } = await supabaseAdmin
        .from('customers')
        .select('count')
        .limit(1)
      
      if (error) {
        console.log('❌ Database error:', error.message)
        
        // Try to see what tables exist
        const { data: tables, error: tablesError } = await supabaseAdmin
          .rpc('list_tables')
          .catch(() => null)
        
        if (tablesError) {
          console.log('Could not list tables:', tablesError.message)
        }
      } else {
        console.log('✅ Database connection successful!')
        
        // Check for real data
        const { data: customers } = await supabaseAdmin
          .from('customers')
          .select('id, name, email')
          .limit(3)
        
        console.log('Sample customers:', customers)
      }
      
    } catch (error) {
      console.error('Error:', error.message)
    }
  }
  
  checkDatabase()
} else {
  console.log('❌ Missing environment variables')
}
