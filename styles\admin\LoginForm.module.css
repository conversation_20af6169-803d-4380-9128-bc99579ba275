/* Login Form Styles */
.loginForm {
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.header h2 {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.header p {
  color: #6c757d;
  margin: 0;
  font-size: 1rem;
}

.errorAlert {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.errorIcon {
  font-size: 1.1rem;
}

.errorMessage {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  color: #2c3e50;
  font-weight: 600;
  font-size: 0.9rem;
}

.inputContainer {
  position: relative;
}

.input {
  width: 100%;
  padding: 14px 16px 14px 48px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
}

.input:focus {
  outline: none;
  border-color: #3788d8;
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.input.inputError {
  border-color: #dc3545;
}

.inputIcon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.1rem;
  color: #6c757d;
}

.passwordToggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  color: #6c757d;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.passwordToggle:hover {
  color: #3788d8;
}

.fieldError {
  color: #dc3545;
  font-size: 0.85rem;
  font-weight: 500;
}

.formOptions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: -8px 0 8px 0;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #495057;
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: #3788d8;
}

.checkboxText {
  font-weight: 500;
}

.forgotLink {
  color: #3788d8;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.forgotLink:hover {
  color: #2c6cb7;
  text-decoration: underline;
}

.submitButton {
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.3);
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loadingContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.buttonIcon {
  font-size: 1.2rem;
  transition: transform 0.2s ease;
}

.submitButton:hover:not(:disabled) .buttonIcon {
  transform: translateX(2px);
}

.footer {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

.securityFeatures {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
  text-align: center;
}

.featureIcon {
  font-size: 1.2rem;
  margin-bottom: 4px;
}

.feature span {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
  line-height: 1.2;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .header h2 {
    font-size: 1.6rem;
  }

  .formOptions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .forgotLink {
    text-align: center;
  }

  .securityFeatures {
    flex-direction: column;
    gap: 12px;
  }

  .feature {
    flex-direction: row;
    justify-content: center;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .input {
    padding: 12px 14px 12px 44px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .inputIcon {
    left: 14px;
  }

  .passwordToggle {
    right: 14px;
  }

  .submitButton {
    padding: 14px 20px;
  }
}
