/**
 * Email Service for Ocean Soul Sparkles Admin
 * Main service that combines SMTP functionality with email templates
 */

const { sendEmail, verifyConnection, sendTestEmail } = require('./smtp-service');
const {
  bookingConfirmationTemplate,
  bookingReminderTemplate,
  bookingCancellationTemplate,
  paymentReceiptTemplate,
  staffNotificationTemplate,
  lowInventoryAlertTemplate
} = require('./templates');

/**
 * Email service class with settings-based control
 */
class EmailService {
  constructor() {
    this.isConfigured = !!(process.env.SMTP_USER && process.env.SMTP_PASS);
  }

  /**
   * Check if email notifications are enabled in system settings
   */
  async checkEmailEnabled(notificationType = null) {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/admin/settings`);
      const { settings } = await response.json();

      const notifications = settings?.notifications || {};

      // Check global email toggle
      if (!notifications.emailNotifications) {
        return { enabled: false, reason: 'Email notifications disabled globally' };
      }

      // Check specific notification type if provided
      if (notificationType) {
        const typeKey = `email${notificationType.charAt(0).toUpperCase() + notificationType.slice(1)}`;
        if (notifications[typeKey] === false) {
          return { enabled: false, reason: `Email ${notificationType} notifications disabled` };
        }
      }

      return { enabled: true };
    } catch (error) {
      console.error('Error checking email settings:', error);
      // Default to enabled if settings check fails (backward compatibility)
      return { enabled: true };
    }
  }

  /**
   * Send booking confirmation email
   */
  async sendBookingConfirmation(booking) {
    const settingsCheck = await this.checkEmailEnabled('bookingConfirmation');
    if (!settingsCheck.enabled) {
      console.log(`Email booking confirmation skipped: ${settingsCheck.reason}`);
      return { success: false, skipped: true, reason: settingsCheck.reason };
    }

    if (!booking.customerEmail) {
      console.warn('No customer email provided for booking confirmation');
      return { success: false, error: 'No customer email' };
    }

    const html = bookingConfirmationTemplate(booking);

    return await sendEmail({
      to: booking.customerEmail,
      subject: `Booking Confirmation - ${booking.serviceName}`,
      html
    });
  }

  /**
   * Send booking reminder email
   */
  async sendBookingReminder(booking) {
    const settingsCheck = await this.checkEmailEnabled('bookingReminder');
    if (!settingsCheck.enabled) {
      console.log(`Email booking reminder skipped: ${settingsCheck.reason}`);
      return { success: false, skipped: true, reason: settingsCheck.reason };
    }

    if (!booking.customerEmail) {
      console.warn('No customer email provided for booking reminder');
      return { success: false, error: 'No customer email' };
    }

    const html = bookingReminderTemplate(booking);

    return await sendEmail({
      to: booking.customerEmail,
      subject: `Appointment Reminder - Tomorrow at ${booking.time}`,
      html
    });
  }

  /**
   * Send booking cancellation email
   */
  async sendBookingCancellation(booking) {
    if (!booking.customerEmail) {
      console.warn('No customer email provided for booking cancellation');
      return { success: false, error: 'No customer email' };
    }

    const html = bookingCancellationTemplate(booking);
    
    return await sendEmail({
      to: booking.customerEmail,
      subject: `Booking Cancellation - ${booking.serviceName}`,
      html
    });
  }

  /**
   * Send payment receipt email
   */
  async sendPaymentReceipt(payment) {
    if (!payment.customerEmail) {
      console.warn('No customer email provided for payment receipt');
      return { success: false, error: 'No customer email' };
    }

    const html = paymentReceiptTemplate(payment);
    
    return await sendEmail({
      to: payment.customerEmail,
      subject: `Payment Receipt - ${payment.receiptNumber}`,
      html
    });
  }

  /**
   * Send staff notification email
   */
  async sendStaffNotification(notification) {
    if (!notification.staffEmail) {
      console.warn('No staff email provided for notification');
      return { success: false, error: 'No staff email' };
    }

    const html = staffNotificationTemplate(notification);
    
    return await sendEmail({
      to: notification.staffEmail,
      subject: notification.subject || 'Staff Notification',
      html
    });
  }

  /**
   * Send low inventory alert to admin
   */
  async sendLowInventoryAlert(items, adminEmail) {
    if (!adminEmail) {
      adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    }

    const html = lowInventoryAlertTemplate(items);
    
    return await sendEmail({
      to: adminEmail,
      subject: `Low Inventory Alert - ${items.length} items need attention`,
      html
    });
  }

  /**
   * Send bulk emails (for newsletters, announcements, etc.)
   */
  async sendBulkEmail(recipients, subject, html) {
    const results = [];
    
    for (const recipient of recipients) {
      try {
        const result = await sendEmail({
          to: recipient.email,
          subject,
          html: html.replace(/{{name}}/g, recipient.name || 'Valued Customer')
        });
        results.push({ email: recipient.email, ...result });
        
        // Add delay to avoid overwhelming SMTP server
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        results.push({ 
          email: recipient.email, 
          success: false, 
          error: error.message 
        });
      }
    }
    
    return results;
  }

  /**
   * Verify email configuration
   */
  async verifyConfiguration() {
    return await verifyConnection();
  }

  /**
   * Send test email
   */
  async sendTest(to) {
    return await sendTestEmail(to);
  }

  /**
   * Get email service status
   */
  getStatus() {
    return {
      configured: this.isConfigured,
      smtpHost: process.env.SMTP_HOST || 'Not configured',
      smtpUser: process.env.SMTP_USER ? 'Configured' : 'Not configured',
      smtpPort: process.env.SMTP_PORT || '587'
    };
  }
}

// Create singleton instance
const emailService = new EmailService();

module.exports = emailService;
