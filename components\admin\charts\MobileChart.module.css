/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Chart Styles
 * Mobile-optimized chart styling with responsive design
 */

.container {
  width: 100%;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  border-radius: 0;
  box-shadow: none;
  background: #ffffff;
}

.header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.titleSection {
  flex: 1;
  min-width: 0;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.actions {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.25rem;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.actionButton:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #374151;
}

.actionButton:active {
  background: #e5e7eb;
  transform: scale(0.95);
}

.chartWrapper {
  position: relative;
  padding: 1.5rem;
  background: #ffffff;
}

.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
  min-height: 200px;
}

.errorIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.errorMessage {
  font-size: 1rem;
  color: #ef4444;
  line-height: 1.5;
}

.loading {
  opacity: 0.7;
  pointer-events: none;
}

.fullscreenOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Mobile tooltip styles */
.mobileTooltip {
  background: rgba(0, 0, 0, 0.9);
  color: #ffffff;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  max-width: 250px;
  z-index: 1001;
}

.tooltipHeader {
  font-weight: bold;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltipBody {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tooltipItem {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tooltipColor {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .container {
    border-radius: 20px;
    margin: 0.5rem;
  }
  
  .fullscreen {
    margin: 0;
    border-radius: 0;
  }
  
  .header {
    padding: 1.25rem 1.25rem 1rem;
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .titleSection {
    text-align: center;
  }
  
  .title {
    font-size: 1.5rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .actions {
    justify-content: center;
    margin-left: 0;
    gap: 1rem;
  }
  
  .actionButton {
    width: 48px;
    height: 48px;
    font-size: 1.5rem;
    border-radius: 12px;
  }
  
  .chartWrapper {
    padding: 1.25rem;
  }
  
  .loadingState {
    min-height: 250px;
  }
  
  .spinner {
    width: 48px;
    height: 48px;
    border-width: 4px;
  }
  
  .error {
    padding: 2rem 1.25rem;
    min-height: 250px;
  }
  
  .errorIcon {
    font-size: 4rem;
  }
  
  .errorMessage {
    font-size: 1.125rem;
  }
  
  .mobileTooltip {
    font-size: 16px;
    padding: 16px;
    border-radius: 12px;
    max-width: 300px;
  }
  
  .tooltipHeader {
    margin-bottom: 12px;
    padding-bottom: 12px;
  }
  
  .tooltipBody {
    gap: 8px;
  }
  
  .tooltipItem {
    gap: 12px;
  }
  
  .tooltipColor {
    width: 16px;
    height: 16px;
    border-radius: 4px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .container {
    margin: 0.25rem;
  }
  
  .header {
    padding: 1rem;
  }
  
  .title {
    font-size: 1.25rem;
  }
  
  .chartWrapper {
    padding: 1rem;
  }
  
  .actionButton {
    width: 44px;
    height: 44px;
  }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
  .header {
    flex-direction: row;
    align-items: center;
    padding: 1rem 1.25rem;
  }
  
  .titleSection {
    text-align: left;
  }
  
  .actions {
    margin-left: 1rem;
  }
  
  .chartWrapper {
    padding: 1rem 1.25rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .container {
    border: 2px solid #000000;
  }
  
  .actionButton {
    border-color: #000000;
    border-width: 2px;
  }
  
  .actionButton:hover {
    background: #000000;
    color: #ffffff;
  }
  
  .mobileTooltip {
    background: #000000;
    border: 2px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .container,
  .actionButton {
    transition: none;
  }
  
  .spinner {
    animation: none;
  }
  
  .actionButton:active {
    transform: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .container {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .header {
    background: #374151;
    border-color: #4b5563;
  }
  
  .title {
    color: #f9fafb;
  }
  
  .subtitle {
    color: #d1d5db;
  }
  
  .actionButton {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }
  
  .actionButton:hover {
    background: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
  }
  
  .chartWrapper {
    background: #1f2937;
  }
  
  .loadingState {
    color: #d1d5db;
  }
  
  .spinner {
    border-color: #4b5563;
    border-top-color: #3b82f6;
  }
  
  .errorMessage {
    color: #fca5a5;
  }
}

/* Print styles */
@media print {
  .container {
    background: #ffffff !important;
    box-shadow: none !important;
    border: 1px solid #000000 !important;
  }
  
  .actions {
    display: none !important;
  }
  
  .fullscreenOverlay {
    display: none !important;
  }
  
  .mobileTooltip {
    display: none !important;
  }
}
