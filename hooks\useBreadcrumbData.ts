/**
 * Ocean Soul Sparkles Admin - Breadcrumb Data Hook
 * Provides enhanced breadcrumb data with dynamic content
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

interface BreadcrumbData {
  customerName?: string;
  serviceName?: string;
  bookingId?: string;
  productName?: string;
  artistName?: string;
  loading: boolean;
  error?: string;
}

export function useBreadcrumbData(): BreadcrumbData {
  const router = useRouter();
  const [data, setData] = useState<BreadcrumbData>({ loading: false });

  useEffect(() => {
    const fetchBreadcrumbData = async () => {
      const { pathname, query } = router;
      
      // Only fetch data for dynamic routes that need it
      if (!query.id || typeof query.id !== 'string') {
        setData({ loading: false });
        return;
      }

      setData({ loading: true });

      try {
        const token = localStorage.getItem('admin-token');
        if (!token) {
          setData({ loading: false, error: 'No authentication token' });
          return;
        }

        let apiEndpoint = '';
        let dataKey = '';

        // Determine which API to call based on the route
        if (pathname.includes('/customers/[id]')) {
          apiEndpoint = `/api/admin/customers/${query.id}`;
          dataKey = 'customer';
        } else if (pathname.includes('/bookings/[id]')) {
          apiEndpoint = `/api/admin/bookings/${query.id}`;
          dataKey = 'booking';
        } else if (pathname.includes('/services/[id]')) {
          apiEndpoint = `/api/admin/services/${query.id}`;
          dataKey = 'service';
        } else if (pathname.includes('/products/[id]')) {
          apiEndpoint = `/api/admin/products/${query.id}`;
          dataKey = 'product';
        } else if (pathname.includes('/artists/[id]')) {
          apiEndpoint = `/api/admin/artists/${query.id}`;
          dataKey = 'artist';
        } else {
          setData({ loading: false });
          return;
        }

        const response = await fetch(apiEndpoint, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch ${dataKey} data`);
        }

        const result = await response.json();
        
        // Extract relevant data based on the type
        const breadcrumbData: BreadcrumbData = { loading: false };

        if (dataKey === 'customer' && result.customer) {
          breadcrumbData.customerName = `${result.customer.first_name} ${result.customer.last_name}`;
        } else if (dataKey === 'booking' && result.booking) {
          breadcrumbData.bookingId = result.booking.id;
          if (result.booking.customers) {
            const customer = Array.isArray(result.booking.customers) 
              ? result.booking.customers[0] 
              : result.booking.customers;
            if (customer) {
              breadcrumbData.customerName = `${customer.first_name} ${customer.last_name}`;
            }
          }
          if (result.booking.services) {
            const service = Array.isArray(result.booking.services) 
              ? result.booking.services[0] 
              : result.booking.services;
            if (service) {
              breadcrumbData.serviceName = service.name;
            }
          }
        } else if (dataKey === 'service' && result.service) {
          breadcrumbData.serviceName = result.service.name;
        } else if (dataKey === 'product' && result.product) {
          breadcrumbData.productName = result.product.name;
        } else if (dataKey === 'artist' && result.artist) {
          breadcrumbData.artistName = result.artist.artist_name || result.artist.display_name;
        }

        setData(breadcrumbData);

      } catch (error) {
        console.error('Error fetching breadcrumb data:', error);
        setData({ 
          loading: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    };

    fetchBreadcrumbData();
  }, [router.pathname, router.query.id]);

  return data;
}

// Helper function to generate dynamic breadcrumb labels
export function getDynamicBreadcrumbLabel(
  path: string, 
  data: BreadcrumbData
): string | null {
  if (data.loading) {
    return 'Loading...';
  }

  if (path.includes('/customers/') && data.customerName) {
    return data.customerName;
  }

  if (path.includes('/bookings/') && data.bookingId) {
    if (data.customerName && data.serviceName) {
      return `${data.customerName} - ${data.serviceName}`;
    } else if (data.customerName) {
      return `Booking for ${data.customerName}`;
    } else {
      return `Booking #${data.bookingId.slice(0, 8)}`;
    }
  }

  if (path.includes('/services/') && data.serviceName) {
    return data.serviceName;
  }

  if (path.includes('/products/') && data.productName) {
    return data.productName;
  }

  if (path.includes('/artists/') && data.artistName) {
    return data.artistName;
  }

  return null;
}
