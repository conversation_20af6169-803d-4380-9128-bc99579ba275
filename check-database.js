const { supabaseAdmin } = require('./lib/supabase')

async function checkDatabaseTables() {
  try {
    console.log('🔍 Checking database structure...')
    
    // Check customers table
    console.log('\n📋 Testing customers table...')
    const { data: customers, error: customersError } = await supabaseAdmin
      .from('customers')
      .select('*')
      .limit(5)
    
    if (customersError) {
      console.log('❌ Customers error:', customersError.message)
    } else {
      console.log(`✅ Found ${customers?.length || 0} customers`)
      if (customers?.length > 0) {
        console.log('Sample customer:', customers[0])
      }
    }
    
    // Check services table
    console.log('\n🎨 Testing services table...')
    const { data: services, error: servicesError } = await supabaseAdmin
      .from('services')
      .select('*')
      .limit(5)
    
    if (servicesError) {
      console.log('❌ Services error:', servicesError.message)
    } else {
      console.log(`✅ Found ${services?.length || 0} services`)
      if (services?.length > 0) {
        console.log('Sample service:', services[0])
      }
    }
    
    // Check artists table
    console.log('\n👩‍🎨 Testing artist_profiles table...')
    const { data: artists, error: artistsError } = await supabaseAdmin
      .from('artist_profiles')
      .select('*')
      .limit(5)
    
    if (artistsError) {
      console.log('❌ Artists error:', artistsError.message)
    } else {
      console.log(`✅ Found ${artists?.length || 0} artists`)
      if (artists?.length > 0) {
        console.log('Sample artist:', artists[0])
      }
    }
    
    // Check bookings table
    console.log('\n📅 Testing bookings table...')
    const { data: bookings, error: bookingsError } = await supabaseAdmin
      .from('bookings')
      .select('*')
      .limit(5)
    
    if (bookingsError) {
      console.log('❌ Bookings error:', bookingsError.message)
    } else {
      console.log(`✅ Found ${bookings?.length || 0} bookings`)
      if (bookings?.length > 0) {
        console.log('Sample booking:', bookings[0])
      }
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error)
  }
}

checkDatabaseTables()
