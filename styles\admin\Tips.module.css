/* Tip Management Styles */

.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.header p {
  color: #64748b;
  font-size: 1.1rem;
  margin: 0;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Summary Cards */
.summaryCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summaryCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.summaryCard h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #64748b;
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.summaryValue {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.summaryCount {
  font-size: 0.9rem;
  color: #64748b;
}

/* Controls */
.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filterSelect,
.dateFilter {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: border-color 0.2s ease;
}

.filterSelect:focus,
.dateFilter:focus {
  outline: none;
  border-color: #3b82f6;
}

.bulkActions {
  display: flex;
  gap: 0.5rem;
}

.distributeButton,
.holdButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.distributeButton {
  background: #10b981;
  color: white;
}

.distributeButton:hover:not(:disabled) {
  background: #059669;
}

.holdButton {
  background: #f59e0b;
  color: white;
}

.holdButton:hover:not(:disabled) {
  background: #d97706;
}

.distributeButton:disabled,
.holdButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Tips Table */
.tipsTable {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tipsTable table {
  width: 100%;
  border-collapse: collapse;
}

.tipsTable th {
  background: #f8fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

.tipsTable td {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  vertical-align: middle;
}

.tipsTable tr:hover {
  background: #f8fafc;
}

.amount {
  font-weight: 600;
  color: #10b981;
  font-size: 1.1rem;
}

/* Status Badges */
.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusBadge.pending {
  background: #fef3c7;
  color: #92400e;
}

.statusBadge.distributed {
  background: #d1fae5;
  color: #065f46;
}

.statusBadge.held {
  background: #fee2e2;
  color: #991b1b;
}

/* Action Buttons */
.actionButtons {
  display: flex;
  gap: 0.5rem;
}

.distributeBtn,
.holdBtn,
.releaseBtn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.distributeBtn {
  background: #10b981;
  color: white;
}

.distributeBtn:hover:not(:disabled) {
  background: #059669;
}

.holdBtn {
  background: #f59e0b;
  color: white;
}

.holdBtn:hover:not(:disabled) {
  background: #d97706;
}

.releaseBtn {
  background: #3b82f6;
  color: white;
}

.releaseBtn:hover:not(:disabled) {
  background: #2563eb;
}

.distributeBtn:disabled,
.holdBtn:disabled,
.releaseBtn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.emptyState p {
  font-size: 1.1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filters {
    justify-content: center;
  }
  
  .bulkActions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .summaryCards {
    grid-template-columns: 1fr;
  }
  
  .filters {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .tipsTable {
    overflow-x: auto;
  }
  
  .tipsTable table {
    min-width: 800px;
  }
  
  .actionButtons {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .distributeBtn,
  .holdBtn,
  .releaseBtn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}
