/**
 * Ocean Soul Sparkles Admin - Action Sheet Styles
 * iOS/Android style action sheets for mobile interactions
 */

.actionSheetOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 10000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 0 16px 16px 16px;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.actionSheetOverlay.open {
  opacity: 1;
}

.actionSheet {
  background: var(--admin-card-background, #ffffff);
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.actionSheetOverlay.open .actionSheet {
  transform: translateY(0);
}

.header {
  padding: 20px 20px 16px 20px;
  border-bottom: 1px solid var(--admin-border, #e0e0e0);
  text-align: center;
}

.title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  margin: 0 0 8px 0;
}

.message {
  font-size: 0.875rem;
  color: var(--admin-text-secondary, #666666);
  margin: 0;
  line-height: 1.4;
}

.actions {
  padding: 8px 0;
}

.action {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border: none;
  background: transparent;
  font-size: 1rem;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-height: 56px; /* Touch-friendly minimum height */
}

.action:hover {
  background: var(--admin-hover-background, #f5f5f5);
}

.action:active {
  background: var(--admin-active-background, #e0e0e0);
  transform: scale(0.98);
  transition: transform 0.1s ease, background-color 0.1s ease;
}

.action.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action.disabled:hover,
.action.disabled:active {
  background: transparent;
  transform: none;
}

.actionIcon {
  font-size: 1.25rem;
  margin-right: 12px;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.actionLabel {
  flex: 1;
  color: var(--admin-text-primary, #1a1a1a);
}

/* Action variants */
.action.default .actionLabel {
  color: var(--admin-text-primary, #1a1a1a);
}

.action.primary {
  background: var(--admin-primary, #16213e);
  color: white;
  margin: 8px 16px;
  border-radius: 12px;
}

.action.primary:hover {
  background: var(--admin-primary-dark, #0f1419);
}

.action.primary .actionLabel {
  color: white;
  font-weight: 600;
}

.action.destructive .actionLabel {
  color: #f44336;
  font-weight: 600;
}

.action.destructive:hover {
  background: rgba(244, 67, 54, 0.1);
}

.action.destructive:active {
  background: rgba(244, 67, 54, 0.2);
}

.cancelSection {
  border-top: 8px solid var(--admin-background, #f8f9fa);
  padding: 8px 0;
}

.action.cancel {
  font-weight: 600;
  color: var(--admin-text-secondary, #666666);
}

.action.cancel:hover {
  background: var(--admin-hover-background, #f5f5f5);
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .actionSheetOverlay {
    padding: 0;
  }

  .actionSheet {
    border-radius: 16px 16px 0 0;
    max-height: 85vh;
  }

  .header {
    padding: 16px 16px 12px 16px;
  }

  .action {
    padding: 14px 16px;
    min-height: 52px;
  }

  .actionIcon {
    font-size: 1.125rem;
    margin-right: 10px;
    width: 20px;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .actionSheet {
    max-height: 90vh;
  }

  .header {
    padding: 12px 12px 8px 12px;
  }

  .title {
    font-size: 1rem;
  }

  .message {
    font-size: 0.8rem;
  }

  .action {
    padding: 12px;
    min-height: 48px;
    font-size: 0.9rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .actionSheet {
    background: var(--admin-card-background-dark, #2a2a2a);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
  }

  .header {
    border-bottom-color: var(--admin-border-dark, #404040);
  }

  .title {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .message {
    color: var(--admin-text-secondary-dark, #cccccc);
  }

  .action:hover {
    background: var(--admin-hover-background-dark, #3a3a3a);
  }

  .action:active {
    background: var(--admin-active-background-dark, #4a4a4a);
  }

  .actionLabel {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .action.cancel .actionLabel {
    color: var(--admin-text-secondary-dark, #cccccc);
  }

  .cancelSection {
    border-top-color: var(--admin-background-dark, #1a1a1a);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .actionSheet {
    border: 2px solid var(--admin-border, #000000);
  }

  .action {
    border-bottom: 1px solid var(--admin-border, #000000);
  }

  .action.destructive .actionLabel {
    color: #d32f2f;
    font-weight: 700;
  }

  .action.primary {
    border: 2px solid var(--admin-primary, #16213e);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .actionSheetOverlay {
    transition: opacity 0.1s ease;
  }

  .actionSheet {
    transition: transform 0.1s ease;
  }

  .action {
    transition: background-color 0.1s ease;
  }

  .action:active {
    transform: none;
    transition: background-color 0.1s ease;
  }
}

/* Focus styles for accessibility */
.action:focus {
  outline: 2px solid var(--admin-primary, #16213e);
  outline-offset: -2px;
}

.action:focus:not(:focus-visible) {
  outline: none;
}

/* Safe area support for devices with notches */
@supports (padding: max(0px)) {
  .actionSheet {
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .actionSheet {
    max-height: 95vh;
    border-radius: 12px;
    margin: 8px;
  }

  .actionSheetOverlay {
    align-items: center;
    padding: 8px;
  }

  .header {
    padding: 12px 16px 8px 16px;
  }

  .action {
    padding: 10px 16px;
    min-height: 44px;
  }
}
