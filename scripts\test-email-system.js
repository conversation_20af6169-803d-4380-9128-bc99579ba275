#!/usr/bin/env node

/**
 * Test Email System
 * Tests the email notification system for Ocean Soul Sparkles Admin
 */

require('dotenv').config({ path: require('path').join(__dirname, '..', '.env.local') });

async function testEmailSystem() {
  console.log('📧 Testing Ocean Soul Sparkles Email System...\n');

  try {
    // Import email service (using dynamic import to handle potential module issues)
    const emailService = require('../lib/email/email-service');

    // Test 1: Check email service status
    console.log('1. Checking email service configuration...');
    const status = emailService.getStatus();
    console.log('   Status:', status);

    if (!status.configured) {
      console.log('\n⚠️  SMTP not configured. Emails will be logged to console.');
      console.log('   To configure SMTP, add these environment variables:');
      console.log('   - SMTP_HOST (e.g., smtp.gmail.com)');
      console.log('   - SMTP_USER (your email address)');
      console.log('   - SMTP_PASS (your email password or app password)');
    }

    // Test 2: Verify SMTP connection (if configured)
    console.log('\n2. Verifying SMTP connection...');
    const connectionResult = await emailService.verifyConfiguration();
    console.log('   Connection:', connectionResult.success ? '✅ Success' : `❌ Failed: ${connectionResult.error}`);

    // Test 3: Test booking confirmation email
    console.log('\n3. Testing booking confirmation email...');
    const bookingData = {
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      serviceName: 'Festival Face Paint',
      artistName: 'Emma Wilson',
      date: '2025-06-16',
      time: '2:00 PM',
      duration: 60,
      location: 'Studio',
      totalAmount: '50.00'
    };

    const confirmationResult = await emailService.sendBookingConfirmation(bookingData);
    console.log('   Booking confirmation:', confirmationResult.success ? '✅ Sent' : `❌ Failed: ${confirmationResult.error}`);

    // Test 4: Test booking reminder email
    console.log('\n4. Testing booking reminder email...');
    const reminderResult = await emailService.sendBookingReminder(bookingData);
    console.log('   Booking reminder:', reminderResult.success ? '✅ Sent' : `❌ Failed: ${reminderResult.error}`);

    // Test 5: Test payment receipt email
    console.log('\n5. Testing payment receipt email...');
    const paymentData = {
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      receiptNumber: 'OSS-2025-001',
      date: '2025-06-15',
      serviceName: 'Festival Face Paint',
      amount: '50.00',
      method: 'Card',
      transactionId: 'sq_txn_test_123'
    };

    const receiptResult = await emailService.sendPaymentReceipt(paymentData);
    console.log('   Payment receipt:', receiptResult.success ? '✅ Sent' : `❌ Failed: ${receiptResult.error}`);

    // Test 6: Test staff notification email
    console.log('\n6. Testing staff notification email...');
    const staffNotificationData = {
      staffName: 'Emma Wilson',
      staffEmail: '<EMAIL>',
      subject: 'New Booking Assignment',
      message: 'You have been assigned a new booking for tomorrow.',
      details: '<p><strong>Service:</strong> Festival Face Paint<br><strong>Time:</strong> 2:00 PM</p>',
      actionRequired: 'Please confirm your availability'
    };

    const staffResult = await emailService.sendStaffNotification(staffNotificationData);
    console.log('   Staff notification:', staffResult.success ? '✅ Sent' : `❌ Failed: ${staffResult.error}`);

    // Test 7: Test low inventory alert
    console.log('\n7. Testing low inventory alert...');
    const inventoryItems = [
      { name: 'Face Paint - Red', currentStock: 2, minStock: 5 },
      { name: 'Glitter Gel - Gold', currentStock: 1, minStock: 3 }
    ];

    const inventoryResult = await emailService.sendLowInventoryAlert(inventoryItems, '<EMAIL>');
    console.log('   Low inventory alert:', inventoryResult.success ? '✅ Sent' : `❌ Failed: ${inventoryResult.error}`);

    console.log('\n🎉 Email system testing completed!');
    
    if (status.configured && connectionResult.success) {
      console.log('\n✅ Email system is fully functional and ready for production use.');
    } else {
      console.log('\n📝 Email system is working in console-log mode.');
      console.log('   Configure SMTP credentials to enable actual email sending.');
    }

  } catch (error) {
    console.error('\n❌ Email system test failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  testEmailSystem().catch(console.error);
}

module.exports = { testEmailSystem };
