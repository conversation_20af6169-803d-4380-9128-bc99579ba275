const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // Admin-specific configuration
  env: {
    ADMIN_SUBDOMAIN: 'true',
    PUBLIC_ACCESS: 'false',
    ENHANCED_SECURITY: 'true'
  },

  // Security headers for admin subdomain with PWA support
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(self), microphone=(self), geolocation=(self), payment=(self), notifications=(self), vibrate=(self)'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.squareup.com https://cdn.onesignal.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co https://js.squareup.com https://api.onesignal.com wss://*.supabase.co; frame-src 'self' https://js.squareup.com; worker-src 'self'; manifest-src 'self';"
          },
          {
            key: 'X-Admin-Portal',
            value: 'true'
          }
        ]
      },
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/manifest+json'
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      },
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/javascript'
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate'
          },
          {
            key: 'Service-Worker-Allowed',
            value: '/'
          }
        ]
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, no-cache, must-revalidate, private'
          },
          {
            key: 'X-Admin-API',
            value: 'true'
          }
        ]
      }
    ];
  },

  // Admin-specific redirects
  async redirects() {
    return [
      {
        source: '/login',
        destination: '/admin/login',
        permanent: false
      },
      // Block public routes - redirect to main dashboard
      {
        source: '/shop',
        destination: '/',
        permanent: false
      },
      {
        source: '/book-online',
        destination: '/admin/bookings',
        permanent: false
      }
    ];
  },

  // Note: Webpack configuration moved to bottom to avoid duplication

  // Image optimization for admin assets
  images: {
    domains: [
      'ndlgbcsbidyhxbpqzgqp.supabase.co',
      'oceansoulsparkles.com.au',
      'admin.oceansoulsparkles.com.au'
    ],
    formats: ['image/webp', 'image/avif']
  },

  // Experimental features for admin performance
  experimental: {
    scrollRestoration: true
  },

  // Admin-specific build configuration
  output: 'standalone',
  
  // Environment-specific settings
  publicRuntimeConfig: {
    adminMode: true,
    enhancedSecurity: true
  },

  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: false
  },

  // Webpack configuration with performance optimizations
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
      // Add path aliases
      config.resolve.alias = {
        ...config.resolve.alias,
        '@': path.resolve(__dirname),
        '@/admin': path.resolve(__dirname, 'components/admin'),
        '@/lib': path.resolve(__dirname, 'lib'),
        '@/hooks': path.resolve(__dirname, 'hooks'),
        '@/utils': path.resolve(__dirname, 'utils'),
        '@/types': path.resolve(__dirname, 'types')
      };

      // Admin-specific webpack optimizations
      if (!isServer) {
        config.resolve.fallback = {
          ...config.resolve.fallback,
          fs: false,
          path: false,
          util: false,
          crypto: false,
          stream: false,
          buffer: false
        };
      }

      // Bundle analyzer
      if (process.env.ANALYZE === 'true') {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: isServer ? '../analyze/server.html' : './analyze/client.html'
          })
        );
      }

      // Performance optimizations
      if (!dev && !isServer) {
        // Split chunks optimization
        config.optimization.splitChunks = {
          chunks: 'all',
          cacheGroups: {
            default: false,
            vendors: false,
            // Vendor chunk
            vendor: {
              name: 'vendor',
              chunks: 'all',
              test: /node_modules/,
              priority: 20
            },
            // Common chunk
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              priority: 10,
              reuseExistingChunk: true,
              enforce: true
            },
            // Chart.js chunk (large library)
            charts: {
              name: 'charts',
              test: /[\\/]node_modules[\\/](chart\.js|react-chartjs-2)[\\/]/,
              chunks: 'all',
              priority: 30
            },
            // Supabase chunk
            supabase: {
              name: 'supabase',
              test: /[\\/]node_modules[\\/]@supabase[\\/]/,
              chunks: 'all',
              priority: 30
            },
            // Square SDK chunk
            square: {
              name: 'square',
              test: /[\\/]node_modules[\\/](square|squareup)[\\/]/,
              chunks: 'all',
              priority: 30
            }
          }
        };

        // Tree shaking optimization
        config.optimization.usedExports = true;
        config.optimization.sideEffects = false;

        // Minimize bundle size - removed problematic React aliases
        config.resolve.alias = {
          ...config.resolve.alias
        };
      }

      // Canvas support for QR code generation
      if (isServer) {
        config.externals = config.externals || [];
        config.externals.push('canvas');
      }

      return config;
    },

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: false,
    dirs: ['pages', 'components', 'lib', 'hooks', 'utils']
  }
};

module.exports = nextConfig;
