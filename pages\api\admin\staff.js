import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth/admin-auth'

/**
 * API endpoint for staff management operations
 * Handles CRUD operations for staff members and role management
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Staff API called - ${req.method}`)

  try {
    // Authenticate admin request
    const { user, error: authError } = await authenticateAdminRequest(req)
    if (authError || !user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: authError?.message || 'Authentication failed',
        requestId
      })
    }

    // Check permissions - only DEV and Admin can manage staff
    if (!['DEV', 'Admin'].includes(user.role)) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You do not have permission to manage staff',
        requestId
      })
    }

    if (req.method === 'GET') {
      // Get all staff members
      try {
        const { data: staff, error } = await supabaseAdmin
          .from('admin_users')
          .select(`
            id,
            first_name,
            last_name,
            email,
            role,
            status,
            last_login,
            created_at,
            updated_at
          `)
          .order('created_at', { ascending: false })

        if (error) {
          console.error(`[${requestId}] Database error:`, error)
          
          // Return mock data if database query fails
          const mockStaff = [
            {
              id: '1',
              firstName: 'Admin',
              lastName: 'User',
              email: '<EMAIL>',
              role: 'Admin',
              status: 'active',
              lastLogin: new Date().toISOString(),
              createdAt: '2024-01-01T00:00:00Z'
            },
            {
              id: '2',
              firstName: 'Sarah',
              lastName: 'Johnson',
              email: '<EMAIL>',
              role: 'Artist',
              status: 'active',
              lastLogin: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
              createdAt: '2024-02-15T00:00:00Z'
            },
            {
              id: '3',
              firstName: 'Maya',
              lastName: 'Patel',
              email: '<EMAIL>',
              role: 'Braider',
              status: 'active',
              lastLogin: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
              createdAt: '2024-03-10T00:00:00Z'
            }
          ]

          return res.status(200).json({
            staff: mockStaff,
            source: 'mock',
            message: 'Using mock data - database connection issue',
            requestId
          })
        }

        // Transform database data to match frontend expectations
        const transformedStaff = staff.map(member => ({
          id: member.id,
          firstName: member.first_name,
          lastName: member.last_name,
          email: member.email,
          role: member.role,
          status: member.status,
          lastLogin: member.last_login,
          createdAt: member.created_at
        }))

        return res.status(200).json({
          staff: transformedStaff,
          source: 'database',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error fetching staff:`, error)
        return res.status(500).json({
          error: 'Failed to fetch staff',
          message: error.message,
          requestId
        })
      }

    } else if (req.method === 'POST') {
      // Create new staff member
      const { firstName, lastName, email, role, password } = req.body

      if (!firstName || !lastName || !email || !role) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'firstName, lastName, email, and role are required',
          requestId
        })
      }

      try {
        // Check if email already exists
        const { data: existingUser } = await supabaseAdmin
          .from('admin_users')
          .select('id')
          .eq('email', email)
          .single()

        if (existingUser) {
          return res.status(409).json({
            error: 'Email already exists',
            message: 'A staff member with this email already exists',
            requestId
          })
        }

        // Create new staff member
        const { data: newStaff, error } = await supabaseAdmin
          .from('admin_users')
          .insert([
            {
              first_name: firstName,
              last_name: lastName,
              email: email,
              role: role,
              status: 'active',
              password_hash: password ? await hashPassword(password) : null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ])
          .select()
          .single()

        if (error) {
          console.error(`[${requestId}] Error creating staff:`, error)
          return res.status(500).json({
            error: 'Failed to create staff member',
            message: error.message,
            requestId
          })
        }

        // Transform response
        const transformedStaff = {
          id: newStaff.id,
          firstName: newStaff.first_name,
          lastName: newStaff.last_name,
          email: newStaff.email,
          role: newStaff.role,
          status: newStaff.status,
          createdAt: newStaff.created_at
        }

        return res.status(201).json({
          staff: transformedStaff,
          message: 'Staff member created successfully',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error creating staff:`, error)
        return res.status(500).json({
          error: 'Failed to create staff member',
          message: error.message,
          requestId
        })
      }

    } else if (req.method === 'PUT') {
      // Update staff member
      const { id, firstName, lastName, email, role, status } = req.body

      if (!id) {
        return res.status(400).json({
          error: 'Missing staff ID',
          message: 'Staff ID is required for updates',
          requestId
        })
      }

      try {
        const { data: updatedStaff, error } = await supabaseAdmin
          .from('admin_users')
          .update({
            first_name: firstName,
            last_name: lastName,
            email: email,
            role: role,
            status: status,
            updated_at: new Date().toISOString()
          })
          .eq('id', id)
          .select()
          .single()

        if (error) {
          console.error(`[${requestId}] Error updating staff:`, error)
          return res.status(500).json({
            error: 'Failed to update staff member',
            message: error.message,
            requestId
          })
        }

        // Transform response
        const transformedStaff = {
          id: updatedStaff.id,
          firstName: updatedStaff.first_name,
          lastName: updatedStaff.last_name,
          email: updatedStaff.email,
          role: updatedStaff.role,
          status: updatedStaff.status,
          lastLogin: updatedStaff.last_login,
          createdAt: updatedStaff.created_at
        }

        return res.status(200).json({
          staff: transformedStaff,
          message: 'Staff member updated successfully',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error updating staff:`, error)
        return res.status(500).json({
          error: 'Failed to update staff member',
          message: error.message,
          requestId
        })
      }

    } else if (req.method === 'DELETE') {
      // Delete staff member
      const { id } = req.query

      if (!id) {
        return res.status(400).json({
          error: 'Missing staff ID',
          message: 'Staff ID is required for deletion',
          requestId
        })
      }

      try {
        const { error } = await supabaseAdmin
          .from('admin_users')
          .delete()
          .eq('id', id)

        if (error) {
          console.error(`[${requestId}] Error deleting staff:`, error)
          return res.status(500).json({
            error: 'Failed to delete staff member',
            message: error.message,
            requestId
          })
        }

        return res.status(200).json({
          message: 'Staff member deleted successfully',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error deleting staff:`, error)
        return res.status(500).json({
          error: 'Failed to delete staff member',
          message: error.message,
          requestId
        })
      }

    } else {
      return res.status(405).json({ 
        error: 'Method not allowed',
        message: 'Only GET, POST, PUT, and DELETE methods are supported',
        requestId
      })
    }

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      requestId
    })
  }
}

// Helper function to hash passwords (placeholder)
async function hashPassword(password) {
  // In a real implementation, use bcrypt or similar
  // For now, return a placeholder hash
  return `hashed_${password}_${Date.now()}`
}
