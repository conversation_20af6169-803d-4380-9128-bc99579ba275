import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { toast } from 'react-toastify';
import styles from '../../styles/admin/Suppliers.module.css';

interface Supplier {
  id?: string;
  name: string;
  contact_person: string;
  email: string;
  phone: string;
  address: string;
  website: string;
  payment_terms: string;
  lead_time_days: number;
  minimum_order_amount: number;
  is_active: boolean;
  notes: string;
}

interface SupplierFormProps {
  supplier?: Supplier;
  isEditing?: boolean;
}

export default function SupplierForm({ supplier, isEditing = false }: SupplierFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Supplier>({
    name: supplier?.name || '',
    contact_person: supplier?.contact_person || '',
    email: supplier?.email || '',
    phone: supplier?.phone || '',
    address: supplier?.address || '',
    website: supplier?.website || '',
    payment_terms: supplier?.payment_terms || 'Net 30',
    lead_time_days: supplier?.lead_time_days || 7,
    minimum_order_amount: supplier?.minimum_order_amount || 0,
    is_active: supplier?.is_active !== undefined ? supplier.is_active : true,
    notes: supplier?.notes || ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : 
               type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
               value
    }));
  };

  const validateForm = () => {
    const errors: string[] = [];

    if (!formData.name.trim()) {
      errors.push('Supplier name is required');
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.push('Invalid email format');
    }

    if (formData.lead_time_days < 0) {
      errors.push('Lead time cannot be negative');
    }

    if (formData.minimum_order_amount < 0) {
      errors.push('Minimum order amount cannot be negative');
    }

    if (formData.website && !formData.website.startsWith('http')) {
      setFormData(prev => ({
        ...prev,
        website: `https://${formData.website}`
      }));
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const errors = validateForm();
    if (errors.length > 0) {
      errors.forEach(error => toast.error(error));
      return;
    }

    setLoading(true);

    try {
      const token = localStorage.getItem('adminToken');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const url = isEditing ? `/api/admin/suppliers/${supplier?.id}` : '/api/admin/suppliers';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          contactPerson: formData.contact_person.trim(),
          email: formData.email.trim() || null,
          phone: formData.phone.trim() || null,
          address: formData.address.trim() || null,
          website: formData.website.trim() || null,
          paymentTerms: formData.payment_terms,
          leadTimeDays: formData.lead_time_days,
          minimumOrderAmount: formData.minimum_order_amount,
          isActive: formData.is_active,
          notes: formData.notes.trim() || null
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${isEditing ? 'update' : 'create'} supplier`);
      }

      const data = await response.json();
      
      toast.success(`Supplier ${isEditing ? 'updated' : 'created'} successfully`);
      
      // Redirect to supplier details or suppliers list
      if (isEditing) {
        router.push(`/admin/suppliers/${supplier?.id}`);
      } else {
        router.push('/admin/suppliers');
      }

    } catch (error) {
      console.error('Error saving supplier:', error);
      toast.error(error instanceof Error ? error.message : `Failed to ${isEditing ? 'update' : 'create'} supplier`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.supplierForm}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h1 className={styles.title}>
            {isEditing ? 'Edit Supplier' : 'Add New Supplier'}
          </h1>
          <p className={styles.subtitle}>
            {isEditing 
              ? 'Update supplier information and business details'
              : 'Enter supplier information and business details'
            }
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className={styles.formGrid}>
          {/* Basic Information */}
          <div className={styles.formGroup}>
            <label htmlFor="name" className={styles.formLabel}>
              Supplier Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={styles.formInput}
              required
              placeholder="Enter supplier name"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="contact_person" className={styles.formLabel}>
              Contact Person
            </label>
            <input
              type="text"
              id="contact_person"
              name="contact_person"
              value={formData.contact_person}
              onChange={handleInputChange}
              className={styles.formInput}
              placeholder="Primary contact name"
            />
          </div>

          {/* Contact Information */}
          <div className={styles.formGroup}>
            <label htmlFor="email" className={styles.formLabel}>
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={styles.formInput}
              placeholder="<EMAIL>"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="phone" className={styles.formLabel}>
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className={styles.formInput}
              placeholder="+61 2 1234 5678"
            />
          </div>

          {/* Address and Website */}
          <div className={styles.formGroup}>
            <label htmlFor="address" className={styles.formLabel}>
              Address
            </label>
            <input
              type="text"
              id="address"
              name="address"
              value={formData.address}
              onChange={handleInputChange}
              className={styles.formInput}
              placeholder="Business address"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="website" className={styles.formLabel}>
              Website
            </label>
            <input
              type="url"
              id="website"
              name="website"
              value={formData.website}
              onChange={handleInputChange}
              className={styles.formInput}
              placeholder="www.supplier.com"
            />
          </div>

          {/* Business Terms */}
          <div className={styles.formGroup}>
            <label htmlFor="payment_terms" className={styles.formLabel}>
              Payment Terms
            </label>
            <select
              id="payment_terms"
              name="payment_terms"
              value={formData.payment_terms}
              onChange={handleInputChange}
              className={styles.formSelect}
            >
              <option value="Net 15">Net 15</option>
              <option value="Net 30">Net 30</option>
              <option value="Net 45">Net 45</option>
              <option value="Net 60">Net 60</option>
              <option value="COD">Cash on Delivery</option>
              <option value="Prepaid">Prepaid</option>
            </select>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="lead_time_days" className={styles.formLabel}>
              Lead Time (Days)
            </label>
            <input
              type="number"
              id="lead_time_days"
              name="lead_time_days"
              value={formData.lead_time_days}
              onChange={handleInputChange}
              className={styles.formInput}
              min="0"
              placeholder="7"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="minimum_order_amount" className={styles.formLabel}>
              Minimum Order Amount (AUD)
            </label>
            <input
              type="number"
              id="minimum_order_amount"
              name="minimum_order_amount"
              value={formData.minimum_order_amount}
              onChange={handleInputChange}
              className={styles.formInput}
              min="0"
              step="0.01"
              placeholder="0.00"
            />
          </div>

          {/* Status */}
          <div className={styles.formGroup}>
            <label className={styles.formLabel}>
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                style={{ marginRight: '0.5rem' }}
              />
              Active Supplier
            </label>
          </div>

          {/* Notes */}
          <div className={`${styles.formGroup} ${styles.fullWidth}`}>
            <label htmlFor="notes" className={styles.formLabel}>
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              className={styles.formTextarea}
              placeholder="Additional notes about this supplier..."
              rows={4}
            />
          </div>
        </div>

        <div className={styles.formActions}>
          <Link href="/admin/suppliers" className={styles.cancelBtn}>
            Cancel
          </Link>
          <button 
            type="submit" 
            className={styles.saveBtn}
            disabled={loading}
          >
            {loading ? 'Saving...' : (isEditing ? 'Update Supplier' : 'Create Supplier')}
          </button>
        </div>
      </form>
    </div>
  );
}
