import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken, adminLogout } from '../../../lib/auth/admin-auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get token from cookie or header
    const token = req.cookies['admin-token'] || 
                 req.headers.authorization?.replace('Bearer ', '');

    if (token) {
      // Verify token and get user info for audit logging
      const authResult = await verifyAdminToken(token);
      
      if (authResult.valid && authResult.user) {
        // Get client IP for audit logging
        const clientIP = req.headers['x-forwarded-for'] as string || 
                        req.headers['x-real-ip'] as string || 
                        req.connection.remoteAddress || 
                        'unknown';

        // Log the logout
        await adminLogout(authResult.user.id, clientIP);
      }
    }

    // Clear the cookie
    res.setHeader('Set-Cookie', [
      'admin-token=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0'
    ]);

    return res.status(200).json({ success: true, message: 'Logged out successfully' });

  } catch (error) {
    console.error('Logout API error:', error);
    
    // Still clear the cookie even if there's an error
    res.setHeader('Set-Cookie', [
      'admin-token=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0'
    ]);
    
    return res.status(200).json({ success: true, message: 'Logged out' });
  }
}
