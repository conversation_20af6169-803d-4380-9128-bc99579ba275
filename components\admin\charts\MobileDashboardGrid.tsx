/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Dashboard Grid
 * Responsive grid layout for dashboard charts and widgets on mobile devices
 */

import React, { useState, useEffect } from 'react';
import styles from './MobileDashboardGrid.module.css';

export interface DashboardWidget {
  id: string;
  title: string;
  component: React.ReactNode;
  size: 'small' | 'medium' | 'large' | 'full';
  priority: number;
  category?: string;
  refreshable?: boolean;
  exportable?: boolean;
}

export interface MobileDashboardGridProps {
  widgets: DashboardWidget[];
  columns?: number;
  gap?: number;
  className?: string;
  loading?: boolean;
  onWidgetRefresh?: (widgetId: string) => void;
  onWidgetExport?: (widgetId: string) => void;
  onWidgetReorder?: (widgets: DashboardWidget[]) => void;
  customizable?: boolean;
  'data-testid'?: string;
}

export const MobileDashboardGrid: React.FC<MobileDashboardGridProps> = ({
  widgets,
  columns = 1,
  gap = 16,
  className = '',
  loading = false,
  onWidgetRefresh,
  onWidgetExport,
  onWidgetReorder,
  customizable = false,
  'data-testid': testId,
}) => {
  const [orderedWidgets, setOrderedWidgets] = useState(widgets);
  const [draggedWidget, setDraggedWidget] = useState<string | null>(null);
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');

  // Detect screen size for responsive columns
  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setScreenSize('mobile');
      } else if (width < 1024) {
        setScreenSize('tablet');
      } else {
        setScreenSize('desktop');
      }
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  // Update ordered widgets when props change
  useEffect(() => {
    setOrderedWidgets(widgets.sort((a, b) => a.priority - b.priority));
  }, [widgets]);

  // Responsive column calculation
  const getColumns = () => {
    if (screenSize === 'mobile') return 1;
    if (screenSize === 'tablet') return Math.min(columns, 2);
    return columns;
  };

  const handleWidgetRefresh = (widgetId: string) => {
    onWidgetRefresh?.(widgetId);
  };

  const handleWidgetExport = (widgetId: string) => {
    onWidgetExport?.(widgetId);
  };

  // Drag and drop handlers for customization
  const handleDragStart = (e: React.DragEvent, widgetId: string) => {
    if (!customizable) return;
    setDraggedWidget(widgetId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (!customizable) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetWidgetId: string) => {
    if (!customizable || !draggedWidget) return;
    
    e.preventDefault();
    
    const newWidgets = [...orderedWidgets];
    const draggedIndex = newWidgets.findIndex(w => w.id === draggedWidget);
    const targetIndex = newWidgets.findIndex(w => w.id === targetWidgetId);
    
    if (draggedIndex !== -1 && targetIndex !== -1) {
      const [draggedItem] = newWidgets.splice(draggedIndex, 1);
      newWidgets.splice(targetIndex, 0, draggedItem);
      
      setOrderedWidgets(newWidgets);
      onWidgetReorder?.(newWidgets);
    }
    
    setDraggedWidget(null);
  };

  const handleDragEnd = () => {
    setDraggedWidget(null);
  };

  const getWidgetSize = (widget: DashboardWidget) => {
    const cols = getColumns();
    
    switch (widget.size) {
      case 'small':
        return cols === 1 ? 1 : 1;
      case 'medium':
        return cols === 1 ? 1 : Math.min(2, cols);
      case 'large':
        return cols === 1 ? 1 : Math.min(3, cols);
      case 'full':
        return cols;
      default:
        return 1;
    }
  };

  const containerClasses = [
    styles.container,
    loading && styles.loading,
    customizable && styles.customizable,
    className
  ].filter(Boolean).join(' ');

  const gridStyle = {
    gridTemplateColumns: `repeat(${getColumns()}, 1fr)`,
    gap: `${gap}px`,
  };

  return (
    <div className={containerClasses} data-testid={testId}>
      {loading && (
        <div className={styles.loadingOverlay}>
          <div className={styles.spinner} />
          <p>Loading dashboard...</p>
        </div>
      )}
      
      <div className={styles.grid} style={gridStyle}>
        {orderedWidgets.map((widget) => (
          <div
            key={widget.id}
            className={[
              styles.widget,
              styles[`size-${widget.size}`],
              draggedWidget === widget.id && styles.dragging
            ].filter(Boolean).join(' ')}
            style={{
              gridColumn: `span ${getWidgetSize(widget)}`,
            }}
            draggable={customizable}
            onDragStart={(e) => handleDragStart(e, widget.id)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, widget.id)}
            onDragEnd={handleDragEnd}
          >
            <div className={styles.widgetHeader}>
              <h3 className={styles.widgetTitle}>{widget.title}</h3>
              
              <div className={styles.widgetActions}>
                {widget.refreshable && (
                  <button
                    className={styles.actionButton}
                    onClick={() => handleWidgetRefresh(widget.id)}
                    aria-label={`Refresh ${widget.title}`}
                  >
                    🔄
                  </button>
                )}
                
                {widget.exportable && (
                  <button
                    className={styles.actionButton}
                    onClick={() => handleWidgetExport(widget.id)}
                    aria-label={`Export ${widget.title}`}
                  >
                    📥
                  </button>
                )}
                
                {customizable && (
                  <div className={styles.dragHandle} aria-label="Drag to reorder">
                    ⋮⋮
                  </div>
                )}
              </div>
            </div>
            
            <div className={styles.widgetContent}>
              {widget.component}
            </div>
          </div>
        ))}
      </div>
      
      {orderedWidgets.length === 0 && !loading && (
        <div className={styles.emptyState}>
          <div className={styles.emptyIcon}>📊</div>
          <h3 className={styles.emptyTitle}>No widgets to display</h3>
          <p className={styles.emptyMessage}>
            Add some widgets to see your dashboard data here.
          </p>
        </div>
      )}
    </div>
  );
};

// Widget wrapper component for easier integration
export const DashboardWidget: React.FC<{
  title: string;
  children: React.ReactNode;
  size?: DashboardWidget['size'];
  className?: string;
  loading?: boolean;
  error?: string;
}> = ({ 
  title, 
  children, 
  size = 'medium', 
  className = '', 
  loading = false, 
  error 
}) => {
  const widgetClasses = [
    styles.standaloneWidget,
    styles[`size-${size}`],
    loading && styles.loading,
    error && styles.error,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={widgetClasses}>
      <div className={styles.widgetHeader}>
        <h3 className={styles.widgetTitle}>{title}</h3>
      </div>
      
      <div className={styles.widgetContent}>
        {loading ? (
          <div className={styles.widgetLoading}>
            <div className={styles.spinner} />
            <p>Loading...</p>
          </div>
        ) : error ? (
          <div className={styles.widgetError}>
            <div className={styles.errorIcon}>⚠️</div>
            <p>{error}</p>
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
};

export default MobileDashboardGrid;
