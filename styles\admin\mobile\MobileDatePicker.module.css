/**
 * Ocean Soul Sparkles Admin - Mobile Date Picker Styles
 * Touch-friendly date and time selection optimized for mobile devices
 */

.datePickerContainer {
  position: relative;
  width: 100%;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--admin-text-primary, #1a1a1a);
  margin-bottom: 6px;
}

.required {
  color: #f44336;
  margin-left: 2px;
}

.nativeInput {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  pointer-events: none;
  z-index: -1;
}

.displayInput {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 2px solid var(--admin-border, #e0e0e0);
  border-radius: 8px;
  background: var(--admin-card-background, #ffffff);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px; /* Touch-friendly minimum height */
}

.displayInput:hover {
  border-color: var(--admin-primary, #16213e);
}

.displayInput.focused {
  border-color: var(--admin-primary, #16213e);
  box-shadow: 0 0 0 3px rgba(22, 33, 62, 0.1);
}

.displayInput.disabled {
  background: var(--admin-background, #f8f9fa);
  color: var(--admin-text-secondary, #666666);
  cursor: not-allowed;
  opacity: 0.6;
}

.displayValue {
  flex: 1;
  font-size: 1rem;
  color: var(--admin-text-primary, #1a1a1a);
}

.displayInput.disabled .displayValue {
  color: var(--admin-text-secondary, #666666);
}

.icon {
  font-size: 1.25rem;
  margin-left: 8px;
  opacity: 0.7;
}

/* Custom Picker Overlay */
.customPickerOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.customPicker {
  background: var(--admin-card-background, #ffffff);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

/* Date Picker */
.datePicker {
  padding: 20px;
}

.monthHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.navButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--admin-primary, #16213e);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.navButton:hover {
  background: var(--admin-hover-background, #f5f5f5);
}

.monthTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  margin: 0;
}

.daysHeader {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.dayHeader {
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--admin-text-secondary, #666666);
  padding: 8px 4px;
}

.daysGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.dayButton {
  background: none;
  border: none;
  padding: 12px 4px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--admin-text-primary, #1a1a1a);
  transition: all 0.2s ease;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dayButton:hover {
  background: var(--admin-hover-background, #f5f5f5);
}

.dayButton.selected {
  background: var(--admin-primary, #16213e);
  color: white;
}

.dayButton.disabled {
  color: var(--admin-text-disabled, #cccccc);
  cursor: not-allowed;
}

.dayButton.disabled:hover {
  background: none;
}

/* Time Picker */
.timePicker {
  padding: 20px;
}

.timeInputs {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.timeInput {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.timeInput label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--admin-text-secondary, #666666);
}

.timeInput input {
  width: 60px;
  padding: 8px;
  border: 2px solid var(--admin-border, #e0e0e0);
  border-radius: 6px;
  text-align: center;
  font-size: 1rem;
  font-weight: 500;
}

.timeInput input:focus {
  outline: none;
  border-color: var(--admin-primary, #16213e);
  box-shadow: 0 0 0 3px rgba(22, 33, 62, 0.1);
}

.timeSeparator {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--admin-text-primary, #1a1a1a);
  margin-top: 20px;
}

.confirmButton {
  width: 100%;
  padding: 12px;
  background: var(--admin-primary, #16213e);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.confirmButton:hover {
  background: var(--admin-primary-dark, #0f1419);
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .displayInput {
    padding: 14px 16px;
    min-height: 52px;
  }

  .customPickerOverlay {
    padding: 16px;
  }

  .customPicker {
    max-height: 90vh;
  }

  .datePicker,
  .timePicker {
    padding: 16px;
  }

  .dayButton {
    min-height: 44px;
    font-size: 1rem;
  }

  .timeInput input {
    width: 70px;
    padding: 12px 8px;
    font-size: 1.125rem;
  }

  .timeSeparator {
    font-size: 1.75rem;
  }

  .confirmButton {
    padding: 16px;
    font-size: 1.125rem;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .displayInput {
    padding: 12px 14px;
    min-height: 48px;
  }

  .customPickerOverlay {
    padding: 12px;
  }

  .datePicker,
  .timePicker {
    padding: 12px;
  }

  .monthHeader {
    margin-bottom: 16px;
  }

  .monthTitle {
    font-size: 1rem;
  }

  .dayButton {
    min-height: 40px;
    padding: 8px 4px;
  }

  .timeInputs {
    gap: 12px;
  }

  .timeInput input {
    width: 60px;
    padding: 10px 6px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .displayInput {
    background: var(--admin-card-background-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
  }

  .displayInput:hover,
  .displayInput.focused {
    border-color: var(--admin-primary, #16213e);
  }

  .displayValue {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .customPicker {
    background: var(--admin-card-background-dark, #2a2a2a);
  }

  .monthTitle {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .dayButton {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .dayButton:hover {
    background: var(--admin-hover-background-dark, #3a3a3a);
  }

  .timeInput input {
    background: var(--admin-card-background-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .timeSeparator {
    color: var(--admin-text-primary-dark, #ffffff);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .displayInput {
    border-width: 3px;
  }

  .dayButton.selected {
    border: 2px solid white;
  }

  .timeInput input {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .displayInput,
  .navButton,
  .dayButton,
  .confirmButton {
    transition: none;
  }
}

/* Focus styles for accessibility */
.displayInput:focus-within,
.navButton:focus,
.dayButton:focus,
.confirmButton:focus {
  outline: 2px solid var(--admin-primary, #16213e);
  outline-offset: 2px;
}

.displayInput:focus-within:not(:focus-visible),
.navButton:focus:not(:focus-visible),
.dayButton:focus:not(:focus-visible),
.confirmButton:focus:not(:focus-visible) {
  outline: none;
}
