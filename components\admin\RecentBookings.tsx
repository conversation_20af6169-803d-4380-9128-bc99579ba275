import Link from 'next/link';
import styles from '../../styles/admin/RecentBookings.module.css';

interface Booking {
  id: string;
  customerName: string;
  serviceName: string;
  artistName?: string;
  scheduledDate: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  totalAmount: number;
  duration: number;
}

interface RecentBookingsProps {
  bookings?: Booking[];
  userRole: 'DEV' | 'Admin' | 'Artist' | 'Braider';
}

export default function RecentBookings({ bookings, userRole }: RecentBookingsProps) {
  const sampleBookings: Booking[] = bookings || [
    {
      id: '1',
      customerName: '<PERSON>',
      serviceName: 'Festival Face Paint',
      artistName: '<PERSON>',
      scheduledDate: '2024-01-15T14:00:00Z',
      status: 'confirmed',
      totalAmount: 85,
      duration: 60
    },
    {
      id: '2',
      customerName: '<PERSON>',
      serviceName: 'Hair Braiding - Complex',
      artistName: '<PERSON>',
      scheduledDate: '2024-01-15T16:30:00Z',
      status: 'pending',
      totalAmount: 120,
      duration: 90
    },
    {
      id: '3',
      customerName: '<PERSON>',
      serviceName: 'Glitter Art Design',
      artistName: '<PERSON>',
      scheduledDate: '2024-01-14T13:00:00<PERSON>',
      status: 'completed',
      totalAmount: 65,
      duration: 45
    },
    {
      id: '4',
      customerName: 'James <PERSON>',
      serviceName: 'Body Art - Medium',
      artistName: 'Emma Wilson',
      scheduledDate: '2024-01-14T10:00:00Z',
      status: 'completed',
      totalAmount: 150,
      duration: 120
    },
    {
      id: '5',
      customerName: 'Anna Martinez',
      serviceName: 'Special Event Package',
      artistName: 'Lisa Brown',
      scheduledDate: '2024-01-13T15:00:00Z',
      status: 'cancelled',
      totalAmount: 200,
      duration: 180
    }
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#17a2b8';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return '✓';
      case 'pending': return '⏳';
      case 'completed': return '🎉';
      case 'cancelled': return '✕';
      default: return '?';
    }
  };

  return (
    <div className={styles.recentBookingsContainer}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <h2 className={styles.sectionTitle}>Recent Bookings</h2>
          <p className={styles.sectionSubtitle}>Latest booking activity</p>
        </div>
        <div className={styles.headerRight}>
          <Link href="/admin/bookings" className={styles.viewAllButton}>
            View All
          </Link>
        </div>
      </div>

      <div className={styles.bookingsContainer}>
        {sampleBookings.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>📅</div>
            <h3>No recent bookings</h3>
            <p>New bookings will appear here</p>
            <Link href="/admin/bookings/new" className={styles.createButton}>
              Create New Booking
            </Link>
          </div>
        ) : (
          <div className={styles.bookingsList}>
            {sampleBookings.map((booking) => (
              <div key={booking.id} className={styles.bookingCard}>
                <div className={styles.bookingHeader}>
                  <div className={styles.customerInfo}>
                    <div className={styles.customerAvatar}>
                      {booking.customerName.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div className={styles.customerDetails}>
                      <div className={styles.customerName}>{booking.customerName}</div>
                      <div className={styles.bookingDate}>{formatDate(booking.scheduledDate)}</div>
                    </div>
                  </div>
                  <div 
                    className={styles.statusBadge}
                    style={{ backgroundColor: getStatusColor(booking.status) }}
                  >
                    <span className={styles.statusIcon}>{getStatusIcon(booking.status)}</span>
                    <span className={styles.statusText}>{booking.status}</span>
                  </div>
                </div>

                <div className={styles.bookingContent}>
                  <div className={styles.serviceInfo}>
                    <div className={styles.serviceName}>{booking.serviceName}</div>
                    {booking.artistName && (
                      <div className={styles.artistName}>with {booking.artistName}</div>
                    )}
                  </div>
                  
                  <div className={styles.bookingMeta}>
                    <div className={styles.metaItem}>
                      <span className={styles.metaIcon}>⏱️</span>
                      <span className={styles.metaText}>{booking.duration} min</span>
                    </div>
                    <div className={styles.metaItem}>
                      <span className={styles.metaIcon}>💰</span>
                      <span className={styles.metaText}>{formatCurrency(booking.totalAmount)}</span>
                    </div>
                  </div>
                </div>

                <div className={styles.bookingActions}>
                  <Link 
                    href={`/admin/bookings/${booking.id}`}
                    className={styles.viewButton}
                  >
                    View Details
                  </Link>
                  
                  {booking.status === 'pending' && (
                    <button className={styles.confirmButton}>
                      Confirm
                    </button>
                  )}
                  
                  {(userRole === 'DEV' || userRole === 'Admin') && (
                    <button className={styles.editButton}>
                      Edit
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className={styles.quickStats}>
        <div className={styles.statItem}>
          <div className={styles.statValue}>
            {sampleBookings.filter(b => b.status === 'pending').length}
          </div>
          <div className={styles.statLabel}>Pending</div>
        </div>
        <div className={styles.statItem}>
          <div className={styles.statValue}>
            {sampleBookings.filter(b => b.status === 'confirmed').length}
          </div>
          <div className={styles.statLabel}>Confirmed</div>
        </div>
        <div className={styles.statItem}>
          <div className={styles.statValue}>
            {formatCurrency(sampleBookings.reduce((sum, b) => sum + b.totalAmount, 0))}
          </div>
          <div className={styles.statLabel}>Total Value</div>
        </div>
      </div>
    </div>
  );
}
