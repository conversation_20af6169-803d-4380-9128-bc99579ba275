/**
 * Ocean Soul Sparkles Admin - Mobile Image Styles
 * Responsive images optimized for mobile screens with lazy loading
 */

.mobileImage {
  max-width: 100%;
  height: auto;
  display: block;
  transition: opacity 0.3s ease;
  opacity: 0;
  background: var(--admin-background, #f8f9fa);
  border-radius: 4px;
}

.mobileImage.loaded {
  opacity: 1;
}

.mobileImage.error {
  opacity: 0.7;
  filter: grayscale(100%);
}

/* Image Gallery */
.imageGallery {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  width: 100%;
}

.galleryItem {
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  background: var(--admin-background, #f8f9fa);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.galleryItem:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.galleryItem:active {
  transform: scale(0.98);
}

.galleryImage {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
}

.imageCaption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 8px 12px 12px 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Avatar Component */
.avatar {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  background: var(--admin-background, #f8f9fa);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar.clickable {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.avatar.clickable:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.avatar.clickable:active {
  transform: scale(0.95);
}

.avatar.small {
  width: 32px;
  height: 32px;
}

.avatar.medium {
  width: 48px;
  height: 48px;
}

.avatar.large {
  width: 64px;
  height: 64px;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .imageGallery {
    gap: 6px;
  }

  .galleryImage {
    height: 100px;
  }

  .imageCaption {
    padding: 6px 10px 10px 10px;
    font-size: 0.8rem;
  }

  .galleryItem:hover {
    transform: none;
    box-shadow: none;
  }

  .galleryItem:active {
    transform: scale(0.95);
  }

  .avatar.clickable:hover {
    transform: none;
    box-shadow: none;
  }

  .avatar.clickable:active {
    transform: scale(0.9);
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .imageGallery {
    gap: 4px;
  }

  .galleryImage {
    height: 80px;
  }

  .imageCaption {
    padding: 4px 8px 8px 8px;
    font-size: 0.75rem;
  }

  .avatar.small {
    width: 28px;
    height: 28px;
  }

  .avatar.medium {
    width: 40px;
    height: 40px;
  }

  .avatar.large {
    width: 56px;
    height: 56px;
  }
}

/* Single column layout for very small screens */
@media (max-width: 320px) {
  .imageGallery {
    grid-template-columns: 1fr;
  }

  .galleryImage {
    height: 120px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .mobileImage {
    background: var(--admin-background-dark, #1a1a1a);
  }

  .galleryItem {
    background: var(--admin-background-dark, #1a1a1a);
  }

  .avatar {
    background: var(--admin-background-dark, #1a1a1a);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .galleryItem {
    border: 2px solid var(--admin-border, #000000);
  }

  .avatar {
    border: 2px solid var(--admin-border, #000000);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .mobileImage {
    transition: none;
  }

  .galleryItem {
    transition: none;
  }

  .galleryItem:hover,
  .galleryItem:active {
    transform: none;
  }

  .avatar.clickable {
    transition: none;
  }

  .avatar.clickable:hover,
  .avatar.clickable:active {
    transform: none;
  }
}

/* Loading states */
.mobileImage[data-loading="true"] {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Error states */
.mobileImage[data-error="true"] {
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 0.875rem;
  text-align: center;
  padding: 20px;
}

.mobileImage[data-error="true"]::before {
  content: "⚠️ Failed to load";
}

/* Focus styles for accessibility */
.galleryItem:focus,
.avatar.clickable:focus {
  outline: 2px solid var(--admin-primary, #16213e);
  outline-offset: 2px;
}

.galleryItem:focus:not(:focus-visible),
.avatar.clickable:focus:not(:focus-visible) {
  outline: none;
}

/* Print styles */
@media print {
  .galleryItem:hover,
  .galleryItem:active {
    transform: none;
    box-shadow: none;
  }

  .avatar.clickable:hover,
  .avatar.clickable:active {
    transform: none;
    box-shadow: none;
  }

  .imageCaption {
    background: rgba(0, 0, 0, 0.8);
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}
