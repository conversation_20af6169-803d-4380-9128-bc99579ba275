import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const requestId = uuidv4();
  const { id: artistId } = req.query;
  
  try {
    // Authentication check
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid authentication token required',
        requestId
      });
    }

    // Verify artist exists
    const { data: artist, error: artistError } = await supabase
      .from('artist_profiles')
      .select('id, name, email, commission_rate, total_revenue')
      .eq('id', artistId)
      .single();

    if (artistError || !artist) {
      return res.status(404).json({
        error: 'Artist not found',
        message: 'The specified artist does not exist',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { 
        status, 
        start_date, 
        end_date,
        payment_method,
        period = 'all', // 'week', 'month', 'quarter', 'year', 'all'
        limit = 50, 
        offset = 0 
      } = req.query;

      let query = supabase
        .from('commission_transactions')
        .select(`
          id,
          artist_id,
          booking_id,
          payment_id,
          service_amount,
          commission_rate,
          commission_amount,
          tip_amount,
          total_earnings,
          status,
          payment_method,
          paid_at,
          paid_by,
          notes,
          created_at,
          updated_at,
          bookings!inner(
            id,
            start_time,
            end_time,
            status as booking_status,
            total_amount,
            customers(
              id,
              first_name,
              last_name,
              email
            ),
            services(
              id,
              name,
              category
            )
          ),
          payments(
            id,
            amount,
            method,
            status as payment_status,
            payment_time
          ),
          payer:admin_users!paid_by(
            id,
            first_name,
            last_name,
            email
          )
        `)
        .eq('artist_id', artistId)
        .order('created_at', { ascending: false });

      // Apply date filters based on period
      const now = new Date();
      let periodStart: string | undefined;

      switch (period) {
        case 'week':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          periodStart = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          periodStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
          break;
        case 'quarter':
          const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
          periodStart = quarterStart.toISOString().split('T')[0];
          break;
        case 'year':
          periodStart = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0];
          break;
      }

      // Apply filters
      if (status) {
        query = query.eq('status', status);
      }
      if (payment_method) {
        query = query.eq('payment_method', payment_method);
      }
      if (start_date || periodStart) {
        query = query.gte('created_at', start_date || periodStart);
      }
      if (end_date) {
        query = query.lte('created_at', end_date);
      }

      // Apply pagination
      query = query.range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1);

      const { data: commissions, error } = await query;

      if (error) {
        console.error('Artist commissions fetch error:', error);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to fetch artist commission transactions',
          requestId
        });
      }

      // Get total count for pagination
      const { count: totalCount } = await supabase
        .from('commission_transactions')
        .select('*', { count: 'exact', head: true })
        .eq('artist_id', artistId);

      // Calculate detailed statistics
      const allCommissions = (commissions || []) as any[];
      const stats = {
        total: allCommissions.length,
        totalEarnings: allCommissions.reduce((sum, c) => sum + (c.total_earnings || 0), 0),
        totalCommissions: allCommissions.reduce((sum, c) => sum + (c.commission_amount || 0), 0),
        totalTips: allCommissions.reduce((sum, c) => sum + (c.tip_amount || 0), 0),
        totalServiceAmount: allCommissions.reduce((sum, c) => sum + (c.service_amount || 0), 0),
        averageCommissionRate: allCommissions.length > 0
          ? allCommissions.reduce((sum, c) => sum + (c.commission_rate || 0), 0) / allCommissions.length
          : artist.commission_rate || 0,
        averageEarningsPerBooking: allCommissions.length > 0
          ? allCommissions.reduce((sum, c) => sum + (c.total_earnings || 0), 0) / allCommissions.length
          : 0,
        statusBreakdown: {
          pending: allCommissions.filter(c => c.status === 'pending').length,
          calculated: allCommissions.filter(c => c.status === 'calculated').length,
          paid: allCommissions.filter(c => c.status === 'paid').length,
          disputed: allCommissions.filter(c => c.status === 'disputed').length
        },
        paymentMethodBreakdown: {
          cash: allCommissions.filter(c => c.payment_method === 'cash').length,
          bank_transfer: allCommissions.filter(c => c.payment_method === 'bank_transfer').length,
          payroll: allCommissions.filter(c => c.payment_method === 'payroll').length,
          unpaid: allCommissions.filter(c => !c.payment_method).length
        }
      };

      // Get monthly earnings trend (last 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      
      const { data: monthlyData } = await supabase
        .from('commission_transactions')
        .select('total_earnings, commission_amount, tip_amount, created_at')
        .eq('artist_id', artistId)
        .gte('created_at', sixMonthsAgo.toISOString())
        .order('created_at', { ascending: true });

      // Group by month
      const monthlyTrend = (monthlyData as any[])?.reduce((acc: any, transaction: any) => {
        const month = new Date(transaction.created_at).toISOString().slice(0, 7); // YYYY-MM
        if (!acc[month]) {
          acc[month] = {
            month,
            totalEarnings: 0,
            totalCommissions: 0,
            totalTips: 0,
            count: 0
          };
        }
        acc[month].totalEarnings += transaction.total_earnings || 0;
        acc[month].totalCommissions += transaction.commission_amount || 0;
        acc[month].totalTips += transaction.tip_amount || 0;
        acc[month].count += 1;
        return acc;
      }, {}) || {};

      const monthlyTrendArray = Object.values(monthlyTrend);

      return res.status(200).json({
        artist,
        commissions: allCommissions,
        stats,
        monthlyTrend: monthlyTrendArray,
        pagination: {
          total: totalCount || 0,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: (parseInt(offset as string) + parseInt(limit as string)) < (totalCount || 0)
        },
        requestId
      });
    }

    if (req.method === 'POST') {
      // Auto-calculate commissions for unpaid bookings
      const { auto_calculate = false, booking_ids = [] } = req.body;

      if (auto_calculate) {
        // Get completed bookings without commission transactions
        let bookingsQuery = supabase
          .from('bookings')
          .select(`
            id,
            total_amount,
            start_time,
            status,
            payments(
              id,
              amount,
              status,
              tip_amount
            )
          `)
          .eq('artist_id', artistId)
          .eq('status', 'completed')
          .not('id', 'in', `(${
            (await supabase
              .from('commission_transactions')
              .select('booking_id')
              .eq('artist_id', artistId)
            ).data?.map(c => `'${c.booking_id}'`).join(',') || "''"
          })`);

        if (booking_ids.length > 0) {
          bookingsQuery = bookingsQuery.in('id', booking_ids);
        }

        const { data: unpaidBookings, error: bookingsError } = await bookingsQuery;

        if (bookingsError) {
          console.error('Unpaid bookings fetch error:', bookingsError);
          return res.status(500).json({
            error: 'Database error',
            message: 'Failed to fetch unpaid bookings',
            requestId
          });
        }

        const newCommissions = [];
        for (const booking of unpaidBookings || []) {
          const payment = booking.payments?.[0];
          if (payment && payment.status === 'completed') {
            const serviceAmount = payment.amount || booking.total_amount || 0;
            const commissionRate = artist.commission_rate || 30;
            const commissionAmount = (serviceAmount * commissionRate) / 100;
            const tipAmount = payment.tip_amount || 0;
            const totalEarnings = commissionAmount + tipAmount;

            newCommissions.push({
              id: uuidv4(),
              artist_id: artistId,
              booking_id: booking.id,
              payment_id: payment.id,
              service_amount: serviceAmount,
              commission_rate: commissionRate,
              commission_amount: commissionAmount,
              tip_amount: tipAmount,
              total_earnings: totalEarnings,
              status: 'calculated',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
          }
        }

        if (newCommissions.length > 0) {
          const { data: createdCommissions, error: createError } = await supabase
            .from('commission_transactions')
            .insert(newCommissions)
            .select();

          if (createError) {
            console.error('Auto-commission creation error:', createError);
            return res.status(500).json({
              error: 'Database error',
              message: 'Failed to create commission transactions',
              requestId
            });
          }

          return res.status(201).json({
            commissions: createdCommissions,
            message: `${newCommissions.length} commission transactions created successfully`,
            requestId
          });
        } else {
          return res.status(200).json({
            commissions: [],
            message: 'No unpaid bookings found for commission calculation',
            requestId
          });
        }
      }

      return res.status(400).json({
        error: 'Invalid request',
        message: 'Please specify auto_calculate=true to calculate commissions',
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      message: `HTTP method ${req.method} is not supported`,
      requestId
    });

  } catch (error) {
    console.error('Artist commission API error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      requestId
    });
  }
}
