import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { toast } from 'react-toastify';
import styles from '../../styles/admin/PurchaseOrders.module.css';

interface Supplier {
  id: string;
  name: string;
  contact_person: string | null;
  email: string | null;
  phone: string | null;
  payment_terms: string;
  lead_time_days: number;
  minimum_order_amount: number;
}

interface InventoryItem {
  id: string;
  name: string;
  sku: string | null;
  quantity_on_hand: number;
  min_stock_level: number;
}

interface PurchaseOrderItem {
  inventoryId: string;
  productName: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
}

export default function PurchaseOrderForm() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [selectedSupplier, setSelectedSupplier] = useState<string>('');
  const [orderDate, setOrderDate] = useState(new Date().toISOString().split('T')[0]);
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState('');
  const [notes, setNotes] = useState('');
  const [items, setItems] = useState<PurchaseOrderItem[]>([]);

  useEffect(() => {
    fetchSuppliers();
    fetchInventoryItems();
  }, []);

  const fetchSuppliers = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/suppliers?active=true', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSuppliers(data.suppliers || []);
      }
    } catch (error) {
      console.error('Error fetching suppliers:', error);
    }
  };

  const fetchInventoryItems = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/inventory?active=true', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setInventoryItems(data.inventory || []);
      }
    } catch (error) {
      console.error('Error fetching inventory items:', error);
    }
  };

  const addItem = () => {
    setItems([...items, {
      inventoryId: '',
      productName: '',
      quantity: 1,
      unitCost: 0,
      totalCost: 0
    }]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: keyof PurchaseOrderItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // Update product name when inventory item is selected
    if (field === 'inventoryId') {
      const inventoryItem = inventoryItems.find(item => item.id === value);
      if (inventoryItem) {
        updatedItems[index].productName = inventoryItem.name;
      }
    }

    // Recalculate total cost when quantity or unit cost changes
    if (field === 'quantity' || field === 'unitCost') {
      updatedItems[index].totalCost = updatedItems[index].quantity * updatedItems[index].unitCost;
    }

    setItems(updatedItems);
  };

  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + item.totalCost, 0);
  };

  const calculateTax = () => {
    return calculateSubtotal() * 0.1; // 10% GST
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax();
  };

  const validateForm = () => {
    const errors: string[] = [];

    if (!selectedSupplier) {
      errors.push('Please select a supplier');
    }

    if (!orderDate) {
      errors.push('Order date is required');
    }

    if (items.length === 0) {
      errors.push('At least one item is required');
    }

    items.forEach((item, index) => {
      if (!item.inventoryId) {
        errors.push(`Item ${index + 1}: Please select a product`);
      }
      if (item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
      }
      if (item.unitCost <= 0) {
        errors.push(`Item ${index + 1}: Unit cost must be greater than 0`);
      }
    });

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const errors = validateForm();
    if (errors.length > 0) {
      errors.forEach(error => toast.error(error));
      return;
    }

    setLoading(true);

    try {
      const token = localStorage.getItem('adminToken');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('/api/admin/purchase-orders', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          supplierId: selectedSupplier,
          orderDate,
          expectedDeliveryDate: expectedDeliveryDate || null,
          notes: notes.trim() || null,
          items: items.map(item => ({
            inventoryId: item.inventoryId,
            quantity: item.quantity,
            unitCost: item.unitCost
          }))
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create purchase order');
      }

      const data = await response.json();
      
      toast.success('Purchase order created successfully');
      router.push(`/admin/purchase-orders/${data.purchaseOrder.id}`);

    } catch (error) {
      console.error('Error creating purchase order:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create purchase order');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  return (
    <div className={styles.purchaseOrderForm}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h1 className={styles.title}>Create Purchase Order</h1>
          <p className={styles.subtitle}>Create a new purchase order for supplier deliveries</p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className={styles.formSection}>
          <h3>Order Information</h3>
          <div className={styles.formGrid}>
            <div className={styles.formGroup}>
              <label htmlFor="supplier" className={styles.formLabel}>
                Supplier *
              </label>
              <select
                id="supplier"
                value={selectedSupplier}
                onChange={(e) => setSelectedSupplier(e.target.value)}
                className={styles.formSelect}
                required
              >
                <option value="">Select a supplier</option>
                {suppliers.map((supplier) => (
                  <option key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </option>
                ))}
              </select>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="orderDate" className={styles.formLabel}>
                Order Date *
              </label>
              <input
                type="date"
                id="orderDate"
                value={orderDate}
                onChange={(e) => setOrderDate(e.target.value)}
                className={styles.formInput}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="expectedDeliveryDate" className={styles.formLabel}>
                Expected Delivery Date
              </label>
              <input
                type="date"
                id="expectedDeliveryDate"
                value={expectedDeliveryDate}
                onChange={(e) => setExpectedDeliveryDate(e.target.value)}
                className={styles.formInput}
              />
            </div>

            <div className={`${styles.formGroup} ${styles.fullWidth}`}>
              <label htmlFor="notes" className={styles.formLabel}>
                Notes
              </label>
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className={styles.formTextarea}
                placeholder="Additional notes for this purchase order..."
                rows={3}
              />
            </div>
          </div>
        </div>

        <div className={styles.formSection}>
          <div className={styles.sectionHeader}>
            <h3>Order Items</h3>
            <button
              type="button"
              onClick={addItem}
              className={styles.addItemBtn}
            >
              + Add Item
            </button>
          </div>

          {items.length === 0 ? (
            <div className={styles.emptyItems}>
              <p>No items added yet. Click "Add Item" to get started.</p>
            </div>
          ) : (
            <div className={styles.itemsList}>
              {items.map((item, index) => (
                <div key={index} className={styles.itemRow}>
                  <div className={styles.itemFields}>
                    <div className={styles.formGroup}>
                      <label className={styles.formLabel}>Product *</label>
                      <select
                        value={item.inventoryId}
                        onChange={(e) => updateItem(index, 'inventoryId', e.target.value)}
                        className={styles.formSelect}
                        required
                      >
                        <option value="">Select a product</option>
                        {inventoryItems.map((inventoryItem) => (
                          <option key={inventoryItem.id} value={inventoryItem.id}>
                            {inventoryItem.name} {inventoryItem.sku ? `(${inventoryItem.sku})` : ''}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className={styles.formGroup}>
                      <label className={styles.formLabel}>Quantity *</label>
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 0)}
                        className={styles.formInput}
                        min="1"
                        required
                      />
                    </div>

                    <div className={styles.formGroup}>
                      <label className={styles.formLabel}>Unit Cost *</label>
                      <input
                        type="number"
                        value={item.unitCost}
                        onChange={(e) => updateItem(index, 'unitCost', parseFloat(e.target.value) || 0)}
                        className={styles.formInput}
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>

                    <div className={styles.formGroup}>
                      <label className={styles.formLabel}>Total</label>
                      <div className={styles.totalCost}>
                        {formatCurrency(item.totalCost)}
                      </div>
                    </div>
                  </div>

                  <button
                    type="button"
                    onClick={() => removeItem(index)}
                    className={styles.removeItemBtn}
                    title="Remove item"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {items.length > 0 && (
          <div className={styles.orderSummary}>
            <h3>Order Summary</h3>
            <div className={styles.summaryRow}>
              <span>Subtotal:</span>
              <span>{formatCurrency(calculateSubtotal())}</span>
            </div>
            <div className={styles.summaryRow}>
              <span>Tax (10% GST):</span>
              <span>{formatCurrency(calculateTax())}</span>
            </div>
            <div className={`${styles.summaryRow} ${styles.totalRow}`}>
              <span>Total:</span>
              <span>{formatCurrency(calculateTotal())}</span>
            </div>
          </div>
        )}

        <div className={styles.formActions}>
          <Link href="/admin/purchase-orders" className={styles.cancelBtn}>
            Cancel
          </Link>
          <button 
            type="submit" 
            className={styles.saveBtn}
            disabled={loading || items.length === 0}
          >
            {loading ? 'Creating...' : 'Create Purchase Order'}
          </button>
        </div>
      </form>
    </div>
  );
}
