/**
 * POS Authentication Protection Module
 * Provides security measures for POS payment operations
 */

let paymentOperationActive = false;
let paymentTimeout = null;
let securityLockout = false;

/**
 * Start POS payment operation with security measures
 */
export function startPOSPaymentOperation() {
  try {
    if (securityLockout) {
      throw new Error('POS system is temporarily locked due to security measures');
    }

    paymentOperationActive = true;
    
    // Set timeout for payment operation (5 minutes)
    paymentTimeout = setTimeout(() => {
      endPOSPaymentOperation();
      console.warn('POS payment operation timed out for security');
    }, 5 * 60 * 1000);

    console.log('POS payment operation started with security protection');
    return true;
  } catch (error) {
    console.error('Error starting POS payment operation:', error);
    return false;
  }
}

/**
 * End POS payment operation and clear security measures
 */
export function endPOSPaymentOperation() {
  try {
    paymentOperationActive = false;
    
    if (paymentTimeout) {
      clearTimeout(paymentTimeout);
      paymentTimeout = null;
    }

    console.log('POS payment operation ended');
    return true;
  } catch (error) {
    console.error('Error ending POS payment operation:', error);
    return false;
  }
}

/**
 * Check if POS payment operation is currently active
 */
export function isPOSPaymentOperationActive() {
  return paymentOperationActive;
}

/**
 * Validate POS payment request for security
 */
export function validatePOSPaymentRequest(paymentData) {
  try {
    // Basic validation
    if (!paymentData) {
      throw new Error('Payment data is required');
    }

    if (!paymentData.amount || paymentData.amount <= 0) {
      throw new Error('Valid payment amount is required');
    }

    if (paymentData.amount > 10000) {
      throw new Error('Payment amount exceeds maximum limit');
    }

    // Check for suspicious patterns
    if (paymentData.amount === 9999.99) {
      console.warn('Suspicious payment amount detected');
    }

    return {
      valid: true,
      sanitizedData: {
        amount: Math.round(paymentData.amount * 100) / 100, // Round to 2 decimal places
        currency: paymentData.currency || 'AUD',
        description: paymentData.description?.substring(0, 100) || '', // Limit description length
        metadata: paymentData.metadata || {}
      }
    };
  } catch (error) {
    return {
      valid: false,
      error: error.message
    };
  }
}

/**
 * Log POS security event
 */
export function logPOSSecurityEvent(event, details = {}) {
  try {
    const securityLog = {
      timestamp: new Date().toISOString(),
      event,
      details,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
      ip: 'unknown' // Would be populated by server-side logging
    };

    console.log('POS Security Event:', securityLog);
    
    // In production, this would send to a security monitoring service
    return true;
  } catch (error) {
    console.error('Error logging POS security event:', error);
    return false;
  }
}

/**
 * Trigger security lockout (emergency measure)
 */
export function triggerSecurityLockout(reason = 'Security violation detected') {
  try {
    securityLockout = true;
    endPOSPaymentOperation();
    
    logPOSSecurityEvent('SECURITY_LOCKOUT', { reason });
    
    // Auto-unlock after 30 minutes
    setTimeout(() => {
      securityLockout = false;
      logPOSSecurityEvent('SECURITY_LOCKOUT_CLEARED', { reason: 'Automatic timeout' });
    }, 30 * 60 * 1000);

    console.error('POS security lockout triggered:', reason);
    return true;
  } catch (error) {
    console.error('Error triggering security lockout:', error);
    return false;
  }
}

/**
 * Check if system is in security lockout
 */
export function isSecurityLockoutActive() {
  return securityLockout;
}

/**
 * Clear security lockout (admin override)
 */
export function clearSecurityLockout() {
  try {
    securityLockout = false;
    logPOSSecurityEvent('SECURITY_LOCKOUT_CLEARED', { reason: 'Admin override' });
    console.log('POS security lockout cleared by admin');
    return true;
  } catch (error) {
    console.error('Error clearing security lockout:', error);
    return false;
  }
}

/**
 * Sanitize payment metadata for security
 */
export function sanitizePaymentMetadata(metadata) {
  try {
    if (!metadata || typeof metadata !== 'object') {
      return {};
    }

    const sanitized = {};
    const allowedKeys = ['booking_id', 'customer_id', 'service_id', 'artist_id', 'notes'];
    
    for (const key of allowedKeys) {
      if (metadata[key]) {
        // Sanitize string values
        if (typeof metadata[key] === 'string') {
          sanitized[key] = metadata[key].substring(0, 255).replace(/[<>]/g, '');
        } else {
          sanitized[key] = metadata[key];
        }
      }
    }

    return sanitized;
  } catch (error) {
    console.error('Error sanitizing payment metadata:', error);
    return {};
  }
}

/**
 * Generate secure payment reference
 */
export function generateSecurePaymentReference() {
  try {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    return `OSS-${timestamp}-${random}`.toUpperCase();
  } catch (error) {
    console.error('Error generating payment reference:', error);
    return `OSS-${Date.now()}-ERROR`;
  }
}

// Default export for backward compatibility
export default {
  startPOSPaymentOperation,
  endPOSPaymentOperation,
  isPOSPaymentOperationActive,
  validatePOSPaymentRequest,
  logPOSSecurityEvent,
  triggerSecurityLockout,
  isSecurityLockoutActive,
  clearSecurityLockout,
  sanitizePaymentMetadata,
  generateSecurePaymentReference
};
