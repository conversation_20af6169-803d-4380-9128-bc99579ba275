/**
 * Test to verify the speakeasy Edge Runtime fix
 */

describe('Speakeasy Edge Runtime Fix', () => {
  test('Edge Runtime auth module should import without errors', async () => {
    // This should not throw any speakeasy-related errors
    const { verifyAdminToken } = await import('../lib/auth/admin-auth-edge');
    expect(typeof verifyAdminToken).toBe('function');
  });

  test('Node.js auth module should still support MFA', async () => {
    // This should work in Node.js runtime (API routes)
    const { verifyMFAAndLogin, generateMFASecret, enableMFA } = await import('../lib/auth/admin-auth');
    
    expect(typeof verifyMFAAndLogin).toBe('function');
    expect(typeof generateMFASecret).toBe('function');
    expect(typeof enableMFA).toBe('function');
  });

  test('Middleware should import without speakeasy errors', async () => {
    // This should not cause util.deprecate errors
    expect(() => {
      require('../middleware');
    }).not.toThrow();
  });
});
