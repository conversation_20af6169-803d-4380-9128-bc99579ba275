import { useState, useEffect } from 'react'
import styles from '@/styles/admin/ReceiptCustomizer.module.css'

/**
 * Receipt Customizer Component
 * Allows customization of receipt templates with live preview
 */
export default function ReceiptCustomizer({ onTemplateSelect, selectedTemplate, showPreview = true }) {
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [previewData, setPreviewData] = useState(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState(null)

  useEffect(() => {
    loadTemplates()
    generatePreviewData()
  }, [])

  const loadTemplates = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/receipts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to load receipt templates')
      }

      const data = await response.json()
      setTemplates(data.templates || [])
      
      // Select default template if none selected
      if (!selectedTemplate && data.templates?.length > 0) {
        const defaultTemplate = data.templates.find(t => t.is_default) || data.templates[0]
        onTemplateSelect?.(defaultTemplate)
      }
    } catch (err) {
      console.error('Error loading templates:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const generatePreviewData = () => {
    setPreviewData({
      receipt_number: `OSS-${Date.now()}`,
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+61 400 123 456',
      service_name: 'Festival Face Paint',
      tier_name: 'Premium',
      artist_name: 'Emma Wilson',
      start_time: new Date().toISOString(),
      duration: 90,
      total_amount: 120.00,
      tip_amount: 18.00,
      payment_method: 'Card',
      notes: 'Customer requested glitter accents'
    })
  }

  const handleTemplateSelect = (template) => {
    onTemplateSelect?.(template)
  }

  const handleCreateTemplate = () => {
    setShowCreateModal(true)
  }

  const handleEditTemplate = (template) => {
    setEditingTemplate(template)
    setShowEditModal(true)
  }

  const handleDeleteTemplate = async (template) => {
    if (template.is_default) {
      alert('Cannot delete the default template')
      return
    }

    if (!confirm(`Are you sure you want to delete "${template.name}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/receipts?id=${template.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to delete template')
      }

      // Reload templates
      await loadTemplates()

      // If deleted template was selected, select default
      if (selectedTemplate?.id === template.id) {
        const defaultTemplate = templates.find(t => t.is_default) || templates[0]
        onTemplateSelect?.(defaultTemplate)
      }
    } catch (error) {
      console.error('Error deleting template:', error)
      alert('Failed to delete template. Please try again.')
    }
  }

  const handleSaveTemplate = async (templateData) => {
    try {
      const url = editingTemplate
        ? `/api/admin/receipts?id=${editingTemplate.id}`
        : '/api/admin/receipts'

      const method = editingTemplate ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify(templateData)
      })

      if (!response.ok) {
        throw new Error('Failed to save template')
      }

      // Reload templates
      await loadTemplates()

      // Close modals
      setShowCreateModal(false)
      setShowEditModal(false)
      setEditingTemplate(null)
    } catch (error) {
      console.error('Error saving template:', error)
      alert('Failed to save template. Please try again.')
    }
  }

  const getTemplateTypeIcon = (type) => {
    switch (type) {
      case 'compact': return '📄'
      case 'detailed': return '📋'
      default: return '🧾'
    }
  }

  const getTemplateTypeDescription = (type) => {
    switch (type) {
      case 'compact': return 'Minimal receipt with essential information only'
      case 'detailed': return 'Comprehensive receipt with all available details'
      default: return 'Standard receipt with balanced information'
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading receipt templates...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.error}>
        <p>Error loading templates: {error}</p>
        <button onClick={loadTemplates} className={styles.retryBtn}>
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className={styles.receiptCustomizer}>
      <div className={styles.templatesSection}>
        <div className={styles.sectionHeader}>
          <div>
            <h3>Receipt Templates</h3>
            <p className={styles.description}>
              Choose a receipt template for your transactions. You can customize these templates in the admin settings.
            </p>
          </div>
          <button
            onClick={handleCreateTemplate}
            className={styles.createButton}
          >
            ➕ Create Template
          </button>
        </div>
        
        <div className={styles.templateGrid}>
          {templates.map((template) => (
            <div
              key={template.id}
              className={`${styles.templateCard} ${
                selectedTemplate?.id === template.id ? styles.selected : ''
              }`}
              onClick={() => handleTemplateSelect(template)}
            >
              <div className={styles.templateHeader}>
                <span className={styles.templateIcon}>
                  {getTemplateTypeIcon(template.template_type)}
                </span>
                <div className={styles.templateInfo}>
                  <h4 className={styles.templateName}>{template.name}</h4>
                  {template.is_default && (
                    <span className={styles.defaultBadge}>Default</span>
                  )}
                </div>
              </div>
              
              <p className={styles.templateDescription}>
                {template.description || getTemplateTypeDescription(template.template_type)}
              </p>
              
              <div className={styles.templateFeatures}>
                <div className={styles.featureList}>
                  {template.show_customer_details && (
                    <span className={styles.feature}>👤 Customer Details</span>
                  )}
                  {template.show_service_details && (
                    <span className={styles.feature}>🎨 Service Info</span>
                  )}
                  {template.show_artist_details && (
                    <span className={styles.feature}>✨ Artist Info</span>
                  )}
                  {template.show_payment_details && (
                    <span className={styles.feature}>💳 Payment Details</span>
                  )}
                </div>
              </div>
              
              <div className={styles.templateMeta}>
                <span className={styles.templateType}>
                  {template.template_type.charAt(0).toUpperCase() + template.template_type.slice(1)}
                </span>
                <span className={styles.businessName}>
                  {template.business_name}
                </span>
              </div>

              <div className={styles.templateActions}>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleEditTemplate(template)
                  }}
                  className={styles.actionButton}
                  title="Edit Template"
                >
                  ✏️
                </button>
                {!template.is_default && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDeleteTemplate(template)
                    }}
                    className={`${styles.actionButton} ${styles.deleteButton}`}
                    title="Delete Template"
                  >
                    🗑️
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {templates.length === 0 && (
          <div className={styles.noTemplates}>
            <p>No receipt templates found.</p>
            <p>Default templates will be created automatically.</p>
          </div>
        )}
      </div>

      {showPreview && selectedTemplate && previewData && (
        <div className={styles.previewSection}>
          <h3>Receipt Preview</h3>
          <div className={styles.previewContainer}>
            <ReceiptPreview 
              template={selectedTemplate} 
              data={previewData} 
            />
          </div>
        </div>
      )}

      {/* Template Creation/Editing Modal */}
      {(showCreateModal || showEditModal) && (
        <TemplateModal
          template={editingTemplate}
          onSave={handleSaveTemplate}
          onClose={() => {
            setShowCreateModal(false)
            setShowEditModal(false)
            setEditingTemplate(null)
          }}
        />
      )}
    </div>
  )
}

/**
 * Receipt Preview Component
 * Shows a live preview of how the receipt will look
 */
function ReceiptPreview({ template, data }) {
  const [previewHtml, setPreviewHtml] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    generatePreview()
  }, [template, data])

  const generatePreview = async () => {
    try {
      setLoading(true)
      
      // Generate preview HTML using the receipt generator
      const response = await fetch('/api/admin/receipts/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({
          templateId: template.id,
          bookingData: data
        })
      })

      if (!response.ok) {
        throw new Error('Failed to generate preview')
      }

      const result = await response.json()
      setPreviewHtml(result.html || '')
    } catch (error) {
      console.error('Error generating preview:', error)
      setPreviewHtml('<p>Preview not available</p>')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className={styles.previewLoading}>
        <div className={styles.loadingSpinner}></div>
        <p>Generating preview...</p>
      </div>
    )
  }

  return (
    <div className={styles.receiptPreview}>
      <div className={styles.previewHeader}>
        <span className={styles.previewLabel}>Preview</span>
        <button
          onClick={generatePreview}
          className={styles.refreshBtn}
          title="Refresh Preview"
        >
          🔄
        </button>
      </div>
      <div
        className={styles.previewContent}
        dangerouslySetInnerHTML={{ __html: previewHtml }}
      />
    </div>
  )
}

/**
 * Template Creation/Editing Modal
 */
function TemplateModal({ template, onSave, onClose }) {
  const [formData, setFormData] = useState({
    name: template?.name || '',
    description: template?.description || '',
    template_type: template?.template_type || 'standard',
    business_name: template?.business_name || 'Ocean Soul Sparkles',
    business_address: template?.business_address || '',
    business_phone: template?.business_phone || '',
    business_email: template?.business_email || '',
    business_website: template?.business_website || '',
    business_abn: template?.business_abn || '',
    show_logo: template?.show_logo ?? true,
    logo_position: template?.logo_position || 'center',
    header_color: template?.header_color || '#667eea',
    text_color: template?.text_color || '#333333',
    font_family: template?.font_family || 'Arial',
    font_size: template?.font_size || 12,
    show_customer_details: template?.show_customer_details ?? true,
    show_service_details: template?.show_service_details ?? true,
    show_artist_details: template?.show_artist_details ?? true,
    show_payment_details: template?.show_payment_details ?? true,
    show_booking_notes: template?.show_booking_notes ?? false,
    show_terms_conditions: template?.show_terms_conditions ?? true,
    footer_message: template?.footer_message || 'Thank you for choosing Ocean Soul Sparkles!',
    show_social_media: template?.show_social_media ?? false
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    onSave(formData)
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modal}>
        <div className={styles.modalHeader}>
          <h3>{template ? 'Edit Template' : 'Create Template'}</h3>
          <button onClick={onClose} className={styles.closeButton}>✕</button>
        </div>

        <form onSubmit={handleSubmit} className={styles.modalForm}>
          <div className={styles.formGrid}>
            <div className={styles.formSection}>
              <h4>Basic Information</h4>
              <div className={styles.formGroup}>
                <label>Template Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  required
                />
              </div>

              <div className={styles.formGroup}>
                <label>Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                  rows={3}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Template Type</label>
                <select
                  value={formData.template_type}
                  onChange={(e) => handleChange('template_type', e.target.value)}
                >
                  <option value="standard">Standard</option>
                  <option value="compact">Compact</option>
                  <option value="detailed">Detailed</option>
                </select>
              </div>
            </div>

            <div className={styles.formSection}>
              <h4>Business Information</h4>
              <div className={styles.formGroup}>
                <label>Business Name</label>
                <input
                  type="text"
                  value={formData.business_name}
                  onChange={(e) => handleChange('business_name', e.target.value)}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Address</label>
                <textarea
                  value={formData.business_address}
                  onChange={(e) => handleChange('business_address', e.target.value)}
                  rows={2}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Phone</label>
                <input
                  type="text"
                  value={formData.business_phone}
                  onChange={(e) => handleChange('business_phone', e.target.value)}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Email</label>
                <input
                  type="email"
                  value={formData.business_email}
                  onChange={(e) => handleChange('business_email', e.target.value)}
                />
              </div>
            </div>

            <div className={styles.formSection}>
              <h4>Display Options</h4>
              <div className={styles.checkboxGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.show_customer_details}
                    onChange={(e) => handleChange('show_customer_details', e.target.checked)}
                  />
                  <span>Show Customer Details</span>
                </label>

                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.show_service_details}
                    onChange={(e) => handleChange('show_service_details', e.target.checked)}
                  />
                  <span>Show Service Details</span>
                </label>

                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.show_artist_details}
                    onChange={(e) => handleChange('show_artist_details', e.target.checked)}
                  />
                  <span>Show Artist Details</span>
                </label>

                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.show_payment_details}
                    onChange={(e) => handleChange('show_payment_details', e.target.checked)}
                  />
                  <span>Show Payment Details</span>
                </label>
              </div>
            </div>
          </div>

          <div className={styles.modalActions}>
            <button type="button" onClick={onClose} className={styles.cancelButton}>
              Cancel
            </button>
            <button type="submit" className={styles.saveButton}>
              {template ? 'Update Template' : 'Create Template'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
