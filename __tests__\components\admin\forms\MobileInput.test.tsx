/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Input Tests
 * Comprehensive tests for mobile input components
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {
  MobileInput,
  MobileEmailInput,
  MobilePhoneInput,
  MobileNumberInput,
  MobilePasswordInput
} from '../../../../components/admin/forms/MobileInput';

describe('MobileInput', () => {
  const defaultProps = {
    name: 'test-input',
    value: '',
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    it('renders with basic props', () => {
      render(<MobileInput {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('name', 'test-input');
    });

    it('displays label when provided', () => {
      render(<MobileInput {...defaultProps} label="Test Label" />);
      
      const label = screen.getByText('Test Label');
      expect(label).toBeInTheDocument();
      expect(label).toHaveAttribute('for', expect.stringContaining('test-input'));
    });

    it('shows required indicator when required', () => {
      render(<MobileInput {...defaultProps} label="Test Label" required />);
      
      const requiredIndicator = screen.getByText('*');
      expect(requiredIndicator).toBeInTheDocument();
      expect(requiredIndicator).toHaveAttribute('aria-label', 'required');
    });

    it('displays placeholder text', () => {
      render(<MobileInput {...defaultProps} placeholder="Enter text here" />);
      
      const input = screen.getByPlaceholderText('Enter text here');
      expect(input).toBeInTheDocument();
    });

    it('handles value changes', async () => {
      const user = userEvent.setup();
      const onChange = jest.fn();
      
      render(<MobileInput {...defaultProps} onChange={onChange} />);
      
      const input = screen.getByRole('textbox');
      await user.type(input, 'test value');
      
      expect(onChange).toHaveBeenCalledTimes(10); // Once for each character
      expect(onChange).toHaveBeenLastCalledWith('test value');
    });
  });

  describe('Validation States', () => {
    it('displays error message when error prop is provided', () => {
      const error = { field: 'test', message: 'This field is required' };
      render(<MobileInput {...defaultProps} error={error} />);
      
      const errorMessage = screen.getByText('This field is required');
      expect(errorMessage).toBeInTheDocument();
      expect(errorMessage).toHaveAttribute('role', 'alert');
    });

    it('displays error message when error is a string', () => {
      render(<MobileInput {...defaultProps} error="Invalid input" />);
      
      const errorMessage = screen.getByText('Invalid input');
      expect(errorMessage).toBeInTheDocument();
    });

    it('applies error styling when error is present', () => {
      render(<MobileInput {...defaultProps} error="Error message" />);
      
      const container = screen.getByRole('textbox').closest('div');
      expect(container).toHaveClass('error');
    });

    it('applies success styling when success prop is true', () => {
      render(<MobileInput {...defaultProps} success />);
      
      const container = screen.getByRole('textbox').closest('div');
      expect(container).toHaveClass('success');
    });

    it('displays help text when provided', () => {
      render(<MobileInput {...defaultProps} helpText="This is help text" />);
      
      const helpText = screen.getByText('This is help text');
      expect(helpText).toBeInTheDocument();
    });

    it('hides help text when error is present', () => {
      render(
        <MobileInput 
          {...defaultProps} 
          error="Error message" 
          helpText="This is help text" 
        />
      );
      
      expect(screen.getByText('Error message')).toBeInTheDocument();
      expect(screen.queryByText('This is help text')).not.toBeInTheDocument();
    });
  });

  describe('Disabled and ReadOnly States', () => {
    it('applies disabled styling and behavior when disabled', () => {
      render(<MobileInput {...defaultProps} disabled />);
      
      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
      
      const container = input.closest('div');
      expect(container).toHaveClass('disabled');
    });

    it('applies readonly styling when readonly', () => {
      render(<MobileInput {...defaultProps} readOnly />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('readonly');
      
      const container = input.closest('div');
      expect(container).toHaveClass('readOnly');
    });
  });

  describe('Focus Management', () => {
    it('handles focus and blur events', async () => {
      const user = userEvent.setup();
      const onFocus = jest.fn();
      const onBlur = jest.fn();
      
      render(
        <MobileInput 
          {...defaultProps} 
          onFocus={onFocus} 
          onBlur={onBlur} 
        />
      );
      
      const input = screen.getByRole('textbox');
      
      await user.click(input);
      expect(onFocus).toHaveBeenCalledTimes(1);
      
      await user.tab();
      expect(onBlur).toHaveBeenCalledTimes(1);
    });

    it('applies focused styling when focused', async () => {
      const user = userEvent.setup();
      render(<MobileInput {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      const container = input.closest('div');
      
      await user.click(input);
      expect(container).toHaveClass('focused');
    });

    it('focuses input when label is clicked', async () => {
      const user = userEvent.setup();
      render(<MobileInput {...defaultProps} label="Test Label" />);
      
      const label = screen.getByText('Test Label');
      const input = screen.getByRole('textbox');
      
      await user.click(label);
      expect(input).toHaveFocus();
    });
  });

  describe('Icons and Suffixes', () => {
    it('displays icon when provided', () => {
      const icon = <span data-testid="test-icon">📧</span>;
      render(<MobileInput {...defaultProps} icon={icon} />);
      
      const iconElement = screen.getByTestId('test-icon');
      expect(iconElement).toBeInTheDocument();
    });

    it('displays suffix when provided', () => {
      const suffix = <span data-testid="test-suffix">@domain.com</span>;
      render(<MobileInput {...defaultProps} suffix={suffix} />);
      
      const suffixElement = screen.getByTestId('test-suffix');
      expect(suffixElement).toBeInTheDocument();
    });

    it('applies correct padding classes with icon and suffix', () => {
      const icon = <span>📧</span>;
      const suffix = <span>@</span>;
      
      render(<MobileInput {...defaultProps} icon={icon} suffix={suffix} />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass('withIcon');
      expect(input).toHaveClass('withSuffix');
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <MobileInput 
          {...defaultProps} 
          error="Error message"
          helpText="Help text"
        />
      );
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-invalid', 'true');
      expect(input).toHaveAttribute('aria-describedby');
    });

    it('associates error message with input', () => {
      render(<MobileInput {...defaultProps} error="Error message" />);
      
      const input = screen.getByRole('textbox');
      const errorMessage = screen.getByText('Error message');
      
      const describedBy = input.getAttribute('aria-describedby');
      expect(describedBy).toContain(errorMessage.id);
    });
  });
});

describe('Specialized Input Components', () => {
  describe('MobileEmailInput', () => {
    it('renders with email type and inputMode', () => {
      render(<MobileEmailInput name="email" value="" onChange={jest.fn()} />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'email');
      expect(input).toHaveAttribute('inputMode', 'email');
    });
  });

  describe('MobilePhoneInput', () => {
    it('renders with tel type and inputMode', () => {
      render(<MobilePhoneInput name="phone" value="" onChange={jest.fn()} />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'tel');
      expect(input).toHaveAttribute('inputMode', 'tel');
    });
  });

  describe('MobileNumberInput', () => {
    it('renders with number type and inputMode', () => {
      render(<MobileNumberInput name="number" value="" onChange={jest.fn()} />);
      
      const input = screen.getByRole('spinbutton');
      expect(input).toHaveAttribute('type', 'number');
      expect(input).toHaveAttribute('inputMode', 'numeric');
    });
  });

  describe('MobilePasswordInput', () => {
    it('renders with password type initially', () => {
      render(<MobilePasswordInput name="password" value="" onChange={jest.fn()} />);
      
      const input = screen.getByLabelText(/password/i);
      expect(input).toHaveAttribute('type', 'password');
    });

    it('toggles password visibility when toggle button is clicked', async () => {
      const user = userEvent.setup();
      render(<MobilePasswordInput name="password" value="secret" onChange={jest.fn()} />);
      
      const input = screen.getByDisplayValue('secret');
      const toggleButton = screen.getByRole('button', { name: /show password/i });
      
      expect(input).toHaveAttribute('type', 'password');
      
      await user.click(toggleButton);
      expect(input).toHaveAttribute('type', 'text');
      
      await user.click(toggleButton);
      expect(input).toHaveAttribute('type', 'password');
    });
  });
});
