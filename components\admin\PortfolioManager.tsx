import React, { useState, useEffect } from 'react';
import styles from '../../styles/admin/Portfolio.module.css';

interface PortfolioItem {
  id: string;
  artist_id: string;
  title: string;
  description?: string;
  category: string;
  image_url: string;
  thumbnail_url?: string;
  tags?: string[];
  is_featured: boolean;
  is_public: boolean;
  display_order: number;
  work_date?: string;
  customer_consent: boolean;
  created_at: string;
  updated_at: string;
}

interface Artist {
  id: string;
  name: string;
  email: string;
}

interface PortfolioManagerProps {
  artistId?: string;
  onItemAdded?: (item: PortfolioItem) => void;
  onItemUpdated?: (item: PortfolioItem) => void;
  onItemDeleted?: (itemId: string) => void;
}

const CATEGORIES = [
  { value: 'face_painting', label: 'Face Painting' },
  { value: 'hair_braiding', label: 'Hair Braiding' },
  { value: 'glitter_art', label: 'Glitter Art' },
  { value: 'body_art', label: 'Body Art' },
  { value: 'special_effects', label: 'Special Effects' }
];

export default function PortfolioManager({ 
  artistId, 
  onItemAdded, 
  onItemUpdated, 
  onItemDeleted 
}: PortfolioManagerProps) {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [artists, setArtists] = useState<Artist[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<PortfolioItem | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedArtist, setSelectedArtist] = useState<string>(artistId || 'all');

  // Form state
  const [formData, setFormData] = useState({
    artist_id: artistId || '',
    title: '',
    description: '',
    category: 'face_painting',
    image_url: '',
    thumbnail_url: '',
    tags: '',
    is_featured: false,
    is_public: true,
    work_date: '',
    customer_consent: false
  });

  useEffect(() => {
    loadPortfolioItems();
    if (!artistId) {
      loadArtists();
    }
  }, [artistId, selectedCategory, selectedArtist]);

  const loadPortfolioItems = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('adminToken');
      
      let url = '/api/admin/artists/portfolio';
      const params = new URLSearchParams();
      
      if (artistId || selectedArtist !== 'all') {
        params.append('artist_id', artistId || selectedArtist);
      }
      if (selectedCategory !== 'all') {
        params.append('category', selectedCategory);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load portfolio items');
      }

      const data = await response.json();
      setPortfolioItems(data.portfolioItems || []);
    } catch (err) {
      console.error('Error loading portfolio items:', err);
      setError('Failed to load portfolio items');
    } finally {
      setLoading(false);
    }
  };

  const loadArtists = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/artists', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load artists');
      }

      const data = await response.json();
      setArtists(data.artists || []);
    } catch (err) {
      console.error('Error loading artists:', err);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const token = localStorage.getItem('adminToken');
      const tagsArray = formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : [];
      
      const submitData = {
        ...formData,
        tags: tagsArray,
        work_date: formData.work_date || null
      };

      let url = '/api/admin/artists/portfolio';
      let method = 'POST';

      if (editingItem) {
        url = `/api/admin/artists/${editingItem.artist_id}/portfolio?item_id=${editingItem.id}`;
        method = 'PUT';
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      if (!response.ok) {
        throw new Error('Failed to save portfolio item');
      }

      const data = await response.json();
      
      if (editingItem) {
        setPortfolioItems(prev => 
          prev.map(item => item.id === editingItem.id ? data.portfolioItem : item)
        );
        onItemUpdated?.(data.portfolioItem);
        setEditingItem(null);
      } else {
        setPortfolioItems(prev => [data.portfolioItem, ...prev]);
        onItemAdded?.(data.portfolioItem);
      }

      resetForm();
      setShowAddForm(false);
    } catch (err) {
      console.error('Error saving portfolio item:', err);
      setError('Failed to save portfolio item');
    }
  };

  const handleEdit = (item: PortfolioItem) => {
    setEditingItem(item);
    setFormData({
      artist_id: item.artist_id,
      title: item.title,
      description: item.description || '',
      category: item.category,
      image_url: item.image_url,
      thumbnail_url: item.thumbnail_url || '',
      tags: item.tags?.join(', ') || '',
      is_featured: item.is_featured,
      is_public: item.is_public,
      work_date: item.work_date || '',
      customer_consent: item.customer_consent
    });
    setShowAddForm(true);
  };

  const handleDelete = async (item: PortfolioItem) => {
    if (!confirm(`Are you sure you want to delete "${item.title}"?`)) {
      return;
    }

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(
        `/api/admin/artists/${item.artist_id}/portfolio?item_id=${item.id}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to delete portfolio item');
      }

      setPortfolioItems(prev => prev.filter(i => i.id !== item.id));
      onItemDeleted?.(item.id);
    } catch (err) {
      console.error('Error deleting portfolio item:', err);
      setError('Failed to delete portfolio item');
    }
  };

  const resetForm = () => {
    setFormData({
      artist_id: artistId || '',
      title: '',
      description: '',
      category: 'face_painting',
      image_url: '',
      thumbnail_url: '',
      tags: '',
      is_featured: false,
      is_public: true,
      work_date: '',
      customer_consent: false
    });
    setEditingItem(null);
  };

  const handleCancel = () => {
    resetForm();
    setShowAddForm(false);
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading portfolio...</p>
      </div>
    );
  }

  return (
    <div className={styles.portfolioManager}>
      <div className={styles.header}>
        <h2>Portfolio Management</h2>
        <button 
          className={styles.addButton}
          onClick={() => setShowAddForm(true)}
        >
          + Add Portfolio Item
        </button>
      </div>

      {error && (
        <div className={styles.error}>
          <p>{error}</p>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      {/* Filters */}
      <div className={styles.filters}>
        {!artistId && (
          <div className={styles.filterGroup}>
            <label>Artist:</label>
            <select 
              value={selectedArtist} 
              onChange={(e) => setSelectedArtist(e.target.value)}
            >
              <option value="all">All Artists</option>
              {artists.map(artist => (
                <option key={artist.id} value={artist.id}>
                  {artist.name}
                </option>
              ))}
            </select>
          </div>
        )}
        
        <div className={styles.filterGroup}>
          <label>Category:</label>
          <select 
            value={selectedCategory} 
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <option value="all">All Categories</option>
            {CATEGORIES.map(cat => (
              <option key={cat.value} value={cat.value}>
                {cat.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <div className={styles.modalHeader}>
              <h3>{editingItem ? 'Edit Portfolio Item' : 'Add Portfolio Item'}</h3>
              <button className={styles.closeButton} onClick={handleCancel}>×</button>
            </div>
            
            <form onSubmit={handleSubmit} className={styles.form}>
              {!artistId && (
                <div className={styles.formGroup}>
                  <label>Artist *</label>
                  <select 
                    value={formData.artist_id} 
                    onChange={(e) => setFormData(prev => ({ ...prev, artist_id: e.target.value }))}
                    required
                  >
                    <option value="">Select Artist</option>
                    {artists.map(artist => (
                      <option key={artist.id} value={artist.id}>
                        {artist.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              <div className={styles.formGroup}>
                <label>Title *</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  required
                />
              </div>

              <div className={styles.formGroup}>
                <label>Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Category *</label>
                  <select 
                    value={formData.category} 
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    required
                  >
                    {CATEGORIES.map(cat => (
                      <option key={cat.value} value={cat.value}>
                        {cat.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className={styles.formGroup}>
                  <label>Work Date</label>
                  <input
                    type="date"
                    value={formData.work_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, work_date: e.target.value }))}
                  />
                </div>
              </div>

              <div className={styles.formGroup}>
                <label>Image URL *</label>
                <input
                  type="url"
                  value={formData.image_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
                  required
                />
              </div>

              <div className={styles.formGroup}>
                <label>Thumbnail URL</label>
                <input
                  type="url"
                  value={formData.thumbnail_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, thumbnail_url: e.target.value }))}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Tags (comma-separated)</label>
                <input
                  type="text"
                  value={formData.tags}
                  onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder="e.g. butterfly, colorful, glitter"
                />
              </div>

              <div className={styles.checkboxGroup}>
                <label>
                  <input
                    type="checkbox"
                    checked={formData.is_featured}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_featured: e.target.checked }))}
                  />
                  Featured Item
                </label>

                <label>
                  <input
                    type="checkbox"
                    checked={formData.is_public}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_public: e.target.checked }))}
                  />
                  Public (visible to customers)
                </label>

                <label>
                  <input
                    type="checkbox"
                    checked={formData.customer_consent}
                    onChange={(e) => setFormData(prev => ({ ...prev, customer_consent: e.target.checked }))}
                  />
                  Customer Consent Obtained
                </label>
              </div>

              <div className={styles.formActions}>
                <button type="button" onClick={handleCancel} className={styles.cancelButton}>
                  Cancel
                </button>
                <button type="submit" className={styles.submitButton}>
                  {editingItem ? 'Update' : 'Add'} Portfolio Item
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Portfolio Grid */}
      <div className={styles.portfolioGrid}>
        {portfolioItems.length === 0 ? (
          <div className={styles.emptyState}>
            <p>No portfolio items found.</p>
            <button onClick={() => setShowAddForm(true)}>
              Add your first portfolio item
            </button>
          </div>
        ) : (
          portfolioItems.map(item => (
            <div key={item.id} className={styles.portfolioCard}>
              <div className={styles.imageContainer}>
                <img 
                  src={item.thumbnail_url || item.image_url} 
                  alt={item.title}
                  className={styles.portfolioImage}
                />
                {item.is_featured && (
                  <div className={styles.featuredBadge}>⭐ Featured</div>
                )}
                {!item.is_public && (
                  <div className={styles.privateBadge}>🔒 Private</div>
                )}
              </div>
              
              <div className={styles.cardContent}>
                <h3>{item.title}</h3>
                <p className={styles.category}>{CATEGORIES.find(c => c.value === item.category)?.label}</p>
                {item.description && (
                  <p className={styles.description}>{item.description}</p>
                )}
                
                {item.tags && item.tags.length > 0 && (
                  <div className={styles.tags}>
                    {item.tags.map(tag => (
                      <span key={tag} className={styles.tag}>{tag}</span>
                    ))}
                  </div>
                )}
                
                <div className={styles.cardActions}>
                  <button 
                    onClick={() => handleEdit(item)}
                    className={styles.editButton}
                  >
                    Edit
                  </button>
                  <button 
                    onClick={() => handleDelete(item)}
                    className={styles.deleteButton}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
