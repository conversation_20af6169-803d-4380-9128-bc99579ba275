/**
 * Ocean Soul Sparkles Admin Dashboard - Backup Management Dashboard
 * Comprehensive backup monitoring and management interface
 */

import React, { useState, useEffect } from 'react';
import styles from '../../styles/admin/BackupDashboard.module.css';

interface BackupStatus {
  pitr_enabled: boolean;
  last_backup: {
    date: string;
    status: string;
    size_mb: number;
    duration_seconds: number;
  } | null;
  backup_health: {
    success_rate_7d: number;
    total_backups_7d: number;
    failed_backups_7d: number;
    avg_duration_7d: number;
  };
  storage_info: {
    total_size_mb: number;
    retention_days: number;
    estimated_cost_monthly: number;
  };
  data_integrity: {
    last_verified: string;
    tables_verified: number;
    total_rows: number;
    status: 'healthy' | 'warning' | 'error';
  };
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: string;
  }>;
}

interface VerificationResult {
  verification_id: string;
  timestamp: string;
  overall_status: 'healthy' | 'warning' | 'error';
  tables_checked: number;
  total_rows: number;
  total_size: string;
  table_details: Array<{
    table_name: string;
    row_count: number;
    last_updated: string;
    size: string;
    status: 'healthy' | 'warning' | 'error';
    issues: string[];
  }>;
  recommendations: string[];
}

const BackupDashboard: React.FC = () => {
  const [backupStatus, setBackupStatus] = useState<BackupStatus | null>(null);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(false);

  useEffect(() => {
    fetchBackupStatus();
    const interval = setInterval(fetchBackupStatus, 60000); // Refresh every minute
    return () => clearInterval(interval);
  }, []);

  const fetchBackupStatus = async () => {
    try {
      const response = await fetch('/api/admin/backup/status');
      if (response.ok) {
        const result = await response.json();
        setBackupStatus(result.data);
      } else {
        console.error('Failed to fetch backup status');
      }
    } catch (error) {
      console.error('Error fetching backup status:', error);
    } finally {
      setLoading(false);
    }
  };

  const runVerification = async () => {
    setVerifying(true);
    try {
      const response = await fetch('/api/admin/backup/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const result = await response.json();
        setVerificationResult(result.data);
      } else {
        console.error('Verification failed');
      }
    } catch (error) {
      console.error('Error running verification:', error);
    } finally {
      setVerifying(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return '#27ae60';
      case 'warning': return '#f39c12';
      case 'error': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatTimestamp = (timestamp: string): string => {
    return new Date(timestamp).toLocaleString();
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>Loading backup status...</div>
      </div>
    );
  }

  if (!backupStatus) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>Failed to load backup status</div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Backup & Recovery Management</h2>
        <div className={styles.headerActions}>
          <button 
            onClick={runVerification} 
            disabled={verifying}
            className={styles.verifyButton}
          >
            {verifying ? 'Verifying...' : 'Run Verification'}
          </button>
          <button onClick={fetchBackupStatus} className={styles.refreshButton}>
            Refresh
          </button>
        </div>
      </div>

      {/* Critical Alerts */}
      {backupStatus.alerts.length > 0 && (
        <div className={styles.alertsSection}>
          <h3>🚨 Backup Alerts</h3>
          <div className={styles.alertsList}>
            {backupStatus.alerts.map((alert, index) => (
              <div key={index} className={`${styles.alert} ${styles[alert.type]}`}>
                <div className={styles.alertMessage}>{alert.message}</div>
                <div className={styles.alertTime}>{formatTimestamp(alert.timestamp)}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Status Overview */}
      <div className={styles.statusGrid}>
        <div className={styles.statusCard}>
          <h3>PITR Status</h3>
          <div className={`${styles.statusValue} ${backupStatus.pitr_enabled ? styles.enabled : styles.disabled}`}>
            {backupStatus.pitr_enabled ? '✅ Enabled' : '❌ Disabled'}
          </div>
          <div className={styles.statusDescription}>
            Point-in-Time Recovery
          </div>
        </div>

        <div className={styles.statusCard}>
          <h3>Last Backup</h3>
          <div className={styles.statusValue}>
            {backupStatus.last_backup ? (
              <>
                <div>{formatTimestamp(backupStatus.last_backup.date)}</div>
                <div className={styles.backupDetails}>
                  Status: {backupStatus.last_backup.status} | 
                  Size: {backupStatus.last_backup.size_mb}MB | 
                  Duration: {formatDuration(backupStatus.last_backup.duration_seconds)}
                </div>
              </>
            ) : (
              <div className={styles.noBackup}>No backups found</div>
            )}
          </div>
        </div>

        <div className={styles.statusCard}>
          <h3>7-Day Health</h3>
          <div className={styles.statusValue}>
            <div className={styles.successRate}>
              {backupStatus.backup_health.success_rate_7d}% Success Rate
            </div>
            <div className={styles.healthDetails}>
              {backupStatus.backup_health.total_backups_7d} total | 
              {backupStatus.backup_health.failed_backups_7d} failed | 
              Avg: {formatDuration(backupStatus.backup_health.avg_duration_7d)}
            </div>
          </div>
        </div>

        <div className={styles.statusCard}>
          <h3>Data Integrity</h3>
          <div className={styles.statusValue}>
            <div 
              className={styles.integrityStatus}
              style={{ color: getStatusColor(backupStatus.data_integrity.status) }}
            >
              {backupStatus.data_integrity.status.toUpperCase()}
            </div>
            <div className={styles.integrityDetails}>
              {backupStatus.data_integrity.tables_verified} tables | 
              {backupStatus.data_integrity.total_rows.toLocaleString()} rows
            </div>
          </div>
        </div>
      </div>

      {/* Storage Information */}
      <div className={styles.section}>
        <h3>Storage & Cost Information</h3>
        <div className={styles.storageInfo}>
          <div className={styles.storageItem}>
            <span className={styles.storageLabel}>Total Backup Size:</span>
            <span className={styles.storageValue}>{backupStatus.storage_info.total_size_mb} MB</span>
          </div>
          <div className={styles.storageItem}>
            <span className={styles.storageLabel}>Retention Period:</span>
            <span className={styles.storageValue}>{backupStatus.storage_info.retention_days} days</span>
          </div>
          <div className={styles.storageItem}>
            <span className={styles.storageLabel}>Estimated Monthly Cost:</span>
            <span className={styles.storageValue}>${backupStatus.storage_info.estimated_cost_monthly}</span>
          </div>
        </div>
      </div>

      {/* Verification Results */}
      {verificationResult && (
        <div className={styles.section}>
          <h3>Latest Verification Results</h3>
          <div className={styles.verificationHeader}>
            <div className={styles.verificationInfo}>
              <span>Verification ID: {verificationResult.verification_id}</span>
              <span>Completed: {formatTimestamp(verificationResult.timestamp)}</span>
              <span 
                className={styles.verificationStatus}
                style={{ color: getStatusColor(verificationResult.overall_status) }}
              >
                Status: {verificationResult.overall_status.toUpperCase()}
              </span>
            </div>
          </div>

          <div className={styles.verificationSummary}>
            <div className={styles.summaryItem}>
              <span>Tables Checked:</span>
              <span>{verificationResult.tables_checked}</span>
            </div>
            <div className={styles.summaryItem}>
              <span>Total Rows:</span>
              <span>{verificationResult.total_rows.toLocaleString()}</span>
            </div>
            <div className={styles.summaryItem}>
              <span>Total Size:</span>
              <span>{verificationResult.total_size}</span>
            </div>
          </div>

          {/* Table Details */}
          <div className={styles.tableDetails}>
            <h4>Table Status Details</h4>
            <div className={styles.tableList}>
              {verificationResult.table_details.map((table, index) => (
                <div key={index} className={styles.tableItem}>
                  <div className={styles.tableName}>
                    {table.table_name}
                    <span 
                      className={styles.tableStatus}
                      style={{ color: getStatusColor(table.status) }}
                    >
                      {table.status}
                    </span>
                  </div>
                  <div className={styles.tableInfo}>
                    <span>Rows: {table.row_count.toLocaleString()}</span>
                    <span>Size: {table.size}</span>
                    <span>Last Updated: {table.last_updated}</span>
                  </div>
                  {table.issues.length > 0 && (
                    <div className={styles.tableIssues}>
                      {table.issues.map((issue, issueIndex) => (
                        <div key={issueIndex} className={styles.issue}>⚠️ {issue}</div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Recommendations */}
          {verificationResult.recommendations.length > 0 && (
            <div className={styles.recommendations}>
              <h4>Recommendations</h4>
              <ul className={styles.recommendationsList}>
                {verificationResult.recommendations.map((recommendation, index) => (
                  <li key={index} className={styles.recommendation}>
                    {recommendation}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Quick Actions */}
      <div className={styles.section}>
        <h3>Quick Actions</h3>
        <div className={styles.quickActions}>
          <button className={styles.actionButton} disabled>
            Enable PITR (Manual Setup Required)
          </button>
          <button className={styles.actionButton} disabled>
            Configure Daily Backups (Manual Setup Required)
          </button>
          <button className={styles.actionButton} onClick={runVerification}>
            Run Data Integrity Check
          </button>
          <button className={styles.actionButton} disabled>
            Test Recovery Procedure (Manual Process)
          </button>
        </div>
      </div>

      {/* Implementation Status */}
      <div className={styles.section}>
        <h3>Implementation Status</h3>
        <div className={styles.implementationStatus}>
          <div className={styles.statusItem}>
            <span className={styles.statusIcon}>❌</span>
            <span>Point-in-Time Recovery (PITR) - Not Configured</span>
          </div>
          <div className={styles.statusItem}>
            <span className={styles.statusIcon}>❌</span>
            <span>Automated Daily Backups - Not Configured</span>
          </div>
          <div className={styles.statusItem}>
            <span className={styles.statusIcon}>✅</span>
            <span>Backup Monitoring System - Active</span>
          </div>
          <div className={styles.statusItem}>
            <span className={styles.statusIcon}>✅</span>
            <span>Data Integrity Verification - Available</span>
          </div>
          <div className={styles.statusItem}>
            <span className={styles.statusIcon}>⏳</span>
            <span>Recovery Testing - Pending</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BackupDashboard;
