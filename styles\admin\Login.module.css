/* Admin Login Page Styles */
.loginContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.loginCard {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 450px;
  margin: 20px;
  position: relative;
  z-index: 2;
}

.logoSection {
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  color: white;
  padding: 40px 30px;
  text-align: center;
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
  font-weight: 500;
}

.formSection {
  padding: 40px 30px;
}

.securityNotice {
  background: #f8f9fa;
  padding: 20px 30px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-top: 1px solid #e9ecef;
}

.securityIcon {
  font-size: 1.2rem;
  color: #28a745;
}

.securityText {
  flex: 1;
}

.securityText p {
  margin: 0;
  font-size: 0.85rem;
  color: #6c757d;
  line-height: 1.4;
}

.securityText p:first-child {
  font-weight: 600;
  color: #495057;
}

.backgroundPattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: sparkleFloat 6s linear infinite;
}

.sparkle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.sparkle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: 1s;
}

.sparkle:nth-child(3) {
  top: 80%;
  left: 20%;
  animation-delay: 2s;
}

.sparkle:nth-child(4) {
  top: 30%;
  left: 70%;
  animation-delay: 3s;
}

.sparkle:nth-child(5) {
  top: 70%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes sparkleFloat {
  0%, 100% {
    opacity: 0;
    transform: translateY(0) scale(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-20px) scale(1);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .loginContainer {
    padding: 16px;
  }

  .loginCard {
    margin: 0;
    border-radius: 16px;
  }

  .logoSection {
    padding: 30px 20px;
  }

  .logo h1 {
    font-size: 1.6rem;
  }

  .formSection {
    padding: 30px 20px;
  }

  .securityNotice {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .loginCard {
    border-radius: 12px;
  }

  .logoSection {
    padding: 24px 16px;
  }

  .logo h1 {
    font-size: 1.4rem;
  }

  .formSection {
    padding: 24px 16px;
  }

  .securityNotice {
    padding: 12px 16px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}
