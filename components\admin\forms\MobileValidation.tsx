/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Validation Component
 * Mobile-friendly validation feedback and form validation utilities
 */

import React, { useState, useEffect } from 'react';
import { ValidationError } from '@/types';
import styles from './MobileValidation.module.css';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
  email?: boolean;
  phone?: boolean;
  url?: boolean;
  number?: boolean;
  min?: number;
  max?: number;
}

export interface MobileValidationProps {
  errors: ValidationError[];
  className?: string;
  showSummary?: boolean;
  'data-testid'?: string;
}

export interface FormValidationState {
  [fieldName: string]: {
    value: any;
    errors: ValidationError[];
    touched: boolean;
    valid: boolean;
  };
}

// Validation feedback component
export const MobileValidation: React.FC<MobileValidationProps> = ({
  errors,
  className = '',
  showSummary = true,
  'data-testid': testId,
}) => {
  if (!errors || errors.length === 0) {
    return null;
  }

  const containerClasses = [
    styles.container,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses} data-testid={testId} role="alert" aria-live="polite">
      {showSummary && errors.length > 1 && (
        <div className={styles.summary}>
          <div className={styles.summaryIcon}>⚠️</div>
          <div className={styles.summaryText}>
            Please fix {errors.length} error{errors.length > 1 ? 's' : ''} below:
          </div>
        </div>
      )}
      
      <div className={styles.errorList}>
        {errors.map((error, index) => (
          <div key={index} className={styles.errorItem}>
            <div className={styles.errorIcon}>❌</div>
            <div className={styles.errorContent}>
              {error.field && (
                <div className={styles.errorField}>{error.field}:</div>
              )}
              <div className={styles.errorMessage}>{error.message}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Success feedback component
export const MobileSuccess: React.FC<{
  message: string;
  className?: string;
  'data-testid'?: string;
}> = ({ message, className = '', 'data-testid': testId }) => {
  return (
    <div 
      className={[styles.success, className].filter(Boolean).join(' ')}
      data-testid={testId}
      role="status"
      aria-live="polite"
    >
      <div className={styles.successIcon}>✅</div>
      <div className={styles.successMessage}>{message}</div>
    </div>
  );
};

// Warning feedback component
export const MobileWarning: React.FC<{
  message: string;
  className?: string;
  'data-testid'?: string;
}> = ({ message, className = '', 'data-testid': testId }) => {
  return (
    <div 
      className={[styles.warning, className].filter(Boolean).join(' ')}
      data-testid={testId}
      role="alert"
      aria-live="polite"
    >
      <div className={styles.warningIcon}>⚠️</div>
      <div className={styles.warningMessage}>{message}</div>
    </div>
  );
};

// Validation utilities
export const validateField = (value: any, rules: ValidationRule): ValidationError[] => {
  const errors: ValidationError[] = [];

  // Required validation
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    errors.push({
      field: '',
      message: 'This field is required'
    });
    return errors; // Don't validate further if required and empty
  }

  // Skip other validations if value is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return errors;
  }

  const stringValue = String(value);

  // Length validations
  if (rules.minLength && stringValue.length < rules.minLength) {
    errors.push({
      field: '',
      message: `Must be at least ${rules.minLength} characters long`
    });
  }

  if (rules.maxLength && stringValue.length > rules.maxLength) {
    errors.push({
      field: '',
      message: `Must be no more than ${rules.maxLength} characters long`
    });
  }

  // Pattern validation
  if (rules.pattern && !rules.pattern.test(stringValue)) {
    errors.push({
      field: '',
      message: 'Invalid format'
    });
  }

  // Email validation
  if (rules.email) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(stringValue)) {
      errors.push({
        field: '',
        message: 'Please enter a valid email address'
      });
    }
  }

  // Phone validation
  if (rules.phone) {
    const phonePattern = /^\+?[\d\s\-\(\)]{10,}$/;
    if (!phonePattern.test(stringValue)) {
      errors.push({
        field: '',
        message: 'Please enter a valid phone number'
      });
    }
  }

  // URL validation
  if (rules.url) {
    try {
      new URL(stringValue);
    } catch {
      errors.push({
        field: '',
        message: 'Please enter a valid URL'
      });
    }
  }

  // Number validations
  if (rules.number) {
    const numValue = Number(value);
    if (isNaN(numValue)) {
      errors.push({
        field: '',
        message: 'Must be a valid number'
      });
    } else {
      if (rules.min !== undefined && numValue < rules.min) {
        errors.push({
          field: '',
          message: `Must be at least ${rules.min}`
        });
      }

      if (rules.max !== undefined && numValue > rules.max) {
        errors.push({
          field: '',
          message: `Must be no more than ${rules.max}`
        });
      }
    }
  }

  // Custom validation
  if (rules.custom) {
    const customError = rules.custom(value);
    if (customError) {
      errors.push({
        field: '',
        message: customError
      });
    }
  }

  return errors;
};

// Form validation hook
export const useMobileFormValidation = (
  initialValues: Record<string, any>,
  validationRules: Record<string, ValidationRule>
) => {
  const [formState, setFormState] = useState<FormValidationState>(() => {
    const state: FormValidationState = {};
    Object.keys(initialValues).forEach(field => {
      state[field] = {
        value: initialValues[field],
        errors: [],
        touched: false,
        valid: true
      };
    });
    return state;
  });

  const validateForm = () => {
    const newState = { ...formState };
    let isFormValid = true;

    Object.keys(validationRules).forEach(field => {
      const value = newState[field]?.value;
      const rules = validationRules[field];
      const errors = validateField(value, rules);
      
      newState[field] = {
        ...newState[field],
        errors,
        valid: errors.length === 0
      };

      if (errors.length > 0) {
        isFormValid = false;
      }
    });

    setFormState(newState);
    return isFormValid;
  };

  const updateField = (field: string, value: any, shouldValidate = true) => {
    const rules = validationRules[field];
    const errors = shouldValidate && rules ? validateField(value, rules) : [];

    setFormState(prev => ({
      ...prev,
      [field]: {
        value,
        errors,
        touched: true,
        valid: errors.length === 0
      }
    }));
  };

  const touchField = (field: string) => {
    setFormState(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        touched: true
      }
    }));
  };

  const resetForm = () => {
    const state: FormValidationState = {};
    Object.keys(initialValues).forEach(field => {
      state[field] = {
        value: initialValues[field],
        errors: [],
        touched: false,
        valid: true
      };
    });
    setFormState(state);
  };

  const getFieldProps = (field: string) => ({
    value: formState[field]?.value || '',
    onChange: (value: any) => updateField(field, value),
    onBlur: () => touchField(field),
    error: formState[field]?.touched ? formState[field]?.errors[0] : undefined
  });

  const getAllErrors = (): ValidationError[] => {
    const allErrors: ValidationError[] = [];
    Object.entries(formState).forEach(([field, state]) => {
      if (state.touched && state.errors.length > 0) {
        state.errors.forEach(error => {
          allErrors.push({
            ...error,
            field: field
          });
        });
      }
    });
    return allErrors;
  };

  const isFormValid = Object.values(formState).every(field => field.valid);
  const hasErrors = Object.values(formState).some(field => field.touched && field.errors.length > 0);

  return {
    formState,
    updateField,
    touchField,
    validateForm,
    resetForm,
    getFieldProps,
    getAllErrors,
    isFormValid,
    hasErrors
  };
};

export default MobileValidation;
