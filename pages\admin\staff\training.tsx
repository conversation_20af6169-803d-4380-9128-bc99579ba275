import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../../hooks/useAuth';
import AdminLayout from '../../../components/admin/AdminLayout';
import styles from '../../../styles/admin/StaffTraining.module.css';

interface TrainingModule {
  id: string;
  name: string;
  description: string;
  category: string;
  is_required: boolean;
  duration_minutes: number;
  passing_score: number;
}

interface TrainingProgress {
  id: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'failed';
  started_at?: string;
  completed_at?: string;
  score?: number;
  attempts: number;
  notes?: string;
  assigned_at: string;
  staff_training_modules: TrainingModule;
}

interface TrainingStatistics {
  total: number;
  completed: number;
  required: number;
  completedRequired: number;
  completionPercentage: number;
  requiredCompletionPercentage: number;
}

const STATUS_COLORS = {
  not_started: '#6b7280',
  in_progress: '#f59e0b',
  completed: '#10b981',
  failed: '#ef4444'
};

const STATUS_LABELS = {
  not_started: 'Not Started',
  in_progress: 'In Progress',
  completed: 'Completed',
  failed: 'Failed'
};

const CATEGORY_LABELS = {
  safety: 'Health & Safety',
  technical: 'Technical Skills',
  customer_service: 'Customer Service',
  compliance: 'Compliance'
};

export default function StaffTrainingPage() {
  const { user } = useAuth();
  const router = useRouter();
  const { staff_id } = router.query;
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<TrainingProgress[]>([]);
  const [availableModules, setAvailableModules] = useState<TrainingModule[]>([]);
  const [statistics, setStatistics] = useState<TrainingStatistics>({
    total: 0,
    completed: 0,
    required: 0,
    completedRequired: 0,
    completionPercentage: 0,
    requiredCompletionPercentage: 0
  });
  const [staffInfo, setStaffInfo] = useState<any>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showAssignModal, setShowAssignModal] = useState(false);

  useEffect(() => {
    if (staff_id) {
      loadTrainingData();
      loadStaffInfo();
      loadAvailableModules();
    }
  }, [staff_id]);

  const loadTrainingData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/staff/training?staff_id=${staff_id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProgress(data.progress || []);
        setStatistics(data.statistics || {});
      } else {
        setError('Failed to load training data');
      }
    } catch (error) {
      console.error('Error loading training data:', error);
      setError('Failed to load training data');
    } finally {
      setLoading(false);
    }
  };

  const loadStaffInfo = async () => {
    try {
      const response = await fetch(`/api/admin/staff?id=${staff_id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStaffInfo(data.staff);
      }
    } catch (error) {
      console.error('Error loading staff info:', error);
    }
  };

  const loadAvailableModules = async () => {
    try {
      const response = await fetch('/api/admin/staff/training', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAvailableModules(data.modules || []);
      }
    } catch (error) {
      console.error('Error loading available modules:', error);
    }
  };

  const assignAllRequired = async () => {
    try {
      const response = await fetch('/api/admin/staff/training', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({
          action: 'assign_all_required',
          staff_id
        })
      });

      if (response.ok) {
        await loadTrainingData();
      } else {
        setError('Failed to assign required modules');
      }
    } catch (error) {
      console.error('Error assigning required modules:', error);
      setError('Failed to assign required modules');
    }
  };

  const assignModule = async (moduleId: string) => {
    try {
      const response = await fetch('/api/admin/staff/training', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({
          action: 'assign_module',
          staff_id,
          module_id: moduleId
        })
      });

      if (response.ok) {
        await loadTrainingData();
        setShowAssignModal(false);
      } else {
        setError('Failed to assign module');
      }
    } catch (error) {
      console.error('Error assigning module:', error);
      setError('Failed to assign module');
    }
  };

  const updateTrainingStatus = async (moduleId: string, action: string, score?: number) => {
    try {
      const response = await fetch('/api/admin/staff/training', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({
          action,
          staff_id,
          module_id: moduleId,
          score
        })
      });

      if (response.ok) {
        await loadTrainingData();
      } else {
        setError(`Failed to ${action.replace('_', ' ')}`);
      }
    } catch (error) {
      console.error(`Error ${action}:`, error);
      setError(`Failed to ${action.replace('_', ' ')}`);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return '#10b981';
    if (percentage >= 70) return '#f59e0b';
    return '#ef4444';
  };

  const filteredProgress = selectedCategory === 'all' 
    ? progress 
    : progress.filter(p => p.staff_training_modules.category === selectedCategory);

  const assignedModuleIds = progress.map(p => p.staff_training_modules.id);
  const unassignedModules = availableModules.filter(m => !assignedModuleIds.includes(m.id));

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading training data...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Staff Training | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage staff training progress and modules" />
      </Head>

      <div className={styles.trainingContainer}>
        <header className={styles.header}>
          <div className={styles.headerLeft}>
            <h1 className={styles.title}>Staff Training</h1>
            {staffInfo && (
              <p className={styles.subtitle}>
                {staffInfo.firstName} {staffInfo.lastName} - {staffInfo.role}
              </p>
            )}
          </div>
          <div className={styles.headerActions}>
            <button
              onClick={assignAllRequired}
              className={styles.assignBtn}
            >
              Assign Required Modules
            </button>
            <button
              onClick={() => setShowAssignModal(true)}
              className={styles.assignBtn}
            >
              + Assign Module
            </button>
            <button
              onClick={() => router.back()}
              className={styles.backBtn}
            >
              ← Back to Staff
            </button>
          </div>
        </header>

        {error && (
          <div className={styles.errorMessage}>
            {error}
            <button onClick={() => setError(null)} className={styles.closeError}>×</button>
          </div>
        )}

        <div className={styles.progressSection}>
          <div className={styles.progressCard}>
            <h3>Overall Progress</h3>
            <div className={styles.progressBar}>
              <div 
                className={styles.progressFill}
                style={{ 
                  width: `${statistics.completionPercentage}%`,
                  backgroundColor: getProgressColor(statistics.completionPercentage)
                }}
              ></div>
            </div>
            <div className={styles.progressStats}>
              <span>{statistics.completed} of {statistics.total} completed</span>
              <span>{statistics.completionPercentage}%</span>
            </div>
          </div>

          <div className={styles.progressCard}>
            <h3>Required Modules</h3>
            <div className={styles.progressBar}>
              <div 
                className={styles.progressFill}
                style={{ 
                  width: `${statistics.requiredCompletionPercentage}%`,
                  backgroundColor: getProgressColor(statistics.requiredCompletionPercentage)
                }}
              ></div>
            </div>
            <div className={styles.progressStats}>
              <span>{statistics.completedRequired} of {statistics.required} completed</span>
              <span>{statistics.requiredCompletionPercentage}%</span>
            </div>
          </div>
        </div>

        <div className={styles.filters}>
          <div className={styles.filterGroup}>
            <label htmlFor="categoryFilter">Filter by Category:</label>
            <select
              id="categoryFilter"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className={styles.filterSelect}
            >
              <option value="all">All Categories</option>
              {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
                <option key={key} value={key}>{label}</option>
              ))}
            </select>
          </div>
        </div>

        <div className={styles.trainingContent}>
          {filteredProgress.length === 0 ? (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>🎓</div>
              <h3>No Training Modules Assigned</h3>
              <p>This staff member doesn't have any training modules assigned yet.</p>
              <button
                onClick={assignAllRequired}
                className={styles.assignBtn}
              >
                Assign Required Modules
              </button>
            </div>
          ) : (
            <div className={styles.modulesList}>
              {filteredProgress.map((item) => (
                <div key={item.id} className={styles.moduleCard}>
                  <div className={styles.cardHeader}>
                    <div className={styles.moduleInfo}>
                      <h3 className={styles.moduleName}>
                        {item.staff_training_modules.name}
                        {item.staff_training_modules.is_required && (
                          <span className={styles.requiredBadge}>Required</span>
                        )}
                      </h3>
                      <p className={styles.moduleDescription}>
                        {item.staff_training_modules.description}
                      </p>
                      <div className={styles.moduleDetails}>
                        <span>Category: {CATEGORY_LABELS[item.staff_training_modules.category as keyof typeof CATEGORY_LABELS]}</span>
                        <span>Duration: {item.staff_training_modules.duration_minutes} minutes</span>
                        <span>Passing Score: {item.staff_training_modules.passing_score}%</span>
                      </div>
                    </div>
                    <div className={styles.statusSection}>
                      <span 
                        className={styles.statusBadge}
                        style={{ backgroundColor: STATUS_COLORS[item.status] }}
                      >
                        {STATUS_LABELS[item.status]}
                      </span>
                      {item.score && (
                        <div className={styles.scoreDisplay}>
                          Score: {item.score}%
                        </div>
                      )}
                    </div>
                  </div>

                  <div className={styles.cardBody}>
                    <div className={styles.progressInfo}>
                      <p>Assigned: {formatDate(item.assigned_at)}</p>
                      {item.started_at && (
                        <p>Started: {formatDate(item.started_at)}</p>
                      )}
                      {item.completed_at && (
                        <p>Completed: {formatDate(item.completed_at)}</p>
                      )}
                      {item.attempts > 0 && (
                        <p>Attempts: {item.attempts}</p>
                      )}
                    </div>

                    <div className={styles.cardActions}>
                      {item.status === 'not_started' && (
                        <button
                          onClick={() => updateTrainingStatus(item.staff_training_modules.id, 'start_training')}
                          className={styles.startBtn}
                        >
                          Start Training
                        </button>
                      )}
                      {(item.status === 'in_progress' || item.status === 'failed') && (
                        <button
                          onClick={() => {
                            const score = prompt('Enter completion score (0-100):');
                            if (score && !isNaN(Number(score))) {
                              updateTrainingStatus(item.staff_training_modules.id, 'complete_training', Number(score));
                            }
                          }}
                          className={styles.completeBtn}
                        >
                          Complete Training
                        </button>
                      )}
                    </div>
                  </div>

                  {item.notes && (
                    <div className={styles.moduleNotes}>
                      <strong>Notes:</strong> {item.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Assign Module Modal */}
        {showAssignModal && (
          <div className={styles.modalOverlay}>
            <div className={styles.modal}>
              <div className={styles.modalHeader}>
                <h3>Assign Training Module</h3>
                <button
                  onClick={() => setShowAssignModal(false)}
                  className={styles.closeModal}
                >
                  ×
                </button>
              </div>
              <div className={styles.modalBody}>
                {unassignedModules.length === 0 ? (
                  <p>All available modules are already assigned to this staff member.</p>
                ) : (
                  <div className={styles.moduleOptions}>
                    {unassignedModules.map((module) => (
                      <div key={module.id} className={styles.moduleOption}>
                        <div className={styles.optionInfo}>
                          <h4>{module.name}</h4>
                          <p>{module.description}</p>
                          <div className={styles.optionDetails}>
                            <span>{CATEGORY_LABELS[module.category as keyof typeof CATEGORY_LABELS]}</span>
                            <span>{module.duration_minutes} min</span>
                            {module.is_required && <span className={styles.requiredBadge}>Required</span>}
                          </div>
                        </div>
                        <button
                          onClick={() => assignModule(module.id)}
                          className={styles.assignOptionBtn}
                        >
                          Assign
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
